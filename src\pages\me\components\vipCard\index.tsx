import { Image } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import { cards, CardType } from './index.config';
import './index.scss';

interface Props {
  type: CardType;
}
export default function VipCard({ type }: Props) {
  const url = cards[type];
  return (
    <View className="vip-card">
      <Image src={url} className="banner" svg mode="widthFix" lazyLoad />
    </View>
  );
}
