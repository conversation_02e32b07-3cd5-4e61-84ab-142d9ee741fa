import useUploadFile from '@/hooks/use-upload-file';
import SvgIcon from '@/pages/me/components/SvgIcon';
import { addFeedback, getFeedbackType } from '@/serves/feedback';
import { FeedbackAppDTO } from '@/serves/feedback/interface';
import { Plus } from '@nutui/icons-react-taro';
import { Button, Form, Input, Uploader, UploaderFileItem } from '@nutui/nutui-react-taro';
import { Image, Text, Textarea, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { CSSProperties, useEffect, useState } from 'react';
import './index.scss';

const Feedback = () => {
  const [feedbackType, setFeedbackType] = useState<
    {
      id: string;
      name: string;
    }[]
  >([]);
  const [selectedType, setSelectedType] = useState<string>('');
  const [uploadImgs, setUploadImgs] = useState<string[]>([]);
  const [problem, setProblem] = useState('');
  const [contact, setContact] = useState('');
  const { uploadFile } = useUploadFile();
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  const [previewCurrent, setPreviewCurrent] = useState<string>('');
  const [fileList, setFileList] = useState<UploaderFileItem[]>([]);

  useEffect(() => {
    getFeedbackType().then((res) => {
      if (res.data) {
        Taro.setStorage({ key: 'feedbackType', data: res.data });
        setFeedbackType(res.data.map((item) => ({ id: item.value, name: item.label })));
        setSelectedType(res.data[0].value);
      }
    });
  }, []);

  const handleSubmit = () => {
    if (!problem.trim()) {
      Taro.showToast({
        title: '请输入问题描述',
        icon: 'none',
      });
      return;
    } else if (!contact.trim()) {
      Taro.showToast({
        title: '请输入联系方式',
        icon: 'none',
      });
    }
    const data: FeedbackAppDTO = {
      type: selectedType,
      problem,
      contact,
      attachments: uploadImgs,
    };
    console.log('upload data', data);
    addFeedback(data).then((res) => {
      if (res.data) {
        Taro.showToast({
          title: '反馈成功',
          icon: 'success',
          success: () => {
            setTimeout(() => {
              Taro.navigateBack();
            }, 1000);
          },
        });
      } else {
        Taro.showToast({
          title: '反馈失败',
          icon: 'none',
        });
      }
    });
  };

  const onUpload = async (file: any): Promise<UploaderFileItem> => {
    const { url } = await uploadFile(undefined, [file] as Taro.chooseMedia.ChooseMedia[]);
    console.log('upload image url', url);

    if (url) {
      setUploadImgs((prev) => [...prev, url]);
      return {
        name: file.tempFilePath.split('/').pop() || '',
        url: url,
        type: file.fileType as 'image' | 'list',
        status: 'success',
      };
    } else {
      return {
        name: file.tempFilePath.split('/').pop(),
        url: file.tempFilePath,
        type: file.fileType as 'image' | 'list',
        status: 'error',
      };
    }
  };

  // 处理删除图片
  const handleDelete = (file: UploaderFileItem, files: UploaderFileItem[]) => {
    // 从uploadImgs数组中移除被删除的图片URL
    if (file.url) {
      setUploadImgs((prev) => prev.filter((url) => url !== file.url));
      setFileList(files);
    }
  };

  // 处理图片点击预览
  const onImageClick = (file: UploaderFileItem, index: number) => {
    const currentUrl = file.url || '';

    if (currentUrl && uploadImgs.length > 0) {
      // 使用自定义预览
      setPreviewCurrent(currentUrl);
      setPreviewVisible(true);
    }
  };

  // 关闭预览
  const closePreview = () => {
    setPreviewVisible(false);
  };

  // 删除预览中的图片
  const deletePreviewImage = () => {
    if (previewCurrent) {
      // 从uploadImgs中删除当前预览的图片
      setUploadImgs((prev) => prev.filter((url) => url !== previewCurrent));

      // 同步更新Uploader组件的文件列表
      setFileList((prev) => prev.filter((file) => file.url !== previewCurrent));

      // 关闭预览
      setPreviewVisible(false);
    }
  };

  return (
    <View className="feedback">
      <View className="feedback-container">
        <View className="feedback-type-section">
          <View className="section-title">反馈类型</View>
          <View className="feedback-types">
            {feedbackType.map((type) => (
              <View
                key={type.id}
                className={`type-item ${selectedType === type.id ? 'selected' : ''}`}
                onClick={() => setSelectedType(type.id)}
              >
                {type.name}
              </View>
            ))}
          </View>
        </View>

        <View className="feedback-content-section">
          <View className="section-title">问题描述</View>
          <Textarea
            className="feedback-input"
            placeholder="详细描述建议或意见"
            maxlength={200}
            value={problem}
            onInput={(e) => setProblem(e.detail.value)}
          />
          <View className="char-count">{problem.length}/200</View>
        </View>

        <View className="feedback-upload-section">
          <View className="upload-title">图片</View>
          <Uploader
            // autoUpload={false}
            upload={onUpload}
            // beforeUpload={beforeUpload}
            value={fileList}
            onChange={setFileList}
            multiple
            uploadIcon={<Plus />}
            maxCount="5"
            mediaType={['image']}
            deletable={false}
            onFileItemClick={onImageClick}
            onDelete={handleDelete}
            style={
              {
                '--nutui-uploader-preview-tips-height': '0px',
              } as CSSProperties
            }
          />
          <Text className="tips">{`${uploadImgs.length}`} / 5</Text>
        </View>

        <View className="feedback-submit-section">
          <Form>
            <Form.Item name="contact" label="联系方式">
              <Input
                onChange={(value) => setContact(value)}
                align="right"
                placeholder="请留下您的任意联系方式"
              />
            </Form.Item>
          </Form>
        </View>

        <View
          className="history-feedback"
          onClick={() =>
            Taro.navigateTo({
              url: '/package/pages/feedback/feedbackHistory/index',
            })
          }
        >
          <SvgIcon name="historyIcon" size={14} />
          <Text>历史问题</Text>
        </View>

        <Button block className="submit-button" onClick={handleSubmit}>
          提交反馈
        </Button>
      </View>

      {/* 自定义图片预览组件 */}
      {previewVisible && (
        <View className="custom-preview-container">
          <View className="preview-overlay" onClick={closePreview}></View>
          <View className="preview-content">
            <Image className="preview-image" src={previewCurrent} mode="aspectFit" />
            <View className="preview-actions">
              <View className="preview-delete" onClick={deletePreviewImage}>
                删除
              </View>
              <View className="preview-close" onClick={closePreview}>
                关闭
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

export default Feedback;
