import { View } from '@tarojs/components';
import { memo } from 'react';

// InfoCard 组件接口定义
interface InfoCardProps {
  customerCount: number;
  serviceArea: string;
}

// InfoCard 组件
const InfoCard: React.FC<InfoCardProps> = memo(({ customerCount, serviceArea }) => {
  return (
    <View className="info-card">
      <View className="info-card-top">
        <View>您服务的客户(位)</View>
        <View>{customerCount}</View>
      </View>
      <View className="info-card-divider"></View>
      <View className="info-card-bottom">
        {/* <Text>你服务的片区</Text> */}
        {/* <Text>{serviceArea}</Text> */}
      </View>
    </View>
  );
});

export default InfoCard;
