// 页面主容器
.wrapper {
  width: 100%;
  height: 100%;
  padding: 20px;
  // position: relative;
  display: flex;
  flex-direction: column;
  gap: 28px;
  z-index: 2;

  // 顶部标题区域，包含头像、用户信息和操作按钮
  .title {
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 16px;
  }

  // 团队信息展示区域，使用网格布局展示三个信息卡片
  .team-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    align-items: center;
    justify-content: center;
    z-index: 2;
  }

  // 广告展示区域，使用渐变背景
  .ad {
    width: 100%;
    height: 110px;
    border-radius: 24px;
    background: linear-gradient(90deg, #a898f9 0%, #d0c7fb 25.56%, #d0c5fb 74.7%, #a897f5 99.06%);
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    align-items: center;
    justify-content: center;
    z-index: 2;

    // 广告项样式
    .ad-item {
      opacity: 0.4;
      border-right: 1px solid #805ec2;
      // text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;

      // 广告文本样式
      .ad-item-text {
        font-size: 22px;
        font-weight: 400;
        color: #805ec2;
      }
    }
  }
}

// 列表项图标样式
.item-icon {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 24px;
  font-weight: 400;
  color: #381187;
}

// 用户头像样式
.avatar-container {
  width: 88px !important;
  height: 88px !important;
  border-radius: 50% !important;
  // margin: 0 auto;
  border: 2px solid #fff;
  overflow: hidden;
}

// 小尺寸SVG图标样式
.svg-small {
  width: 24px;
  height: 24px;
}

// 卡片容器样式
.card-container {
  display: flex;
  flex-basis: calc(100% - 92px);
  flex-direction: column;
  gap: 12px;
}
.card-container-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
// 卡片名称样式
.card-name {
  color: #131313;
  font-family: 'PingFang SC';
  font-size: 32px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

// 卡片信息样式
.card-info {
  display: flex;
  align-items: center;
  gap: 20px;

  // 小标签样式（如实名认证标签）
  .sm {
    background: rgba(238, 122, 66, 0.1);
    height: 40px;
    color: #ee7a42;
  }
}

// 标题右侧操作按钮区域样式
.title-right {
  display: flex;
  align-items: center;
  // gap: 12px;
  width: 120px;
  justify-content: space-between;
  padding: 0 4px;
  margin-left: auto;
  margin-bottom: auto;
}

// 合同待签署项样式
.contract-item {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-end;
  margin-right: 2px * 2;

  text {
    color: #999;
    font-size: 12px * 2;
  }

  // 红点提示样式
  .dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    flex-shrink: 0;
    background: #f93a4a;
  }
}
