import useCustomTabbar from '@/store/custom-tab-bar';
import Taro, { useDidShow } from '@tarojs/taro';

export default function useTabBar(selectedIndex: number) {
  const pageCtx: any = Taro.getCurrentInstance().page;
  const [setSelected] = useCustomTabbar((state) => [state.setSelected]);
  useDidShow(() => {
    console.log('useTabBar', pageCtx);
    console.log('useTabBar', selectedIndex);
    setSelected(selectedIndex); // 更新选中的 tabBar 索引
    // if (typeof pageCtx.getTabBar === 'function' && pageCtx.getTabBar()) {
    //   pageCtx.getTabBar().setData({
    //     selected: selectedIndex,
    //   });
    // }
  });
}
