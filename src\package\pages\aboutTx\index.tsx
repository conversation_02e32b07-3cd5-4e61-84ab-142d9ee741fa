import SvgIcon from '@/pages/me/components/SvgIcon';
import { ArrowRight } from '@nutui/icons-react-taro';
import { Cell } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import './index.scss';

const AboutTx = () => {
  return (
    <View className="about-tx">
      <View className="about-tx-header">
        <SvgIcon name="aboutTx" size={71} />
        <Text>湖南添星科技有限公司</Text>
        <Text>
          “消费还款”模式是添星科技首创的商业模式，该解决方案基于“因消费产生负债，再通过消费化解负债”的思路设立，其基本机制是债务人将日常刚需消费转移到添星商城上，而添星商城会将消费产生的毛利用做化解债事的资金来源，帮助债务人偿还债务。添星科技也将帮助债务人创业增收，从而缩短还款周期
        </Text>
      </View>
      <Cell.Group>
        <Cell
          title="资质证明"
          extra={<ArrowRight size={12} />}
          onClick={() => {
            Taro.navigateTo({
              url: '/package/pages/settings/proof/index',
            });
          }}
        />
        <Cell
          title="用户服务协议"
          extra={<ArrowRight size={12} />}
          onClick={() => {
            Taro.navigateTo({
              url: '/pages/qaSmart/index?url=https://guanjia.hunantianxing.com/userPrivacy/userServes.htm',
            });
          }}
        />
        <Cell
          title="用户隐私政策"
          extra={<ArrowRight size={12} />}
          onClick={() => {
            Taro.navigateTo({
              url: '/pages/qaSmart/index?url=https://guanjia.hunantianxing.com/userPrivacy/privacy.htm',
            });
          }}
        />
      </Cell.Group>
    </View>
  );
};

export default AboutTx;
