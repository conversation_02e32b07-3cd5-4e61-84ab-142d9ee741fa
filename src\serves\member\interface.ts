// Response interface
export interface GetMemberInfoRes {
  /* */
  code: number;

  /* */
  data: UserMemberInfo;

  /* */
  msg: string;
}

export interface UserMemberInfo {
  createTime: string;
  /*会员账号ID */
  username: string;
  memberUserId: number;

  /*会员昵称 */
  nickname: string;

  /*会员头像 */
  avatar: string;

  /*会员真实姓名 */
  name: string;

  /*会员证件号码 */
  idCard: string;

  /*会员手机号码 */
  mobile: string;

  /*会员卡id */
  cardId: number;

  /*会员卡名称 */
  cardName: string;

  /*会员卡大图片 */
  cardImage: string;

  /*会员卡小图片 */
  smallIcon: string;

  /*会员卡权益图片 */
  cardSloganImage: string;

  /*实名认证 （0:未实名 1:实名） */
  realAuthentication: string;

  /*现金券（数量） */
  cashCouponNumber: number;

  /*我的佣金（元） */
  myCommission: number;

  /*我的团队（数量） */
  myGroupNumber: number;

  /*到期日期 */
  expireDate: string;
}

// Response interface
export interface GetCardBenefitsRes {
  /* */
  code: number;

  /* */
  data: {
    /*权益ID */
    id: number;

    /*权益类型 */
    benefitType: string;

    /*权益名称 */
    benefitName: string;

    /*权益图标 */
    benefitIcon: string;

    /*规则 */
    rule: string;

    /*权益值 */
    benefitValue: string;

    /*收益是否可以提现 */
    canWithdraw: number;

    /*权益说明 */
    benefitDescription: string;

    /*状态 */
    status: number;
  }[];

  /* */
  msg: string;
}

// 获取账管顾问
export interface GetMmenumenuPermissionRes {
  /* */
  code: number;

  /* */
  data: {
    /*账管顾问 false:隐藏 true:显示 */
    accountingManagement: boolean;
  };

  /* */
  msg: string;
}
