import { serveIp } from '@/serves/config';
import http from '@/serves/http';
import { CommonResponse } from '@/serves/interface/miniProgram';

import { GetAppReductionConfigListRes, GetAppReductionConfigListVO } from './interface';

/**
 * @description 获取减免金额配置列表
 * @param {GetAppReductionConfigListVO} params
 * @returns {Promise<CommonResponse<GetAppReductionConfigListRes>>}
 */
export function getAppReductionConfigList(
  creditorId: GetAppReductionConfigListVO['creditorId'],
): Promise<CommonResponse<GetAppReductionConfigListRes>> {
  return http.get(`${serveIp.ip}/app-api/crm/contract/appReduction-config/list`, {
    creditorId,
  });
}
