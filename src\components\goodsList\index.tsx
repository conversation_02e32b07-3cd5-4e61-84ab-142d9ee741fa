import useLogin from '@/hooks/use-login';
import { getGoodsList } from '@/serves/goods';
import { Ellipsis, Empty, Price } from '@nutui/nutui-react-taro';
import { Image, View } from '@tarojs/components';
import { useEffect, useMemo, useState } from 'react';
import { SmartMasonryVirtualList } from '../SmartMasonryVirtualList';
import './index.scss';
import Taro from '@tarojs/taro';

const GoodsList = () => {
  const [data, setData] = useState<any[]>([]);
  const { accessToken } = useLogin();
  useEffect(() => {
    console.log(accessToken);
    if (accessToken) {
      getGoodsListfn();
    }
  }, [accessToken]);

  const getGoodsListfn = async () => {
    if (!accessToken) return;
    const res = await getGoodsList();
    setData(res.data.list);
  };
  const items = useMemo(() => {
    return data.map((item, index) => (
      <View
        key={index}
        className="goodsItem"
        onClick={() => {
          Taro.navigateToMiniProgram({
            appId: 'wx445f7d3555ef57a7',
            path: `/pages/goods_details/index?id=${item.productId}&mt=`,
            success: function (res) {
              // 打开成功
            },
          });
        }}
      >
        <View className="goodsImageBox">
          <Image className="goodsImage" src={item.image} mode="aspectFill" />
        </View>
        <View className="goodsInfo">
          <View className="goodsName">
            <Ellipsis content={item.storeName} direction="end" rows={2} />
          </View>
          <View className="goodsPrice">
            <Price price={item.price} size="large" thousands />
          </View>
        </View>
      </View>
    ));
  }, [data]);
  return (
    <View className="goodsList">
      {items.length > 0 ? (
        <SmartMasonryVirtualList items={items} />
      ) : (
        <Empty status="empty" title="暂无相关商品推荐" />
      )}
    </View>
  );
};

export default GoodsList;
