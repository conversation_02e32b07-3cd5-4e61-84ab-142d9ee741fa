import '@nutui/nutui-react-taro/dist/styles/theme-default.scss';
import type { PropsWithChildren } from 'react';
import { Component } from 'react';
import './app.scss';
import ConfigProvider from './components/ConfigProvider';

export interface taroGlobalData {
  userInfo?: {
    /*用户编号 */
    userId: number;
    /*访问令牌 */
    accessToken: string;
    /*刷新令牌 */
    refreshToken: string;
    /*过期时间 */
    expiresTime: Record<string, unknown>;
    /*社交用户 openid */
    openid: string;
  };
}

class App extends Component<PropsWithChildren> {
  // 设置全局变量 "x"
  taroGlobalData = {
    userInfo: undefined,
  } as taroGlobalData;
  componentDidMount() { }

  componentDidShow() { }

  componentDidHide() { }

  // this.props.children 是将要会渲染的页面
  render() {
    return <ConfigProvider>{this.props.children}</ConfigProvider>;
  }
}

export default App;
