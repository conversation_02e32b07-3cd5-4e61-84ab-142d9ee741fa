import PageContainerTemp from '@/components/pageContainerTemp';
import { SimpleValue, Tabs } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import { useState } from 'react';
import './index.scss';
const Index = () => {
  const [tabValue, setTabValue] = useState<SimpleValue>('行业资讯');

  return (
    <PageContainerTemp showTabbar showMarks={false} title="资讯">
      <View className="message-container">
        <Tabs
          value={tabValue}
          onChange={(value) => {
            setTabValue(value);
          }}
        >
          <Tabs.TabPane title="行业资讯" value="行业资讯">
            行业资讯
          </Tabs.TabPane>
          <Tabs.TabPane title="最新政策" value="最新政策">
            最新政策
          </Tabs.TabPane>
          <Tabs.TabPane title="相关案例" value="相关案例">
            相关案例
          </Tabs.TabPane>
          <Tabs.TabPane title="破晓絮语" value="破晓絮语">
            破晓絮语
          </Tabs.TabPane>
        </Tabs>
      </View>
    </PageContainerTemp>
  );
};

export default Index;
