import SvgIcon from '@/pages/me/components/SvgIcon';
import { Text, View } from '@tarojs/components';
import './index.scss';

/**
 * 笔记编辑器工具栏组件的属性接口
 * @interface NoteEditorToolbarProps
 * @property {number} keyboardHeight - 键盘高度，用于调整工具栏位置
 * @property {() => void} onAddImage - 添加图片回调函数
 * @property {() => void} onAddOrderedList - 添加有序列表回调函数
 * @property {() => void} onAddUnorderedList - 添加无序列表回调函数
 * @property {() => void} onShare - 分享笔记回调函数
 * @property {() => void} onDelete - 删除笔记回调函数
 * @property {() => void} onSubmit - 提交/保存笔记回调函数
 */
interface NoteEditorToolbarProps {
  keyboardHeight: number;
  onAddImage: () => void;
  onAddOrderedList: () => void;
  onAddUnorderedList: () => void;
  onShare: () => void;
  onDelete: () => void;
  onSubmit: () => void;
}

/**
 * 笔记编辑器工具栏组件
 * 提供添加图片、列表、分享、删除和保存等功能按钮
 * 工具栏会根据键盘高度自动调整位置
 * 
 * @param {NoteEditorToolbarProps} props - 组件属性
 * @returns {JSX.Element} 渲染的工具栏组件
 */
const NoteEditorToolbar: React.FC<NoteEditorToolbarProps> = ({
  keyboardHeight,
  onAddImage,
  onAddOrderedList,
  onAddUnorderedList,
  onShare,
  onDelete,
  onSubmit,
}) => {
  /**
   * 计算工具栏样式，确保在键盘弹出时仍然可见
   * 根据键盘高度动态调整工具栏位置
   * 
   * @returns {Object} 工具栏的样式对象
   */
  const getToolbarStyle = () => {
    return {
      transform: `translateY(${keyboardHeight > 0 ? -keyboardHeight * 2 : 0}rpx)`,
      // bottom: keyboardHeight > 0 ? `${keyboardHeight * 2}rpx` : '0',
    };
  };

  return (
    <View className="editor-toolbar" style={getToolbarStyle()}>
      {/* 添加图片按钮 */}
      <View className="toolbar-item" onClick={onAddImage}>
        <SvgIcon name="notebookImage" />
        <Text className="iconfont">图片</Text>
      </View>
      {/* 添加有序列表按钮 */}
      <View className="toolbar-item" onClick={onAddOrderedList}>
        <SvgIcon name="notebookList" />
        <Text className="iconfont">项目排序</Text>
      </View>
      {/* 添加无序列表按钮 */}
      <View className="toolbar-item" onClick={onAddUnorderedList}>
        <SvgIcon name="notebookUnordered" />
        <Text className="iconfont">项目编号</Text>
      </View>
      {/* 分享按钮 */}
      <View className="toolbar-item" onClick={onShare}>
        <SvgIcon name="notebookShare" />
        <Text className="iconfont">分享</Text>
      </View>
      {/* 删除按钮 */}
      <View className="toolbar-item" onClick={onDelete}>
        <SvgIcon name="notebookDel" />
        <Text className="iconfont">删除</Text>
      </View>
      {/* 保存按钮 */}
      <View className="toolbar-item" onClick={onSubmit}>
        <SvgIcon name="notebookSave" />
        <Text className="iconfont">保存</Text>
      </View>
    </View>
  );
};

export default NoteEditorToolbar;
