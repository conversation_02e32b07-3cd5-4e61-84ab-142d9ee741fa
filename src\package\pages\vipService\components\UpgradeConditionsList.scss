// 升级条件
.upgrade-conditions {
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin-top: 40px;
}
// 条件组
.condition-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  &-title {
    color: #666;
    font-size: 16px * 2;
    padding: 4px 10px;
    border-radius: 8px;
    align-self: flex-start;
    font-weight: 500;
    position: relative;

    &::before {
      content: '';
      display: inline-block;
      width: 40%;
      height: 30%;
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 1;
      opacity: 0.5;
      // 渐变
      background: linear-gradient(to right, #f8bfc3, #f6e1e6);
    }

    text {
      position: relative;
      z-index: 2;
    }
  }
  &-body {
    background: #fff;
    border-radius: 16px;
    padding: 30px;
    display: flex;
    flex-direction: column;
    gap: 30px;
  }
}

.condition-item {
  display: flex;
  align-items: center;
  gap: 20px;
}

.condition-icon {
  width: 20px * 2;
  height: 20px * 2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px * 2;
  &.ok {
    background: #f76359;
  }
  &.no {
    background: #d8d8d8;
  }
}

.condition-desc {
  color: #131313;
  font-size: 16px * 2;
  font-weight: 500;
}
