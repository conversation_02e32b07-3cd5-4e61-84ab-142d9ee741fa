.goodsList {
  width: 100%;
}
.goodsItem {
  width: 100%;
  background-color: #fff;
  padding-bottom: 16px;
  border-top-right-radius: 16px;
  border-top-left-radius: 16px;
  overflow: hidden;
  .goodsImageBox {
    width: 100%;
    .nut-image-default {
      height: auto;
    }
  }
  .goodsImage {
    width: 100%;
  }
  .goodsName {
    padding: 0 16px;
    font-size: 28px;
  }
  .goodsPrice {
    padding-left: 16px;
    margin-top: 12px;
  }
}
