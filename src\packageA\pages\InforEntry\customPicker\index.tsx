import { Popup, SafeArea } from '@nutui/nutui-react-taro';
import { ScrollView, View } from '@tarojs/components';
import './index.scss';
interface CustomPickerProps {
  customNode?: React.ReactNode;
  visible?: boolean;
  onClose?: () => void;
  title?: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
}
const CustomPicker = (CustomPickerProps: CustomPickerProps) => {
  return (
    CustomPickerProps.visible && (
      <Popup
        title={CustomPickerProps.title}
        position="bottom"
        visible={CustomPickerProps.visible}
        onClose={CustomPickerProps.onClose}
        lockScroll
        closeable
        destroyOnClose
        style={{ width: '100%', minHeight: 'auto' }}
        className="customPicker"
      >
        <View className="customPicker_contentBox">
          <View className="customPicker_content">
            <ScrollView className='customPicker_content_scroll' scrollY style={{ height: '100%', overflow: 'auto' }}>
              {CustomPickerProps.customNode}
            </ScrollView>
          </View>

          <SafeArea position="bottom" />
        </View>
      </Popup>
    )
  );
};

export default CustomPicker;
