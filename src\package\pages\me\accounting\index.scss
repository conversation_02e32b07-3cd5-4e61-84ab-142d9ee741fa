.accounting-page {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 28px;
}

.info-card {
  border-radius: 4px * 2;
  border: 1px solid #fff;
  background: rgba(255, 246, 249, 0.72);
  display: flex;
  gap: 12px * 2;
  flex-direction: column;
  padding: 24px 32px 20px 32px;

  &-top {
    display: flex;
    flex-direction: column;

    view:nth-child(1) {
      color: #333;
      font-size: 14px * 2;
    }

    view:nth-child(2) {
      color: #131313;
      font-size: 22px * 2;
      font-weight: 600;
    }
  }

  &-divider {
    stroke-width: 1px * 2;
    stroke: #dededf;
    height: 1px * 2;
    flex-shrink: 0;
    border-bottom: 2px dashed #dededf;
  }

  &-bottom {
    display: flex;
    gap: 4px * 2;

    text:nth-child(1) {
      color: #666;
      font-size: 12px * 2;
    }

    text:nth-child(2) {
      color: #131313;
      font-size: 12px * 2;
    }
  }
}

.tabs-list {
  background-color: #fff;
  border-radius: 4px * 2;
  --nutui-tabs-titles-item-active-color: #131313;
  --nutui-tabs-tabpane-padding: 0;
}
