import DateSelect from '@/components/dateSelect';
import { MoneyDisplayCard } from '@/components/MoneyDisplay';
import PageContainerTemp from '@/components/pageContainerTemp';
import { getMonthDebtDetail } from '@/serves/debt';

import useUserStore from '@/store/useUserStore';
import { formatDate } from '@/utils/date';
import { addMoney, subtractMoney } from '@/utils/money';
import { Tabs } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import { FC, useCallback, useState } from 'react';
import { useRequest } from 'taro-hooks';
import DebtContentList from './components/DebtContentList';
import './index.scss';

interface DebtDetailOfMeProps {}

const DebtDetailOfMe: FC<DebtDetailOfMeProps> = () => {
  const [tabvalue, setTabvalue] = useState<string>('all');
  return (
    <View className="container">
      <Tabs
        value={tabvalue}
        onChange={(value) => {
          setTabvalue(value as string);
        }}
        autoHeight
      >
        <Tabs.TabPane title="全部" value="all"></Tabs.TabPane>
        <Tabs.TabPane title="消费" value="consume"></Tabs.TabPane>
        <Tabs.TabPane title="助力" value="help"></Tabs.TabPane>
        <Tabs.TabPane title="佣金" value="commission"></Tabs.TabPane>
      </Tabs>
      <AllTabPane type={tabvalue} />
    </View>
  );
};

const AllTabPane = ({ type = 'all' }: { type?: string }) => {
  const { userInfo } = useUserStore((state) => state);
  const [month, setMonth] = useState<string>(formatDate(new Date(), { template: 'YYYYMM' }));
  const { data, loading } = useRequest(
    () => getMonthDebtDetail({ month, memberId: userInfo?.userId }),
    {
      cacheKey: 'monthDebtDetail',
      refreshDeps: [userInfo?.userId, month],
      ready: !!userInfo?.userId,
    },
  );
  // 计算总收益金额
  const total = data?.data.reduce((acc, curr) => {
    if (curr.type === 0) {
      return addMoney(acc, curr.mount);
    }
    return subtractMoney(acc, curr.mount);
  }, 0);

  // 选择月份
  const onSelectMonth = useCallback((dateStr: string) => {
    setMonth(dateStr);
  }, []);

  return (
    <PageContainerTemp type="default" title="化债明细">
      <View className="all-tab-pane">
        <View className="all-tab-pane-header">
          <DateSelect onConfirm={onSelectMonth} />
          <View style={{ display: 'flex', alignItems: 'center' }}>
            <Text>总收益金额：</Text>
            <MoneyDisplayCard
              usePrice={false}
              amount={total}
              style={{ color: '#999', fontSize: '12px' }}
              options={{ unit: '元', digits: 2, showZeroForEmpty: true }}
            />
          </View>
        </View>
        <DebtContentList data={data?.data || []} loading={loading} />
      </View>
    </PageContainerTemp>
  );
};

export default DebtDetailOfMe;
