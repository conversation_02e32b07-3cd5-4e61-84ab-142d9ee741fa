.container {
  padding: 28px 24px;
  background-color: #f5f4f9;
  height: 100%;


  .base-info,
  .reminder {
    --nutui-cell-title-color: #666;
    --nutui-cell-title-font-size: 28px;

    .nut-cell-left {
      flex: none !important;
    }

    .nut-cell-extra {
      color: #131313;
      font-size: 14px * 2;
      font-weight: 400;
      line-height: normal;
    }
  }


  .tabs {
    position: fixed;
    height: 90px * 2;
    width: 100%;
    left: 0;
    bottom: 0;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    align-items: center;
    background-color: #fff;
    padding: 20px 10px;

    .tabs-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 4px * 2;
      background-color: #fff;

      &:not(:last-child) {
        border-right: 1px solid #F5F6FA;
      }

      text {
        color: #666;
        font-size: 12px * 2;
      }
    }
  }
}