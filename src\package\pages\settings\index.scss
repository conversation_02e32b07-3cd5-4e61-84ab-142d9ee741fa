.settings {
  padding: 28px 24px;
  display: flex;
  flex-direction: column;
  gap: 28px;
  background-color: #f5f4f9;
  height: 100%;
  width: 100%;
  --nutui-button-default-border-color: #fff;
  --nutui-button-default-background-color: #fff;

  .cell-group {
    // background-color: #fff;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    gap: 28px;

    .title-container {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .nut-cell-title {
    color: #131313;
    font-size: 14px * 2;
  }
}
