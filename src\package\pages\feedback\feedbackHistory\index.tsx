import EmptyContainer from '@/components/empty-container';
import { getFeedbackList } from '@/serves/feedback';
import { FeedbackListResponse } from '@/serves/feedback/interface';
import { formatDate } from '@/utils/date';
import { Text, View } from '@tarojs/components';
import Taro, { useDidShow } from '@tarojs/taro';
import { useCallback, useMemo, useState } from 'react';
import { useRequest } from 'taro-hooks';
import './index.scss';

// 反馈历史页面组件
const FeedbackHistory = () => {
  // 状态管理
  const [feedbackList, setFeedbackList] = useState<FeedbackListResponse[]>([]); // 存储反馈列表数据
  const feedbackType = useMemo(() => {
    return Taro.getStorageSync('feedbackType') ?? [];
  }, []); // 存储反馈类型

  // 获取反馈历史数据的方法
  const fetchFeedbackHistory = useCallback(async () => {
    try {
      const res = await getFeedbackList();
      setFeedbackList(res.data);
    } catch (error) {
      console.error('获取反馈历史失败', error);
      // 错误提示
      Taro.showToast({
        title: '获取反馈历史失败',
        icon: 'none',
      });
    }
  }, []);

  // 组件挂载时获取反馈历史数据
  const { loading, refresh } = useRequest(fetchFeedbackHistory, {
    manual: true,
    cacheKey: 'feedback-list',
    loadingDelay: 300,
  });

  useDidShow(() => {
    refresh();
  });

  // 处理反馈项点击事件
  const handleItemClick = useCallback((id: number) => {
    Taro.navigateTo({
      url: `/package/pages/feedback/historyDetail/index?id=${id}`,
    });
  }, []);

  const getFeedbackTypeName = useCallback(
    (type: string) => {
      const value = feedbackType.find((item) => item.value === type);
      return value?.label;
    },
    [feedbackType],
  );

  return (
    <View className="feedback-history">
      <View className="feedback-history-container">
        {/* 加载状态显示 */}
        {loading ? (
          <View className="loading-container">加载中...</View>
        ) : feedbackList.length > 0 ? (
          // 反馈列表渲染
          <View className="feedback-list">
            {feedbackList.map((item, index) => (
              <View key={index} className="feedback-item" onClick={() => handleItemClick(item.id!)}>
                {/* 反馈项头部：标题和状态 */}
                <View className="feedback-item-header">
                  <Text className="feedback-item-title">{getFeedbackTypeName(item.type!)}</Text>
                  <View className={`feedback-item-status ${item.dealStatus ? 'resolved' : ''}`}>
                    {item.dealStatus ? '已回复' : '待处理'}
                  </View>
                </View>
                {/* 反馈内容 */}
                <View className="feedback-item-content">{item.problem}</View>
                {/* 反馈时间 */}
                <View className="feedback-item-footer">
                  <Text className="feedback-item-time">
                    反馈时间：
                    {formatDate(item.feedbackTime, {
                      showTime: true,
                      dateSeparator: '/',
                      timeSeparator: ':',
                    })}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        ) : (
          // 空状态显示
          <EmptyContainer title="暂无反馈记录" />
        )}
      </View>
    </View>
  );
};

export default FeedbackHistory;
