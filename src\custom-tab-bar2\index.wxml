<view class="tab-bar" style="display: flex;position: fixed;bottom: 0;left: 0;height: 48px;padding-bottom: env(safe-area-inset-bottom);width: 100%;align-items: center;">
  <view wx:for="{{list}}" style="flex: 1;text-align: center;" wx:key="index" class="tab-bar-item" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
    <view style="color: {{selected === index ? selectedColor : color}}">{{item.text}}</view>
  </view>
</view>