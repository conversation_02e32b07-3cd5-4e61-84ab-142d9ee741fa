/**
 * @description 管理后台 - 会员用户更新 Request VO
 */
export interface MemberUserUpdateReqVO {
  /**
   * @description 手机号
   * @example "15601691300"
   */
  mobile: string;
  /**
   * @description 状态
   * @example "Mg=="
   */
  status?: string;
  /**
   * @description 用户昵称
   * @example "李四"
   */
  nickname: string;
  /**
   * @description 用户邮箱
   * @example "<EMAIL>"
   */
  email?: string;
  /**
   * @description 头像
   * @example "https://www.iocoder.cn/x.png"
   */
  avatar?: string;
  /**
   * @description 用户昵称
   * @example "李四"
   */
  name?: string;
  /**
   * @description 用户性别
   * @example 1
   */
  sex?: number;
  /**
   * @description 所在地编号
   * @example 4371
   */
  areaId?: number;
  /**
   * @description 所在地全称
   * @example "上海上海市普陀区"
   */
  areaName?: string;
  /**
   * @description 出生日期
   */
  birthday?: string;
  /**
   * @description 会员备注
   * @example "我是小备注"
   */
  mark?: string;
  /**
   * @description 会员标签
   * @example [1, 2]
   */
  tagIds?: number[];
  /**
   * @description 会员等级编号
   * @example 1
   */
  levelId?: number;
  /**
   * @description 用户分组编号
   * @example 1
   */
  groupId?: number;
  /**
   * @description 密码,不填的话默认666666
   * @example "666666"
   */
  password?: string;
  /**
   * @description 开卡日期
   */
  cardCreateTime?: string;
  /**
   * @description 是否实名认证
   */
  realAuthentication?: number;
  /**
   * @description 用户状态变更时间
   */
  stateTime?: string;
  /**
   * @description 到期时间
   */
  cardEndTime?: string;
  /**
   * @description 证件号码
   */
  idCard?: string;
  /**
   * @description 实名认证时间
   */
  realAuthenticationTime?: string;
  /**
   * @description 推荐人
   */
  reference?: number;
  /**
   * @description 用户账号
   * @example "0000001"
   */
  username?: string;
  /**
   * @description 注册来源
   * @example 34
   */
  type?: number;
  /**
   * @description 编号
   * @example 23788
   */
  id: number;
}
