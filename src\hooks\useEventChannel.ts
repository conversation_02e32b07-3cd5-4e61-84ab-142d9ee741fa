import Taro, { useLoad, useUnload } from '@tarojs/taro';
import { useState } from 'react';

/**
 * 一个React Hook，用于从Taro.navigateTo的eventChannel接收数据。
 * 它监听一个特定的事件并返回数据负载。
 * 新增了可选的缓存功能，可在页面卸载时自动清理缓存。
 *
 * @template T 事件负载的预期类型。
 * @param {string} eventName 要监听的事件的名称。
 * @param {object} [options] 可选配置项。
 * @param {boolean} [options.cache=false] 是否启用缓存。如果为true，则会将事件数据缓存到本地存储，并在页面加载时尝试恢复。页面卸载时会自动清理缓存。
 * @returns {T | null} 从前一个页面传递的数据，如果尚未收到则为null。
 */
export const useEventChannel = <T = any>(eventName: string, options: { cache?: boolean } = {}) => {
  const { cache = false } = options;
  const cacheKey = `event_channel_cache_${eventName}`;

  const [eventData, setEventData] = useState<T | null>(() => {
    if (cache) {
      try {
        const cachedData = Taro.getStorageSync(cacheKey);
        if (cachedData) {
          return cachedData as T;
        }
      } catch (e) {
        console.error('从缓存读取失败', e);
      }
    }
    return null;
  });

  useLoad(() => {
    const pages = Taro.getCurrentPages();
    if (pages.length === 0) {
      return;
    }
    const currentPage = pages[pages.length - 1];

    if (currentPage && currentPage.getOpenerEventChannel) {
      const eventChannel = currentPage.getOpenerEventChannel();

      if (eventChannel) {
        eventChannel.on(eventName, (data: T) => {
          setEventData(data);
          if (cache) {
            try {
              Taro.setStorageSync(cacheKey, data);
            } catch (e) {
              console.error('写入缓存失败', e);
            }
          }
        });
      }
    }
  });

  useUnload(() => {
    if (cache) {
      try {
        Taro.removeStorageSync(cacheKey);
      } catch (e) {
        console.error('从缓存删除失败', e);
      }
    }
  });

  return eventData;
};
