import { Text, View } from '@tarojs/components';
import { memo } from 'react';

import SvgIcon from '@/pages/me/components/SvgIcon';

interface BoundAccountInfo {
  phone: string;
  accountId: string;
  bindTime: string;
}

interface BoundInfoProps {
  accountInfo: BoundAccountInfo;
  onSwitchBind: () => void;
}

/**
 * 已绑定状态的信息展示组件
 */
export const BoundInfo = memo<BoundInfoProps>(({
  accountInfo,
  onSwitchBind,
}) => {
  return (
    <View className="bound-mall-account">
      <View className="bound-context">
        {/* 商城手机号 */}
        <View className="bound-info-item">
          <View className="bound-info-wrapper">
            <Text className="bound-label">商城手机号</Text>
            <Text className="bound-value">{accountInfo.phone}</Text>
          </View>
          <View className="bound-divider" />
        </View>

        {/* 商城账号 */}
        <View className="bound-info-item">
          <View className="bound-info-wrapper">
            <Text className="bound-label">商城账号</Text>
            <Text className="bound-value">{accountInfo.accountId}</Text>
          </View>
          <View className="bound-divider" />
        </View>

        {/* 绑定时间 */}
        <View className="bound-info-item">
          <View className="bound-info-wrapper">
            <Text className="bound-label">绑定时间</Text>
            <Text className="bound-value">{accountInfo.bindTime}</Text>
          </View>
        </View>
      </View>
      
      <View className="switch-bind-mall-account">
        <View className="bound-info-item" onClick={onSwitchBind}>
          <View className="bound-info-wrapper">
            <Text className="bound-label">切换绑定商城账户</Text>
            <SvgIcon size={12} name="arrow-right-small" className="bound-value" />
          </View>
        </View>
      </View>
    </View>
  );
});
