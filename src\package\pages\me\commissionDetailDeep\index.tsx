import { Button, Cell, CellGroup } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';

import PageContainerTmp from '@/components/pageContainerTemp';
import useRequestWithLifecycle from '@/hooks/use-request-with-lifecycle';
import { useEventChannel } from '@/hooks/useEventChannel';
import { appCalculateMoney } from '@/serves/accounting/appCalculateMoney';
import { getAppReductionConfigList } from '@/serves/accounting/getAppReductionConfigList';
import { GetDebtorIdCollecRes } from '@/serves/accounting/interface';
import { desensitize } from '@/utils/format';
import { formatMoney } from '@/utils/money';
import './index.scss';

type CommissionItem = GetDebtorIdCollecRes['data']['debtorCaseEntrustDetailVOList'][number] & {
  signStatus: 'installment' | 'detail' | 'planDetail';
};

const CommissionDetailDeep = () => {
  const eventPayload = useEventChannel<{ data: CommissionItem }>(
    'acceptDataFromCommissionListPage',
  );
  const commissionData = eventPayload?.data;
  const [period, setPeriod] = useState<number>();

  // 获取减免金额配置，主要是分期tabs
  const { data: reductionData } = useRequestWithLifecycle(
    () => getAppReductionConfigList(commissionData!.creditorId),
    {
      refreshDeps: [commissionData],
      ready: !!commissionData?.creditorId && commissionData.signStatus === 'installment',
    },
  );
  // 获取分期期数
  const periods = useMemo(() => {
    return (
      reductionData?.data?.map((item) => {
        return {
          value: item.period,
          label: `${item.period}期`,
        };
      }) || []
    );
  }, [reductionData]);

  useEffect(() => {
    if (periods.length > 0) {
      setPeriod(periods[0]?.value);
    } else if (commissionData?.debtReductionPeriods) {
      setPeriod(commissionData?.debtReductionPeriods);
    }
  }, [periods, commissionData]);

  // 计算分期还款金额
  const { data: moneyData, loading } = useRequestWithLifecycle(
    () =>
      appCalculateMoney({
        caseEntrustId: commissionData!.id,
        debtReductionPeriods: period ?? periods[0]?.value,
      }),
    {
      refreshDeps: [period, commissionData],
      ready: !!period,
    },
  );

  const handlePeriodChange = useCallback(
    (value: number) => {
      if (loading) return;
      setPeriod(value);
    },
    [loading],
  );

  // 确认分期
  const handleConfirm = useCallback(() => {
    Taro.navigateTo({
      url: '/package/pages/me/accounting/contractTemplate/index',
      success: (res) => {
        res.eventChannel.emit('appContractTemplate', {
          data: {
            caseEntrustId: moneyData?.data.caseEntrustId, // 委案id
            totalDebtReductionAmount: moneyData?.data?.totalDebtReductionAmount, // 化债总金额
            actualReductionAmount: moneyData?.data?.actualReductionAmount, // 本金减免
            actualDebtReductionAmount: moneyData?.data?.actualDebtReductionAmount, // 利息罚金
            monthlyDebtReductionAmount: moneyData?.data?.monthlyDebtReductionAmount, // 每月应化债金额
            debtReductionPeriods: moneyData?.data?.debtReductionPeriods, // 分期期数
          },
        });
      },
    });
  }, [moneyData]);

  // 债务人信息
  const debtInfo = useMemo(
    () => ({
      债务人: commissionData?.debtorName || '',
      债务人证件信息: desensitize(commissionData?.debtorCreditCode || '', 4, 3),
      债权人: commissionData?.creditorName || '',
      逾期账户: desensitize(commissionData?.overdueAccount || '', 4, 3),
      逾期阶段: commissionData?.caseStage || '-',
      委案金额: formatMoney(commissionData?.entrustedAmount, { unit: '元' }),
      利息: formatMoney(commissionData?.interest, { unit: '元' }),
      年利率: `${commissionData?.annualizedRate || '-'}%`,
      '滞纳金(罚息)': formatMoney(commissionData?.lateFee, { unit: '元' }),
    }),
    [commissionData],
  );

  // 是否待签署
  const isSing = useMemo(() => {
    return commissionData?.signStatus === 'detail';
  }, [commissionData]);

  // 分期还款金额
  const financialDetails = useMemo(() => {
    const detail = {
      '委案金额(本金)': formatMoney(moneyData?.data?.entrustedAmount, { unit: '元' }),
      委案本金减免: formatMoney(moneyData?.data?.principalReductionAmount, { unit: '元' }),
      利息罚金: formatMoney(moneyData?.data?.interestAndLateFeeAmount, { unit: '元' }),
      利息罚金减免: formatMoney(moneyData?.data?.interestReductionAmount, { unit: '元' }),
      预计每月还款: formatMoney(moneyData?.data?.monthlyDebtReductionAmount, { unit: '元' }),
    };
    if (isSing) {
      // 待签署
      const financialDetails = {
        分期期数: commissionData?.debtReductionPeriods + '期',
        ...detail,
        签署状态: '待签署(债务人)',
      };
      return financialDetails;
    }
    return detail;
  }, [moneyData, isSing, commissionData]);

  return (
    <PageContainerTmp marksType="none" title="委案明细" showTabbar={false} isRet align="center">
      <View className="deep-container">
        <DebtInfo data={debtInfo} />
        <View className="deep-container-list">
          <DebtInfo data={financialDetails}>
            {!isSing ? (
              <Cell>
                <View className="deep-container-list-item">
                  <View className="deep-container-list-item-top">
                    <View className="deep-container-list-item-top-title">分期期数</View>
                    <View className="deep-container-list-item-top-extra">{period}期</View>
                  </View>
                  <View className="content">
                    {periods.map((item) => (
                      <View
                        onClick={() => handlePeriodChange(item.value)}
                        key={item.value}
                        className={`content-item ${period === item.value ? 'active' : ''}`}
                      >
                        {item.label}
                      </View>
                    ))}
                  </View>
                </View>
              </Cell>
            ) : (
              <Cell title={<View className="isSign-title">已选分期信息</View>}></Cell>
            )}
          </DebtInfo>
        </View>
        {!isSing && (
          <View className="confirm-btn">
            <Button type="danger" block onClick={handleConfirm}>
              确认分期
            </Button>
          </View>
        )}
      </View>
    </PageContainerTmp>
  );
};

const DebtInfo = memo((props: { data: Record<string, string>; children?: React.ReactNode }) => {
  return (
    <CellGroup>
      {props.children}
      {Object.entries(props.data).map(([key, value]) => (
        <Cell key={key} title={key} extra={value} />
      ))}
    </CellGroup>
  );
});
DebtInfo.displayName = 'DebtInfo';

export default CommissionDetailDeep;
