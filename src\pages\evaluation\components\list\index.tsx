import SvgIcon from '@/pages/me/components/SvgIcon';
import type { SvgName } from '@/utils/svgLoader';
import { ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import './index.scss';

interface IData {
  title: string;
  desc: string;
  icon: SvgName;
  onClick?: () => void;
}

const testData: IData[] = [
  {
    title: '创收能力测评',
    desc: '专业债务还款能力评估，精选测试题，输出专业报告',
    icon: 'icon_cp1',
    onClick: () => {
      Taro.navigateTo({
        url: '/pages/index/index',
      });
    },
  },
  {
    title: '债权回收诊断器',
    desc: '专业债权回收率评估，测试你的债权多少时间能收回',
    icon: 'icon_cp2',
    onClick: () => {
      Taro.navigateTo({
        url: '/pages/index/index',
      });
    },
  },
];

const EvaluationList = () => {
  return (
    <ScrollView className="ev-scroll" enableBackToTop>
      {testData.map((data, index: number) => (
        <ItemRender key={index} data={data} index={index} onClick={data.onClick} />
      ))}
    </ScrollView>
  );
};

// item
const ItemRender = ({
  data,
  index,
  onClick,
}: {
  data: IData;
  index: number;
  onClick?: () => void;
}) => {
  return (
    <View className="ev-item" key={index} onClick={onClick}>
      <View className="ev-item-left">
        <Text className="title">{data.title}</Text>
        <Text className="desc">{data.desc}</Text>
      </View>
      <View>
        <SvgIcon name={data.icon} size={70}></SvgIcon>
      </View>
    </View>
  );
};

export default EvaluationList;
