import { serveIp } from '../config';
import HTTP from '../http';
import {
  ContractSignParams,
  ContractSignRes,
  GetContractSignPageListParams,
  GetContractSignPageListRes,
} from './interface';

/**
 * 分页查询-签署-合同列表
 * @param {object} params App - 合同管理 - 签署分页查询DTO
 * @param {number} params.pageNo 页码，从 1 开始
 * @param {number} params.pageSize 每页条数，最大值为 100
 * @param {number} params.partyCSignStatus 丙方签订状态 0:未签订 1:已签订
 * @returns
 */
export function getContractSignPageList(
  params: GetContractSignPageListParams,
): Promise<GetContractSignPageListRes> {
  return HTTP.post(`${serveIp.ip}/app-api/crm/contract/page`, params);
}

/**
 * 修改-签署-合同模板
 * @param {object} params App - 合同管理 - 签署分页查询DTO
 * @param {number} params.id 合同ID
 * @param {string} params.singImage 签署图片
 * @returns
 */
export function contractSign(params: ContractSignParams): Promise<ContractSignRes> {
  return HTTP.post(`${serveIp.ip}/app-api/crm/contract/sign`, params);
}
