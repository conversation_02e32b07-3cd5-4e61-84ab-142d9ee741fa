import Taro from '@tarojs/taro';
import { useCallback } from 'react';

/**
 * 绑定商城账户业务逻辑Hook
 * 负责处理发送验证码、绑定账户等业务操作
 */
export const useBindMallAccountLogic = () => {
  
  /**
   * 发送验证码
   * @param phone 手机号
   * @param isPhoneValid 手机号是否有效
   * @param isRunning 是否正在倒计时
   * @param onSuccess 成功回调
   * @param onError 失败回调
   */
  const sendVerificationCode = useCallback(async (
    phone: string,
    isPhoneValid: boolean,
    isRunning: boolean,
    onSuccess?: () => void,
    onError?: (error: any) => void
  ) => {
    if (!isPhoneValid || isRunning) {
      return;
    }

    try {
      // TODO: 调用发送验证码API
      console.log('发送验证码到:', phone);

      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000));

      Taro.showToast({
        title: '验证码已发送',
        icon: 'success',
        duration: 2000,
      });

      onSuccess?.();
    } catch (error) {
      console.error('发送验证码失败:', error);
      
      Taro.showToast({
        title: '发送失败，请重试',
        icon: 'none',
        duration: 2000,
      });

      onError?.(error);
    }
  }, []);

  /**
   * 绑定商城账户
   * @param phone 手机号
   * @param code 验证码
   * @param isBindButtonEnabled 绑定按钮是否可用
   * @param onSuccess 成功回调
   * @param onError 失败回调
   */
  const bindMallAccount = useCallback(async (
    phone: string,
    code: string,
    isBindButtonEnabled: boolean,
    onSuccess?: () => void,
    onError?: (error: any) => void
  ) => {
    if (!isBindButtonEnabled) {
      return;
    }

    try {
      console.log('bindMallAccount', { phone, code });
      
      // TODO: 调用绑定商城账户API
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000));

      Taro.showToast({
        title: '绑定成功',
        icon: 'success',
        duration: 2000,
      });

      onSuccess?.();
    } catch (error) {
      console.error('绑定商城账户失败:', error);
      
      Taro.showToast({
        title: '绑定失败，请重试',
        icon: 'none',
        duration: 2000,
      });

      onError?.(error);
    }
  }, []);

  /**
   * 获取已绑定的账户信息
   * TODO: 实际项目中应该从API获取真实数据
   */
  const getBoundAccountInfo = useCallback(() => {
    return {
      phone: '************',
      accountId: '********',
      bindTime: '2025/07/10 12:01:01',
    };
  }, []);

  return {
    sendVerificationCode,
    bindMallAccount,
    getBoundAccountInfo,
  };
};
