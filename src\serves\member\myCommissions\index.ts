import { serveIp } from '@/serves/config';
import http from '@/serves/http';
import { GetMyEarningsParams, GetMyEarningsRes } from './interface';

/**
 * 查询-我的收益
 * @param {object} params 会员 APP - 我的佣金 - 详情查询DTO
 * @param {number} params.memberUserId 会员ID
 * @param {number} params.type 类型 0:收入 1:支出 2:待结算
 * @returns
 */
export function getMyEarnings(params: GetMyEarningsParams): Promise<GetMyEarningsRes> {
  return http.post(`${serveIp.ip}/app-api/crm/earnings/getMyEarnings`, params);
}
