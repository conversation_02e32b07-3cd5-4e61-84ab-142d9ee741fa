import Taro from '@tarojs/taro';
import { useShallow } from 'zustand/react/shallow';

const getSetterName = (key: string) => {
  return 'set' + key.charAt(0).toUpperCase() + key.slice(1);
};

export interface Setter {
  (value: any): void;
}

interface StateGetter<T> {
  (set?: any, get?: any, api?: any): T;
}

export const autoCreateSetters = function <T>(config: StateGetter<T> | T) {
  return function (set: any, get: any, api: any) {
    const configResult: any = {};
    config = typeof config === 'function' ? (config as StateGetter<T>)(set, get, api) : config;
    Object.keys(config as any).forEach((key) => {
      if (/^(set|get)/.test(key)) {
        return;
      }
      const setterName = getSetterName(key);
      const setter = (config as any)[setterName];
      configResult[key] = (config as any)[key];
      if (setter) {
        configResult[setterName] = setter;
      } else {
        configResult[getSetterName(key)] = (value: any) => {
          value = typeof value === 'function' ? value(get()[key], api) : value;
          set({ [key]: value });
        };
      }
    });
    return configResult;
  };
};
export interface UseStore<T, SetT> {
  (initState: (state: T & SetT) => any): any;
  getState: () => T & SetT;
  getInitialState: () => T & SetT;
  setState: (value: any) => void;
  subscribe: (listener: (state: T & SetT, prevState: T & SetT) => void) => () => void;
  destroy: () => void;
  reset: () => void;
}

export const autoUseShallow = function <T, SetT>(useStore: any) {
  const rewriteHook: UseStore<T, SetT> = function (initState) {
    return useStore(useShallow(initState));
  };
  rewriteHook.getState = useStore.getState;
  rewriteHook.getInitialState = useStore.getInitialState;
  rewriteHook.setState = useStore.setState;
  rewriteHook.subscribe = useStore.subscribe;
  rewriteHook.destroy = useStore.destroy;
  rewriteHook.reset = () => {
    useStore.setState({ ...useStore.getInitialState() });
  };
  return rewriteHook;
};

const isWeb = process.env.TARO_ENV === 'h5';

export const customStorage = {
  getItem: (key: string): string | null => {
    return isWeb ? window.sessionStorage.getItem(key) : Taro.getStorageSync(key);
  },
  setItem: (key: string, value: string): void => {
    isWeb ? window.sessionStorage.setItem(key, value) : Taro.setStorageSync(key, value);
  },
  removeItem: (key: string): void => {
    isWeb ? window.sessionStorage.removeItem(key) : Taro.removeStorageSync(key);
  },
};
