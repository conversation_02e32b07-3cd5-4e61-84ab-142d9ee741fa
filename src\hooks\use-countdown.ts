import { useRef } from '@taro-hooks/core';
import debounce from 'lodash/debounce';
import { useCallback, useEffect, useState } from 'react';

// 倒计时Hook，支持手动开启、停止，并防抖避免重复点击
const useCountdown = (initialCount: number) => {
  const [count, setCount] = useState(initialCount);
  // 新增一个 state，专门用于驱动 UI 的运行状态
  const [isUiRunning, setUiRunning] = useState(false);

  // isRunningRef 仍然用于内部逻辑判断，防止重复启动
  const isRunningRef = useRef(false);
  const timer = useRef<any>(null);

  const clear = useCallback(() => {
    console.log('clear countdown');
    clearInterval(timer.current);
    isRunningRef.current = false;
    setUiRunning(false); // 停止时，也立即更新UI
    setCount(initialCount);
  }, [initialCount]);

  useEffect(() => {
    return () => {
      // 组件卸载时确保清除
      clearInterval(timer.current);
    };
  }, []);

  const start = useCallback(() => {
    // 内部逻辑判断依然使用 ref
    if (isRunningRef.current) {
      console.log('to many timer');
      return;
    }
    console.log('start');
    isRunningRef.current = true;
    setUiRunning(true); // 点击后，立即更新UI状态

    timer.current = setInterval(() => {
      setCount((prev) => {
        console.log('prev', prev);
        if (prev <= 1) {
          // 在倒计时结束时调用 clear
          // clear 内部会处理所有状态的重置
          clear();
          return 0; // 返回0，避免在clear后还执行一次 prev - 1
        }
        return prev - 1;
      });
    }, 1000);
  }, [clear]);

  // 防抖
  const debouncedStart = useCallback(debounce(start, 300), [start]);

  // 返回 UI 状态和 count
  return { count, start: debouncedStart, isRunning: isUiRunning, clear };
};

export default useCountdown;
