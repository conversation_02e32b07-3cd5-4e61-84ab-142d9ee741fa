.vip-service {
  padding: 20px;
  height: 100%;
  width: 100%;
  background-color: #1a1b20;
  display: flex;
  flex-direction: column;
  font-size: 24px;
  gap: 24px;
  --nutui-color-background: transparent;

  // 添加swiper-item样式
  .swiper-item-spacing {
    padding: 0 8rpx;
    box-sizing: border-box;
    transition: opacity 0.3s ease;
  }

  // 非当前激活的swiper-item样式
  .swiper-item-inactive {
    opacity: 0.5;
  }

  &-title {
    display: flex;
    align-items: center;
    gap: 20px;
    height: 46px * 2;

    .avatar {
      background-color: #fff;
      border-radius: 50%;
      width: 34px * 2;
      height: 34px * 2;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      padding: 2px;

      image {
        border-radius: 50%;
      }
    }

    .right {
      display: flex;
      flex-direction: column;
      gap: 10px;

      &-name {
        display: flex;
        align-items: center;
        gap: 20px;
        text {
          color: #fff;
          font-size: 14px * 2;
          font-weight: 400;
        }
      }

      &-time {
        text {
          color: #babbbf;
          font-size: 11px * 2;
          font-weight: 400;
        }

        .highlight {
          color: #f7c4a9;
        }
      }
    }
  }
}

.vip-equity {
  height: 406px * 2;
  flex-shrink: 0;
  overflow: auto;
  border-radius: 8px * 2;
  background: #22252c;
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 32px;

  &-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    color: #f3c6b0;
    font-size: 16px * 2;
    font-weight: 500;
  }

  .vip-equity-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);

    &-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      // justify-content: center;
      gap: 8px;
      height: 90px * 2;

      text:first-of-type {
        color: #b9bcc0;
        font-size: 12px * 2;
        text-align: center;
      }

      text:last-of-type {
        color: #6e7076;
        font-size: 10px * 2;
        // margin-right: auto;
      }
    }
  }
}
