.list-of-communicate {
  display: flex;
  flex-direction: column;
  gap: 12px * 2;

  &-item {
    display: flex;
    padding: 16px * 2;
    justify-content: space-between;
    align-items: center;

    position: relative;

    &::after {
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      position: absolute;
      content: '';
      display: block;
      width: 90%;
      height: 1px * 2;
      background-color: #f5f6fa;
    }

    &-left {
      display: flex;
      gap: 8px * 2;
      align-items: center;

      &-name {
        color: #131313;
        font-size: 16px * 2;
        font-weight: 500;
      }

      &-phone {
        color: #131313;
        font-size: 16px * 2;
        font-weight: 400;
      }
    }

    &-right {
      display: flex;
      gap: 4px * 2;
      align-items: center;

      &-commission {
        color: #131313;
        font-size: 14px * 2;
        font-weight: 400;
      }
    }
  }
}
