.repayment-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 28px;
  // background-color: #f5f4f9;
  // overflow: hidden;
}

.plan-bg {
  stroke-width: 1px * 2;
  stroke: #fff;
  box-shadow: 0px 2px 8px 0px #fff inset;
  backdrop-filter: blur(29.901100158691406px * 2);
  background: linear-gradient(
    180deg,
    rgba(251, 230, 232, 0.59) 7.14%,
    rgba(255, 255, 255, 0.42) 61.47%,
    rgba(255, 255, 255, 0.58) 100%
  );
  height: 100%;
  width: 100%;
  border-bottom: 1px solid #fff;
  position: relative;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;

  &.credit {
    clip-path: polygon(72% 0%, 75% 20%, 100% 20%, 100% 100%, 0 100%, 0% 60%, 0 0);
  }

  // top: 0;
  // left: 0;
  // z-index: 2;

  // 裁剪左上角和右上角的特殊形状
  // clip-path: path(
  //   'M0 0H250.913C255.955 0 260.608 2.7112 263.093 7.0979L272.113 23.0152C273.544 25.4818 276.181 27 279.033 27H339C345.627 27 351 32.3726 351 39V100%H0V0Z'
  // );
  clip-path: polygon(72% 0%, 75% 13%, 100% 13%, 100% 100%, 0 100%, 0% 60%, 0 0);
}

.plan_repayment_bg {
  width: 100%;
  flex-shrink: 0;
  padding: 64px 32px 48px 32px;
  border-radius: 20px;
  position: relative;
  z-index: 2;
  gap: 24px;
  display: flex;
  flex-direction: column;

  // background: transparent;
  // box-shadow: 0px 2px 8px 0px #fff inset;

  .detail {
    z-index: 3;
    display: grid;
    grid-template-columns: 1fr 1fr;

    &-item {
      display: flex;
      flex-direction: column;
      gap: 12px;
      text:first-of-type {
        color: #333;
        font-size: 14px * 2;
      }
      text:nth-of-type(2) {
        color: #f00;
        font-size: 24px * 2;
        font-weight: 600;
      }

      &.already-repayment {
        text:nth-of-type(2) {
          color: #131313;
          font-size: 24px * 2;
          font-weight: 600;
        }
      }
    }
  }

  .info {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    &-item {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 6px;

      text:first-of-type {
        color: #131313;
        font-size: 600;
        font-size: 14px * 2;
      }
      text:nth-of-type(2) {
        color: #666;
        font-size: 24px;
      }

      &-tag {
        height: 20px * 2;
        background-color: #f5f5f5;
        background: #f56366;
        line-height: 20px * 2;
        text-align: center;
        color: #fff;
        font-size: 24px;
        width: fit-content;
        padding: 0 6px * 2;
      }
    }
  }
}

// 金币背景图设置
.repayment-container-item {
  position: relative;
  .coin-container {
    display: inline-block;
    width: 85px * 2;
    height: 85px * 2;
    position: absolute;
    right: 28px;
    top: -20px;
    z-index: 1;
  }
}

.more {
  z-index: -1;
  // width: 351px;
  width: 100%;
  height: 51px * 2;
  flex-shrink: 0;
  background: linear-gradient(180deg, #f6d4bb 17.67%, #ffefe4 49.63%);
  display: flex;
  padding: 28px 32px;
  box-sizing: border-box;
  // border-radius: 20px;
  border-bottom-right-radius: 20px;
  border-bottom-left-radius: 20px;
  transform: translateY(-20px);

  &-item {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 6px;

    text:nth-child(1) {
      color: #9d9d9d;
      font-size: 11px * 2;
    }

    text:nth-child(2) {
      color: #131313;
      font-size: 12px * 2;
      font-weight: 500;
    }
  }
}

.info-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 3;

  &.credit {
    display: none;
  }
}

.dividing {
  width: 100%;
  height: 1px * 2;
  background-color: #f5f5f5;
  border: 1px dashed #dededf;
}

.detail-title {
  color: #131313;
  font-size: 16px * 2;
  font-weight: 600;
  margin: 28px 0px;
  display: inline-block;
}

.detail-list {
  padding: 0 !important;
  max-height: 700px;

  // &-container {

  &-item {
    display: flex;
    flex-shrink: 0;
    gap: 30px;
    padding: 0;
    padding: 24px;
    background-color: #fff;
    border-bottom: 1px solid #f5f5f5;

    box-sizing: border-box;
    // height: 69px * 2;

    .left {
      display: flex;
      flex-direction: column;
      gap: 16px;

      text:nth-child(1) {
        color: #131313;
        font-size: 14px * 2;
        font-weight: 400;
      }

      text:nth-child(2) {
        color: #666;
        font-size: 12px * 2;
        font-weight: 400;
      }
    }

    .center {
      display: inline-flex;
      flex-direction: column;
      gap: 12px;

      // --nutui-tag-padding: 18px 18px;
      // --nutui-tag-font-size: 24px;
      .nut-tag {
        display: inline-flex !important;
        width: fit-content;
        padding: 20px;
        font-size: 22px;
      }

      text:nth-child(2) {
        color: #666;
        font-size: 12px * 2;
        font-weight: 400;
      }
    }

    .right {
      flex: 1;

      text {
        color: #131313;
        // text-align: right;
        font-size: 16px * 2;
        font-weight: 600;
      }
    }
  }
}

.detail-tabs {
  position: relative;

  .filter {
    top: 10px;
    right: 20px;
    position: absolute;
    transform: translateY(50%);
    color: #666;

    font-size: 12px * 2;
    font-weight: 400;

    display: flex;
    align-items: center;
    gap: 10px;
  }
}
