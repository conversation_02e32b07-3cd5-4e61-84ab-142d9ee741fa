import LoadingPage from '@/components/loadingPage';
import PageContainerTemp from '@/components/pageContainerTemp';
import { getMyEarnings } from '@/serves/member/myCommissions';
import useUserStore from '@/store/useUserStore';
import { View } from '@tarojs/components';
import { FC } from 'react';
import { useRequest } from 'taro-hooks';
import MyCommissionsHeader from './components/MyCommissionsHeader';
import MyCommissionsList from './components/MyCommissionsList';
import './index.scss';

interface MyCommissionsProps {}

const MyCommissions: FC<MyCommissionsProps> = () => {
  const { userInfo } = useUserStore((state) => state);
  const { loading, data } = useRequest(() => getMyEarnings({ type: 0 }), {
    cacheKey: 'my-commissions-list',
    ready: !!userInfo?.userId,
    refreshDeps: [userInfo?.userId],
  });

  if (loading) {
    return <LoadingPage />;
  }

  return (
    <PageContainerTemp title="我的佣金" isRet={true} align="center">
      <View className="my-commissions-container">
        <MyCommissionsHeader data={data} />
        <MyCommissionsList />
      </View>
    </PageContainerTemp>
  );
};

export default MyCommissions;
