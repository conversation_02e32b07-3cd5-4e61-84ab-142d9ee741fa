import { SvgName } from '@/utils/svgLoader';
import { ArrowRight } from '@nutui/icons-react-taro';
import { Cell } from '@nutui/nutui-react-taro';
import { Swiper, SwiperItem, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import SvgIcon from '../SvgIcon';
import './index.scss';

export interface IListItemProps {
  icon: SvgName | React.ReactNode;
  title: string;
  desc?: string[] | React.ReactNode[];
  path?: string; // 跳转路径
  disabled?: boolean; // 是否禁用
  visible?: boolean; // 是否显示
  onClick?: () => void; // 点击事件
}

/**
 * 列表项组件
 * 用于展示带有图标、标题、描述和右箭头的列表项，支持点击跳转和轮播描述
 *
 * @param {object} props - 组件属性对象
 * @param {SvgName | React.ReactNode} props.icon - 列表项左侧图标，可以是SVG名称或React节点
 * @param {string} props.title - 列表项标题
 * @param {string[] | React.ReactNode[]} [props.desc] - 列表项描述，支持多条描述轮播展示
 * @param {string} [props.path] - 点击后跳转的路径
 * @param {boolean} [props.disabled] - 是否禁用列表项，默认为false
 * @param {boolean} [props.visible=true] - 是否显示列表项，默认为true
 * @param {Function} [props.onClick] - 点击列表项的回调函数
 * @returns {JSX.Element | null} 返回列表项组件或null（当visible为false时）
 */
const ListItem = ({
  icon,
  title,
  desc,
  path,
  disabled,
  visible = true,
  onClick,
}: IListItemProps): JSX.Element | null => {
  if (!visible) return null;
  return (
    <Cell
      className={`list-c ${disabled ? 'disabled' : ''}`}
      clickable
      onClick={() => {
        onClick?.();
        if (path) {
          Taro.navigateTo({
            url: path,
          });
        }
      }}
      title={
        <View className="list">
          {/* 判断icon是否为SvgName类型 */}
          {typeof icon === 'string' ? <SvgIcon name={icon as SvgName} /> : icon}
          <Text>{title}</Text>
        </View>
      }
      align="center"
      extra={
        <View className="extra">
          <Swiper className="swiper-c" vertical circular interval={3000} autoplay>
            {desc?.map((item, index) => {
              return <SwiperItem key={index}>{item}</SwiperItem>;
            })}
          </Swiper>

          <ArrowRight size={12} />
          {/* <SvgIcon name='arrow-right' size={12} /> */}
        </View>
      }
    />
  );
};

export default ListItem;
