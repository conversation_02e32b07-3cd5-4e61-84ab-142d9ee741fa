// Response interface
export interface GetDebtMonthRes {
  /* */
  code: number;

  /* */
  data: {
    /*当月 */
    month: string;

    /*当月化债金额 */
    debtReductionAmount: number;

    /*当月应化债金额 */
    monthlyDebtReductionAmount: number;

    /*剩余还债金额 */
    remainingDebtReductionAmount: number;
  };

  /* */
  msg: string;
}
// Response interface
export interface GetContractListResData {
  /*主键ID */
  id: number;

  /*剩余待化债 */
  remainingDebtAmount: number;

  /*案件委托ID */
  caseEntrustId: number;

  /*关联委案编号 */
  caseNumber: string;

  /*模板ID */
  templateId: number;

  /*合同类型 */
  contractType: string;

  /*模板名称 */
  templateName: string;

  /*合同编号（唯一） */
  contractNumber: string;

  /*合同名称 */
  contractName: string;

  /*合同地址 */
  contractUrl: string;

  /*化债总金额（元） */
  totalDebtReductionAmount: number;

  /*实际减免金额（元） */
  actualReductionAmount: number;

  /*实际化债总额（元） */
  actualDebtReductionAmount: number;

  /*每月应化债金额（元） */
  monthlyDebtReductionAmount: number;

  /*已化债金额（元） */
  convertedDebtAmount: number;

  /*债权人名称 */
  creditorName: string;

  /*化债期数（月，必须>0） */
  debtReductionPeriods: number;

  /*合同开始日期 */
  startDate: string;

  /*甲方名称 */
  partyAName: string;

  /*甲方统一社会信用代码 */
  partyAUnifiedSocialCreditCode: string;

  /*甲方法定代表人 */
  partyALegalRepresentative: string;

  /*法定代表人联系方式 */
  partyAContactNumber: string;

  /*甲方地址 */
  partyAAddress: string;

  /*乙方名称 */
  partyBName: string;

  /*乙方统一社会信用代码 */
  partyBUnifiedSocialCreditCode: string;

  /*乙方法定代表人 */
  partyBLegalRepresentative: string;

  /*法定代表人联系方式 */
  partyBContactNumber: string;

  /*乙方地址 */
  partyBAddress: string;

  /*丙方名称 */
  partyCName: string;

  /*丙方证件号码/统一社会信用代码 */
  partyCIdNumberOrUnifiedSocialCreditCode: string;

  /*丙方联系方式 */
  partyCContactNumber: string;

  /*甲方签订状态 */
  partyASignStatus: number;

  /*乙方签订状态 */
  partyBSignStatus: number;

  /*丙方签订状态 */
  partyCSignStatus: number;

  /*甲方签订时间 */
  partyATime: string;

  /*乙方签订时间 */
  partyBTime: string;

  /*丙方签订时间 */
  partyCTime: string;

  /*合同状态: 草稿 draft\n审批中 in_approval\n审批通过 end_approval\n审批驳回 back_approval\n作废 cancel\n履约中 in_performance\n合同结束 end\n变更 change */
  status: string;

  /*审批人ID */
  approverId: number;

  /*审批人姓名 */
  approveName: string;

  /*审批时间 */
  approvalTime: string;

  /*审批描述 */
  approvalRemark: string;

  /*丙方地址 */
  partyCAddress: string;

  /*创建时间 */
  createTime: string;

  /*更新时间 */
  updateTime: string;

  /*创建人 */
  creator: string;

  /*更新人 */
  updater: string;

  /*删除标志（0：未删除，1：已删除） */
  deleted: boolean;

  /*租户ID */
  tenantId: number;

  /*债权人 */
  creditor: string;

  /*逾期账号 */
  overdueAccount: string;

  /*委托金额 */
  entrustedAmount: number;

  /*利息 */
  interest: number;

  /*年化率 */
  annualizedRate: number;

  /*滞纳金 */
  lateFee: number;

  /*最大减免金额 */
  maxReduction: number;

  /*当前期 */
  currentPeriod: number;

  /*开卡银行 */
  bank: string;
}

export interface GetContractListRes {
  /* */
  code: number;

  /* */
  data: GetContractListResData[];

  /* */
  msg: string;
}
// Parameter interface
export interface GetMonthDebtDetailParams {
  /*月份 */
  month?: string;

  /*会员id */
  memberId?: number;
}

// Response interface
export interface GetMonthDebtDetailRes {
  /* */
  code: number;

  /* */
  data: {
    /*主键ID */
    id: number;

    /*合同ID */
    contractId: number;

    /*委案ID */
    caseEntrustId: number;

    /*还款计划ID */
    repaymentPlanId: number;

    /*描述 */
    description: string;

    /*订单号 */
    orderNumber: string;

    /*金额 */
    mount: number;

    /*类型 */
    type: number;

    /*类型名称 */
    typeName: string;

    /*债务类型 */
    debtType: string;

    /*卡ID */
    cardId: number;

    /*会员用户ID */
    memberUserId: number;

    /*创建时间 */
    createTime: string;

    /*更新时间 */
    updateTime: string;

    /*创建人 */
    creator: string;

    /*更新人 */
    updater: string;
  }[];

  /* */
  msg: string;
}

// 还款计划查询
export interface GetRepaymentPlanRes {
  /* */
  code: number;

  /* */
  data: {
    /*数据 */
    list: {
      /*主键ID */
      id: number;

      /*关联合同ID */
      contractId: number;

      /*当前期数（如：1） */
      periodSeq: number;

      /*总期数（如：54） */
      totalPeriods: number;

      /*应还款日期（格式：YYYY-MM-DD） */
      dueDate: string;

      /*计划还款本金 */
      principal: number;

      /*实际化债金额 */
      actualDebtAmount: number;

      /*未结清金额 */
      outstandingAmount: number;

      /*利息 */
      interest: number;

      /*还款状态（1-待还款 2-已还款 3-逾期） */
      statusCode: number;

      /*创建时间 */
      createTime: string;

      /*更新时间 */
      updateTime: string;

      /*创建人 */
      creator: string;

      /*更新人 */
      updater: string;

      /*删除标志（0：未删除，1：已删除） */
      deleted: boolean;

      /*租户ID */
      tenantId: number;
    }[];

    /*总量 */
    total: number;
  };

  /* */
  msg: string;
}
