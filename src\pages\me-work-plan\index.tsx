import DateSelect from '@/components/dateSelect';
import PageContainerTemp from '@/components/pageContainerTemp';
import usePlanByMonth from '@/hooks/use-plan-by-month';
import { formatDate } from '@/utils/date';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { FC, useCallback, useMemo, useState } from 'react';
import CalendarCardItem from './calendar';
import './index.scss';

interface WorkPlanOfMeProps {}

const WorkPlanOfMe: FC<WorkPlanOfMeProps> = () => {
  const [selectDate, setSelectDate] = useState<string>(
    formatDate(new Date(), { template: 'YYYY-MM' }),
  );

  const [activeDate, setDate] = useState<string>(
    formatDate(new Date(), { template: 'YYYY-MM-DD' }),
  );

  const { data, currentMonthWorkNames, getWorkTypeOfLabel } = usePlanByMonth({
    date: selectDate,
  });

  // 获取选择的日期当天任务
  const currentDayWorks = useMemo(() => {
    return data?.data?.filter((item) => {
      const startTime = new Date(item.startTime).getDate();
      const currentTime = new Date(activeDate).getDate();
      return startTime === currentTime;
    });
  }, [data?.data, activeDate]);

  // 选择日期
  const onSelectDate = useCallback((valueStr: string) => {
    // 将 valueStr (YYYYMM 格式) 转换为 YYYY-MM 格式
    const year = valueStr.substring(0, 4);
    const month = valueStr.substring(4);
    const newSelectDate = `${year}-${month}`;
    setSelectDate(newSelectDate);

    // 同时更新 activeDate 为所选月份的第一天
    const newActiveDate = `${year}-${month}-01`;
    setDate(newActiveDate);
  }, []);

  // 点击日期
  const onDayClick = useCallback((day: string) => {
    setDate(day);
  }, []);

  // 添加待办
  const handAddTodo = useCallback(() => {
    Taro.navigateTo({
      url: '/package/pages/newTodo/index',
    });
  }, []);

  const handleTodoClick = useCallback((id: number) => {
    console.log(id);
    Taro.navigateTo({
      url: `/package/pages/newTodo/TodoDetail/index?id=${id}`,
    });
  }, []);

  return (
    <PageContainerTemp showMarks={false} title="工作任务" isRet align="center" showTabbar={false}>
      {/* 日历 */}
      <View className="work-plan-container">
        <DateSelect onConfirm={onSelectDate} />
        <CalendarCardItem
          workNames={currentMonthWorkNames}
          date={new Date(selectDate)}
          onDayClick={onDayClick}
          key={selectDate}
        />
        {/* 待办事项 */}
        <View className="todo-list">
          <Text className="title">{activeDate} 待办事项</Text>
          <View className="todo-list-content">
            {currentDayWorks?.map((item, index) => {
              return (
                <View
                  className="todo-list-item"
                  key={index}
                  onClick={() => handleTodoClick(item.id)}
                >
                  <View className="item-tag">{getWorkTypeOfLabel(item.workType)}</View>
                  <Text>{item.workContent}</Text>
                  <Text>
                    {formatDate(new Date(item.startTime), {
                      template: 'HH:mm',
                    })}
                    ~
                    {formatDate(new Date(item.endTime), {
                      template: 'HH:mm',
                    })}
                  </Text>
                </View>
              );
            })}
          </View>
        </View>
        {/* 添加待办 */}
        {/* <Drag direction="y" style={{ bottom: '200px', right: '50px' }}>
          <Button
            className="add-todo-btn"
            shape="round"
            type="primary"
            icon={<Plus color="#FFF" />}
            onClick={handAddTodo}
          />
        </Drag> */}
      </View>
    </PageContainerTemp>
  );
};

export default WorkPlanOfMe;
