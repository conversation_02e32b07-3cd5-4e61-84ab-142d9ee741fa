import SvgIcon from '@/pages/me/components/SvgIcon';
import { Text, View } from '@tarojs/components';
import { FC } from 'react';
import './index.scss';

interface EmptyContainerProps {
  title: string;
}

const EmptyContainer: FC<EmptyContainerProps> = ({ title }) => {
  return (
    <View className="empty-container">
      <SvgIcon name="feedbackBg" style={{ width: 250, height: 150 }} />
      <Text>{title}</Text>
    </View>
  );
};

export default EmptyContainer;
