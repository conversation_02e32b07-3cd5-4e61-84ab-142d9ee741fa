import { Elevator } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import { pinyin } from 'pinyin-pro';
import { useEffect, useMemo, useRef, useState } from 'react';
import './index.scss';
import areaList from '@/utils/area';

// 通用数据项接口
type Item = { id: string; name: string };
// 分组结构
type Group = { title: string; list: Item[] };

// 获取汉字全拼（无声调）并拼接为单字符串
const getFullPinyin = (text: string) => pinyin(text, { toneType: 'none', type: 'array' }).join('');
// 获取拼音首字母大写
const getInitial = (text: string) => {
  const full = getFullPinyin(text);
  return full.charAt(0).toUpperCase() || '#';
};
interface ElevatorThreeLevelProps {
  onchange: (data: [Item | null, Item | null, Item | null]) => void;
  initvalue?: [Item | null, Item | null, Item | null];
}
const ElevatorThreeLevel = (Props: ElevatorThreeLevelProps) => {
  const { province_list, city_list, county_list } = areaList;
  const firstref = useRef(true);
  //  扁平化各级数据
  const provinces = useMemo(
    () => Object.entries(province_list).map(([id, name]) => ({ id, name })),
    [province_list],
  );

  //  构建市和县映射
  const citiesByProv = useMemo(() => {
    const map: Record<string, Item[]> = {};
    Object.entries(city_list).forEach(([id, name]) => {
      const provId = id.slice(0, 2) + '0000';
      if (!map[provId]) map[provId] = [];
      map[provId].push({ id, name });
    });
    return map;
  }, [city_list]);

  const countiesByCity = useMemo(() => {
    const map: Record<string, Item[]> = {};
    Object.entries(county_list).forEach(([id, name]) => {
      const cityId = id.slice(0, 4) + '00';
      if (!map[cityId]) map[cityId] = [];
      map[cityId].push({ id, name });
    });
    return map;
  }, [county_list]);

  //  级别 & 选中状态
  const [level, setLevel] = useState<1 | 2 | 3>(1);
  const [selectedProv, setSelectedProv] = useState<Item | null>(null);
  const [selectedCity, setSelectedCity] = useState<Item | null>(null);
  const [selectedCounty, setSelectedCounty] = useState<Item | null>(null);
  //  根据层级渲染并拼音分组、排序
  const groupedData = useMemo((): Group[] => {
    let arr: Item[] = [];
    if (level === 1) arr = provinces;
    else if (level === 2 && selectedProv) arr = citiesByProv[selectedProv.id] || [];
    else if (level === 3 && selectedCity) arr = countiesByCity[selectedCity.id] || [];

    // 拼音排序
    arr.sort((a, b) => getFullPinyin(a.name).localeCompare(getFullPinyin(b.name)));

    // 分组
    const groups: Record<string, Item[]> = {};
    arr.forEach((item) => {
      const key = getInitial(item.name);
      if (!groups[key]) groups[key] = [];
      groups[key].push(item);
    });

    // 转数组并排序
    const result = Object.entries(groups)
      .map(([title, list]) => ({ title, list }))
      .sort((a, b) => a.title.localeCompare(b.title));

    // 分组不为空则返回。若为空则返回一个占位，避免组件内部访问时出错
    return result.length > 0 ? result : [{ title: '#', list: arr }];
  }, [level, provinces, citiesByProv, countiesByCity, selectedProv, selectedCity]);

  // 点击逻辑
  const onItemClick = (_key: string, item: Item) => {
    if (level === 1) {
      // 点击省：清空市、区状态，进入城市层
      setSelectedProv(item);
      setSelectedCity(null);
      setLevel(2);
    } else if (level === 2) {
      // 点击市：设置市，进入区层
      setSelectedCity(item);
      setLevel(3);
    } else {
      // 选择区/县完成
      setSelectedCounty(item);
      console.log('已选：', selectedProv, selectedCity, item);
    }
  };
  useEffect(() => {
    firstref.current = false;
    console.log(Props.initvalue);
    if (Props.initvalue) {
      if (Props.initvalue[1]) {
        setLevel(3);
      } else if (Props.initvalue[0]) {
        setLevel(2);
      }
      setSelectedProv(Props.initvalue[0]);
      setSelectedCity(Props.initvalue[1]);
      setSelectedCounty(Props.initvalue[2]);
    }
  }, []);
  useEffect(() => {
    if (!firstref.current) {
      Props.onchange([selectedProv, selectedCity, selectedCounty]);
    }
  }, [selectedProv, selectedCity, selectedCounty]);

  const onIndexClick = (key: string) => console.log('索引：', key);

  // 返回上级：同时清除下层状态
  const goBack = (index: number) => {
    if (index === 3) {
      setLevel(2);
      setSelectedCounty(null);
    } else if (index === 2) {
      setLevel(1);
      setSelectedCity(null);
      setSelectedCounty(null);
    }
  };
  return (
    <div className="custom_elevator">
      <View
        className="custom_elevator_header"
        onClick={(e) => {
          console.log('点击了返回', e.target.dataset.index);
          // 通过 data-index 获取点击的索引值
          goBack(e.target.dataset.index);
        }}
      >
        {selectedProv && (
          <View className={'default_but ' + (!selectedCity ? 'active_but' : '')} data-index={2}>
            {selectedProv.name}
          </View>
        )}
        {selectedCity && (
          <>
            <Text className="text_space">-</Text>
            <View className={'default_but ' + (!selectedCounty ? 'active_but' : '')} data-index={3}>
              {selectedCity.name}
            </View>
          </>
        )}
        {selectedCounty && (
          <>
            <Text>-</Text>
            <View className={'default_but active_but'}>{selectedCounty.name}</View>
          </>
        )}
      </View>
      <View className='tips_box'>
        <Text>
          {level === 1 && '请选择省份'}
          {level === 2 && `请选择城市：${selectedProv?.name}`}
          {level === 3 && `请选择区/县：${selectedCity?.name}`}
        </Text>
      </View>
      <Elevator
        key={level} // 强制组件重新挂载，避免内部状态冲突
        list={groupedData}
        onItemClick={onItemClick}
        onIndexClick={onIndexClick}
      />
    </div>
  );
};

export default ElevatorThreeLevel;
