import { AppMemberCardUserActivationDetailsVo } from '@/serves/member/card-upgrade/interface';
import { useMemo } from 'react';
import { UpgradeWay } from '../components/UpgradeConditionsList';

/**
 * 处理会员卡升级条件的自定义Hook
 * 将API返回的数据转换为UpgradeConditionsList组件需要的格式
 *
 * @param activationDetails 会员卡开通详情数据
 * @returns 处理后的升级条件数据和状态
 */
export function useUpgradeConditions(activationDetails?: AppMemberCardUserActivationDetailsVo) {
  // 转换数据格式，将API返回的数据转换为UpgradeConditionsList需要的格式
  const upgradeWays: UpgradeWay[] = useMemo(() => {
    if (!activationDetails?.activationModeList) {
      return [];
    }

    return activationDetails.activationModeList.map((mode, index) => {
      // 计算已完成的条件数量
      const finishedCount = mode.activationModeDetailsList.filter(
        (item) => item.finishStatus,
      ).length;

      return {
        title: `方式${index + 1}`,
        list: mode.activationModeDetailsList.map((item) => ({
          desc: item.description,
          isOk: item.finishStatus,
        })),
        count: finishedCount,
        total: mode.activationModeDetailsList.length,
      };
    });
  }, [activationDetails]);

  // 获取会员费用
  const memberFee = useMemo(() => {
    return activationDetails?.memberFee || 0;
  }, [activationDetails]);

  // 检查是否所有条件组中至少有一组全部完成
  const canUpgrade = useMemo(() => {
    if (!upgradeWays.length) return false;
    return upgradeWays.every((way) => way.count === way.total);
  }, [upgradeWays]);

  return {
    upgradeWays,
    memberFee,
    canUpgrade,
    hasConditions: upgradeWays.length > 0,
  };
}
