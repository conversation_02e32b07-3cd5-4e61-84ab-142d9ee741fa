import { RichText, Text, View } from '@tarojs/components';
import { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import './index.scss';

interface NoteDetail {
  id: string;
  title: string;
  content: string;
  htmlContent?: string; // 添加富文本HTML内容
  date: string;
}

const NotebookDetail = () => {
  const router = useRouter();
  const { id } = router.params;
  const [noteDetail, setNoteDetail] = useState<NoteDetail | null>(null);
  const [loading, setLoading] = useState(true);

  // 模拟获取笔记详情数据
  useEffect(() => {
    // 模拟API请求
    setTimeout(() => {
      const mockData: NoteDetail = {
        id: id || '1',
        title: '笔记详情',
        content:
          '这是笔记的详细内容，可以包含很多文字。这是一个长文本示例，用来测试文本的显示效果。'.repeat(
            5,
          ),
        htmlContent: `
          <div>
            <p>这是笔记的详细内容，可以包含很多文字。这是一个长文本示例，用来测试文本的显示效果。</p>
            <p>这是第二段落，包含一些格式化的内容。</p>
            <img src="https://placekitten.com/500/300" class="note-image" style="width: 90%;" />
            <p>这是图片下方的文字说明。</p>
          </div>
        `,
        date: '2023-06-15',
      };
      setNoteDetail(mockData);
      setLoading(false);
    }, 500);
  }, [id]);

  if (loading) {
    return (
      <View className="notebook-detail-loading">
        <Text>加载中...</Text>
      </View>
    );
  }

  if (!noteDetail) {
    return (
      <View className="notebook-detail-empty">
        <Text>未找到笔记</Text>
      </View>
    );
  }

  return (
    <View className="notebook-detail-container">
      <View className="notebook-detail-header">
        <Text className="notebook-detail-title">{noteDetail.title}</Text>
        <Text className="notebook-detail-date">{noteDetail.date}</Text>
      </View>
      <View className="notebook-detail-content">
        {noteDetail.htmlContent ? (
          <RichText className="notebook-detail-rich-text" nodes={noteDetail.htmlContent} />
        ) : (
          <Text className="notebook-detail-text">{noteDetail.content}</Text>
        )}
      </View>
    </View>
  );
};

export default NotebookDetail;
