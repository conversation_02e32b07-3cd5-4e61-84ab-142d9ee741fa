{"name": "miniprograms", "version": "1.0.0", "private": true, "description": "添星管家", "templateInfo": {"name": "taro-hooks@2x", "typescript": true, "css": "Sass", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp ", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch"}, "browserslist": ["defaults and fully supports es6-module", "maintained node versions"], "author": "", "dependencies": {"@babel/runtime": "^7.7.7", "@nutui/icons-react-taro": "^3.0.1", "@nutui/nutui-react-taro": "^3.0.8", "@tanstack/react-query": "^5.75.2", "@taro-hooks/plugin-react": "2", "@taro-hooks/shared": "2", "@tarojs/components": "4.0.9", "@tarojs/helper": "4.0.9", "@tarojs/plugin-framework-react": "4.0.9", "@tarojs/plugin-html": "^4.0.9", "@tarojs/plugin-platform-alipay": "4.0.9", "@tarojs/plugin-platform-h5": "4.0.9", "@tarojs/plugin-platform-jd": "4.0.9", "@tarojs/plugin-platform-qq": "4.0.9", "@tarojs/plugin-platform-swan": "4.0.9", "@tarojs/plugin-platform-tt": "4.0.9", "@tarojs/plugin-platform-weapp": "4.0.9", "@tarojs/react": "4.0.9", "@tarojs/runtime": "4.0.9", "@tarojs/shared": "4.0.9", "@tarojs/taro": "4.0.9", "@umijs/fabric": "^1.2.14", "pinyin-pro": "^3.26.0", "prettier": "^3.5.1", "react": "^18.0.0", "react-dom": "^18.0.0", "taro-hooks": "2", "taro-ui": "^3.3.0", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/preset-react": "^7.24.1", "@tarojs/cli": "4.0.9", "@tarojs/vite-runner": "4.0.9", "@types/react": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "@vitejs/plugin-react": "^4.1.0", "babel-plugin-import": "^1.13.8", "babel-preset-taro": "4.0.9", "eslint": "^8.12.0", "eslint-config-taro": "4.0.9", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "react-refresh": "^0.11.0", "sass": "^1.64.0", "stylelint": "^14.4.0", "terser": "^5.16.8", "typescript": "^5.1.0", "vite": "^4.2.0"}}