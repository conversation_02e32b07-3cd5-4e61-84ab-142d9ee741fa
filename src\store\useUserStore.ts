import { autoCreateSetters, autoUseShallow, create, Setter } from './index';
export interface UserInfo {
  /*用户编号 */
  userId: number;

  /*访问令牌 */
  accessToken: string;

  /*刷新令牌 */
  refreshToken: string;

  /*过期时间 */
  expiresTime: Record<string, unknown>;

  /*社交用户 openid */
  openid: string;
}
interface State {
  userInfo: UserInfo | null;
  accessToken: string;
  refreshToken: string;
}
interface SetState {
  setUserInfo: Setter;
  setAccessToken: Setter;
  setRefreshToken: Setter;
}
const useUserStore = autoUseShallow<State, SetState>(
  create(
    autoCreateSetters<State>({
      userInfo: null,
      accessToken: '',
      refreshToken: '',
    }),
  ),
);

export default useUserStore;
