/* 加载页面容器 */
.loading-page {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 999;
}

.loading-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  position: relative;
  width: 64px;
  height: 64px;
  animation: rotate 1.5s linear infinite;
}

.loading-dot {
  position: absolute;
  width: 30%;
  height: 30%;
  border-radius: 50%;
  background-color: #f00;
  transform-origin: center;
  opacity: 0.6;

  &:nth-child(1) {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 1s ease-in-out infinite;
  }

  &:nth-child(2) {
    bottom: 10%;
    left: 15%;
    animation: bounce 1s ease-in-out infinite 0.33s;
  }

  &:nth-child(3) {
    bottom: 10%;
    right: 15%;
    animation: bounce 1s ease-in-out infinite 0.66s;
  }
}

.loading-text {
  margin-top: 16px;
  font-size: 28px;
  color: #666;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
}
