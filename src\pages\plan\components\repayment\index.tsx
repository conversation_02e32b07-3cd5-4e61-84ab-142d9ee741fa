import EmptyContainer from '@/components/empty-container';
import SvgIcon from '@/pages/me/components/SvgIcon';
import { GetContractListRes, GetDebtMonthRes } from '@/serves/debt/interface';
import { formatDate } from '@/utils/date';
import { addMoney } from '@/utils/money';
import { Text, View } from '@tarojs/components';
import { FC, memo } from 'react';
import DebtList from '../debtList';
import './index.scss';

interface RepaymentProps {
  type?: 'debt' | 'credit';
  data?: GetDebtMonthRes;
  contractList?: GetContractListRes;
}

// TODO
// 1. 根据传入的值判断是债务还是债权
const Repayment: FC<RepaymentProps> = ({ type = 'debt', data, contractList }) => {
  const currentMonth = formatDate(new Date(), { template: 'YYYY年MM月' });

  const totalDebt = contractList?.data.reduce(
    (acc, curr) => addMoney(acc, curr.actualDebtReductionAmount),
    0,
  );
  const actualDebt = contractList?.data.reduce(
    (acc, curr) => addMoney(acc, curr.convertedDebtAmount),
    0,
  );

  return (
    <View className="repayment-container">
      <View className="repayment-container-item">
        <View className={`plan_repayment_bg plan-bg ${type}`}>
          {/* <View className="plan-bg"></View> */}
          {/* 详情 */}
          <View className="detail">
            <View className="detail-item">
              <Text>全部债务总额(元)</Text>
              <Text>{totalDebt?.toFixed(2) || '0.00'}</Text>
            </View>
            <View className="detail-item already-repayment">
              <Text>已化债金额(元)</Text>
              <Text>{actualDebt?.toFixed(2) || '0.00'}</Text>
            </View>
          </View>

          <View className={`info-container ${type}`}>
            <View className="dividing"></View>
            <View className="info">
              <View className="info-item">
                <Text>{currentMonth}</Text>
                <View className="info-item-tag">账单月</View>
              </View>
              <View className="info-item">
                <Text>{data?.data?.monthlyDebtReductionAmount || 0}</Text>
                <Text>应化债(元)</Text>
              </View>
              <View className="info-item">
                <Text>{data?.data?.debtReductionAmount || 0}</Text>
                <Text>已化债(元)</Text>
              </View>
            </View>
          </View>
        </View>
        {/* 金币 */}
        <View className="coin-container">
          <SvgIcon
            imageStyle={{ mode: 'widthFix' }}
            name="coinPng"
            className="coin-icon"
            size={85}
          />
        </View>
      </View>

      <View style={{ flex: 1, overflowY: 'auto' }}>
        {contractList?.data.length ? (
          <DebtList data={contractList?.data || []} />
        ) : (
          <EmptyContainer title="暂无债权" />
        )}
      </View>
    </View>
  );
};
export default memo(Repayment);
