import LoadingPage from '@/components/loadingPage';
import NumberKeyboardInput from '@/components/NumberKeyboardInput';
import { useEventChannel } from '@/hooks/useEventChannel';
import { formatDate } from '@/utils/date';
import { formatMoney } from '@/utils/money';
import { Cell } from '@nutui/nutui-react-taro';
import { Button, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useCallback, useEffect, useMemo, useState } from 'react';
import './index.scss';

// 定义接口增强类型安全
interface RepaymentParams {
  periodSeq?: string | number;
  actualDebtAmount?: string;
  outstandingAmount?: string;
  dueDate?: string;
}

const Repayment = () => {
  const encodedParams = useEventChannel<RepaymentParams>('manualRepaymentParams');

  const { periodSeq, actualDebtAmount, outstandingAmount, dueDate } = encodedParams ?? {};
  const [inputValue, setInputValue] = useState<string>(outstandingAmount || '');

  // 直接定义常量，不使用useMemo
  const minAmount = '100'; // 最低还款金额
  const maxAmount = outstandingAmount || '0'; // 最大还款金额

  // 当outstandingAmount变化时更新输入值
  useEffect(() => {
    if (outstandingAmount) {
      setInputValue(outstandingAmount);
    }
  }, [outstandingAmount]);

  // 显示提示信息
  const showMessage = useCallback((message: string): void => {
    Taro.showToast({
      title: message,
      icon: 'none',
    });

    setTimeout(() => {
      Taro.hideToast();
    }, 1000);
  }, []);

  // 处理支付
  const handlePay = useCallback(() => {
    // 验证参数完整性
    if (!outstandingAmount || !minAmount || !maxAmount) {
      showMessage('支付参数不完整');
      return;
    }

    // 验证支付金额
    if (!inputValue) {
      showMessage('请输入还款金额');
      return;
    }

    const inputValueNum = parseFloat(inputValue);
    const minAmountNum = parseFloat(minAmount);
    const maxAmountNum = parseFloat(maxAmount);

    if (isNaN(inputValueNum) || isNaN(minAmountNum) || isNaN(maxAmountNum)) {
      showMessage('金额格式错误');
      return;
    }

    if (inputValueNum < minAmountNum) {
      showMessage(`还款金额不能低于${minAmount}元`);
      return;
    }

    if (inputValueNum > maxAmountNum) {
      showMessage(`还款金额不能超过${maxAmount}元`);
      return;
    }

    // TODO 支付
    // Taro.requestPayment({
    //   timeStamp: '',
    //   nonceStr: '',
    //   package: '',
    //   paySign: '',
    // });
  }, [inputValue, minAmount, maxAmount, showMessage, outstandingAmount]);

  // 缓存格式化后的日期
  const formattedDate = useMemo(() => {
    if (!dueDate) return '';
    return formatDate(new Date(dueDate), {
      template: 'YYYY年MM月',
    });
  }, [dueDate]);

  // 缓存格式化后的金额
  const formattedOutstandingAmount = useMemo(
    () => formatMoney(outstandingAmount || '0'),
    [outstandingAmount],
  );
  const formattedActualDebtAmount = useMemo(
    () => formatMoney(actualDebtAmount || '0'),
    [actualDebtAmount],
  );

  // 直接在渲染中计算按钮文本，不使用useMemo
  const payButtonText = `支付${inputValue}元`;

  if (!encodedParams) {
    // Taro.navigateBack();
    return <LoadingPage />;
  }

  return (
    <View className="repayment-container">
      <Cell.Group divider={false}>
        <Cell title="期数" extra={`第${periodSeq || ''}期（${formattedDate}）`} />
        <Cell title="剩余未化债金额" extra={`${formattedOutstandingAmount}元`} />
        <Cell title="已化债金额" extra={`${formattedActualDebtAmount}元`} />
      </Cell.Group>

      <View className="repayment-chart">
        <View className="repayment-chart-title">转账还款</View>
        <View className="repayment-chart-content">
          <NumberKeyboardInput
            defaultValue={outstandingAmount || ''}
            minValue={minAmount}
            maxValue={maxAmount}
            placeholder={`最低还款${minAmount}元`}
            onValueChange={setInputValue}
            onError={showMessage}
          />
        </View>
        <View className="repayment-chart-desc">
          手动结清本期剩余化债金额，可以手动修改金额（{minAmount}-{maxAmount}元）
        </View>
      </View>

      <View className="repayment-btn">
        <Button onClick={handlePay} className="repayment-btn-confirm">
          {payButtonText}
        </Button>
      </View>
    </View>
  );
};

export default Repayment;
