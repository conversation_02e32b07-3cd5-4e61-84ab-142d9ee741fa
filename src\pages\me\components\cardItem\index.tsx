import { type SvgName } from '@/utils/svgLoader';
import { Text, View, ViewProps } from '@tarojs/components';
import SvgIcon from '../SvgIcon';
import './index.scss';

interface IconItemProps extends ViewProps {
  icon: SvgName; // 图标地址
  text: string; // 文本
  className?: string; // 文本样式
  size?: number; // 图标大小
}

const IconItem = ({ icon, text, className, size = 20, ...rest }: IconItemProps) => {
  return (
    <View className={`icon-item ${className}`} {...rest}>
      <SvgIcon name={icon} className="icon" size={size} />
      <Text>{text}</Text>
      <SvgIcon name="arrow-right-small" size={6} />
    </View>
  );
};

export default IconItem;
