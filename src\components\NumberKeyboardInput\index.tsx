import { Input, NumberKeyboard } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import { ITouchEvent } from '@tarojs/components/types/common';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import './index.scss';

export interface NumberKeyboardInputProps {
  defaultValue: string; // 初始值
  minValue?: string; // 最小值
  maxValue?: string; // 最大值
  placeholder?: string; // 输入框占位文本
  onValueChange?: (value: string) => void; // 值变化回调
  onError?: (message: string) => void; // 错误回调
  showEditButtonByDefault?: boolean; // 默认是否显示编辑按钮
}

const NumberKeyboardInput: React.FC<NumberKeyboardInputProps> = ({
  defaultValue,
  minValue = '0',
  maxValue,
  placeholder,
  onValueChange,
  onError,
  showEditButtonByDefault = true,
}) => {
  const [inputValue, setInputValue] = useState(defaultValue);
  const [showEditButton, setShowEditButton] = useState(showEditButtonByDefault);
  const [visible, setVisible] = useState(false);
  const inputRef = useRef<any>(null);
  const [errorState, setErrorState] = useState<string | null>(null);

  // 当错误状态改变时才触发onError回调
  useEffect(() => {
    if (errorState) {
      onError?.(errorState);
      // 清除错误状态，防止重复触发
      setErrorState(null);
    }
  }, [errorState, onError]);

  // 缓存最小和最大值的数字表示
  const minValueNum = useMemo(() => (minValue ? parseFloat(minValue) : 0), [minValue]);

  const maxValueNum = useMemo(
    () => (maxValue ? parseFloat(maxValue) : Number.MAX_SAFE_INTEGER),
    [maxValue],
  );

  // 格式化金额，限制小数位数
  const formatAmount = useCallback((value: string): string => {
    // 如果为空返回空字符串
    if (!value) return '';

    // 匹配有效的金额格式: 整数部分和最多两位小数
    const match = value.match(/^\d+(\.\d{0,2})?/);
    return match ? match[0] : '';
  }, []);

  // 验证金额是否有效
  const validateAmount = useCallback(
    (amount: string): boolean => {
      // 如果为空
      if (!amount) return false;

      // 转换为数字进行比较
      const numAmount = parseFloat(amount);

      // 检查是否为有效数字
      if (isNaN(numAmount)) return false;

      // 检查是否小于最低金额
      if (numAmount < minValueNum) {
        setErrorState(`金额不能低于${minValue}元`);
        return false;
      }

      // 检查是否大于最大金额
      if (maxValue && numAmount > maxValueNum) {
        setErrorState(`金额不能超过${maxValue}元`);
        return false;
      }

      return true;
    },
    [minValue, maxValue, minValueNum, maxValueNum],
  );

  // 处理输入值变化并通知父组件
  const handleValueChange = useCallback(
    (newValue: string) => {
      setInputValue(newValue);
      onValueChange?.(newValue);
    },
    [onValueChange],
  );

  // 数字键盘按键输入处理
  const onChange = useCallback(
    (value: string) => {
      // 不允许以小数点开头
      if (inputValue === '' && value === '.') {
        return;
      }

      // 检查如果输入的是小数点，并且已经有小数点了，则不添加
      if (value === '.' && inputValue.includes('.')) {
        return;
      } else if (value === '收起') {
        handleKeyboardClose();
        return;
      }

      // 连续输入，将新数字追加到现有值
      setInputValue((prev) => {
        // 如果是第一次输入，或者当前值是默认值，则替换
        if (prev === defaultValue && !showEditButton) {
          const newValue = formatAmount(value);
          onValueChange?.(newValue);
          return newValue;
        }

        // 否则追加数字并格式化
        const newValue = formatAmount(prev + value);

        // 检查小数点后的位数，如果超过两位，不添加
        const parts = newValue.split('.');
        if (parts.length > 1 && parts[1].length > 2) {
          return prev;
        }

        onValueChange?.(newValue);
        return newValue;
      });
    },
    [inputValue, defaultValue, showEditButton, formatAmount, onValueChange],
  );

  // 删除按钮处理
  const onDelete = useCallback(() => {
    // 删除最后一个字符
    setInputValue((prev) => {
      const newValue = prev.length <= 1 ? '' : prev.slice(0, -1);
      onValueChange?.(newValue);
      return newValue;
    });
  }, [onValueChange]);

  // 清空输入框
  const handleClear = useCallback((e: ITouchEvent) => {
    // 阻止事件冒泡，防止触发输入框的点击事件
    e.stopPropagation();
    setInputValue('');
    onValueChange?.('');
  }, [onValueChange]);

  // 修改金额按钮点击处理
  const handleChangeAmount = useCallback(() => {
    // 隐藏修改金额按钮
    setShowEditButton(false);
    // 显示数字键盘
    setVisible(true);
    // 清空输入值，准备输入新值
    handleValueChange('');
    // 防止系统键盘弹出
    setTimeout(() => {
      inputRef.current?.focus();
      inputRef.current?.blur();
    }, 10);
  }, [handleValueChange]);

  // 防止Input获取焦点时弹出系统键盘
  const handleInputFocus = useCallback(
    (e: any) => {
      e.preventDefault();
      inputRef.current?.blur();
      // 确保数字键盘显示
      if (!showEditButton) {
        setVisible(true);
      }
    },
    [showEditButton],
  );

  // 输入框点击处理
  const handleInputClick = useCallback(() => {
    if (!showEditButton) {
      setVisible(true);
    } else {
      // 如果未处于编辑状态，点击输入框相当于点击修改金额按钮
      handleChangeAmount();
    }
  }, [showEditButton, handleChangeAmount]);

  // 键盘关闭处理 - 只有通过点击"收起"按钮才能关闭键盘
  const handleKeyboardClose = useCallback(() => {
    setVisible(false);

    // 如果用户没有输入任何内容，则恢复原始值
    if (inputValue === '') {
      handleValueChange(defaultValue);
    } else if (inputValue !== defaultValue) {
      // 验证金额是否有效
      const isValid = validateAmount(inputValue);

      // 如果无效，恢复原始值
      if (!isValid) {
        handleValueChange(defaultValue);
      }
    }

    // 恢复修改金额按钮
    setShowEditButton(true);
  }, [inputValue, defaultValue, validateAmount, handleValueChange]);

  // 显示清空按钮的条件：有输入值且不显示编辑按钮
  const showClearButton = !showEditButton && inputValue !== '';

  return (
    <View className="number-keyboard-input">
      <View className="number-keyboard-input-content">
        <View className="number-keyboard-input-field" onClick={handleInputClick}>
          <Input
            ref={inputRef}
            placeholder={placeholder}
            type="digit"
            value={inputValue}
            readOnly={true}
            onFocus={handleInputFocus}
          />
          {showClearButton && (
            <View 
              className="number-keyboard-input-clear-wrapper"
              onClick={handleClear}
              catchMove
            >
              <Text className="number-keyboard-input-clear">&#10005;</Text>
            </View>
          )}
        </View>
        {showEditButton && (
          <View className="number-keyboard-input-edit-btn" onClick={handleChangeAmount}>
            修改金额
          </View>
        )}
      </View>

      <NumberKeyboard
        visible={visible}
        type="rightColumn"
        custom={['.', '收起']}
        onChange={onChange}
        onDelete={onDelete}
        // 移除onClose事件处理，只通过收起按钮关闭
        onConfirm={handleKeyboardClose}
      />
    </View>
  );
};

export default NumberKeyboardInput;
