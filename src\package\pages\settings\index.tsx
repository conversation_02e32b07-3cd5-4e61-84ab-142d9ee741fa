import ListItem from '@/pages/me/components/listItem';
import SvgIcon from '@/pages/me/components/SvgIcon';
import { Button, Cell } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import './index.scss';

const Settings = () => {
  const settingsGroups = [
    [
      {
        title: '个人信息',
        icon: <SvgIcon name="me" size={20} />,
        onClick: () => {
          Taro.navigateTo({
            url: '/package/pages/settings/profile/index',
          });
        },
      },
      {
        title: '实名认证',
        icon: <SvgIcon name="sm" size={20} />,
        onClick: () => {
          Taro.navigateTo({
            url: '/package/pages/settings/realName/index',
          });
        },
      },
      {
        title: '绑定添星商城账户',
        icon: <SvgIcon name="bindMallAccount" size={20} />,
        onClick: () => {
          Taro.navigateTo({
            url: '/package/pages/settings/bindMallAccount/index',
          });
        },
      },
      {
        title: '银行卡',
        icon: <SvgIcon name="yh" size={20} />,
        onClick: () => {
          Taro.navigateTo({
            url: '/package/pages/bankCard/index',
          });
        },
      },
      { title: '发票管理', icon: <SvgIcon name="fp" size={20} /> },
      {
        title: '兑换码',
        icon: <SvgIcon name="redemption-code" size={20} />,
        onClick: () => {
          Taro.navigateTo({
            url: '/package/pages/settings/redemptionCode/index',
          });
        },
      },
    ],
    [
      {
        title: '关于添星',
        icon: <SvgIcon name="about" size={20} />,
        onClick: () => {
          Taro.navigateTo({
            url: '/package/pages/aboutTx/index',
          });
        },
      },
      {
        title: '注销账号',
        icon: <SvgIcon name="zx" size={20} />,
        onClick: () => {
          Taro.navigateTo({ url: '/package/pages/settings/logOut/index' });
        },
      },
      { title: '联系我们', icon: <SvgIcon name="lx" size={20} /> },
      // { title: '资质证明', icon: <SvgIcon name="zx" size={20} /> },
      // {
      //   title: '用户服务协议',
      //   icon: <SvgIcon name="lx" size={20} />,
      //   onClick: () => {
      //     Taro.navigateTo({
      //       url: '/pages/qaSmart/index?url=https://guanjia.hunantianxing.com/userPrivacy/userServes.htm',
      //     });
      //   },
      // },
      // {
      //   title: '用户隐私政策',
      //   icon: <SvgIcon name="lx" size={20} />,
      //   onClick: () => {
      //     Taro.navigateTo({
      //       url: '/pages/qaSmart/index?url=https://guanjia.hunantianxing.com/userPrivacy/privacy.htm',
      //     });
      //   },
      // },
    ],
  ];

  return (
    <View className="settings">
      <View className="cell-group">
        {settingsGroups.map((group, groupIndex) => (
          <Cell.Group key={groupIndex}>
            {group.map((item, itemIndex) => (
              <ListItem
                key={itemIndex}
                icon={item.icon}
                title={item.title}
                onClick={item.onClick}
              />
            ))}
          </Cell.Group>
        ))}
      </View>
      <Button className="logout-btn">退出登录</Button>
    </View>
  );
};

export default Settings;
