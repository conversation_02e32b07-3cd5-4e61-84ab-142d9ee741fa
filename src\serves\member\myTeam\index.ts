import { serveIp } from '@/serves/config';
import http from '@/serves/http';
import { GetMemberGroupInfoRes } from './interface';

/**
 * 获取会员团队信息
 * @param {string} memberUserId 会员ID
 * @param {string} pageNo 页码，从 1 开始
 * @param {string} pageSize 每页条数，最大值为 100
 * @returns
 */
export function getMemberGroupInfo(
  memberUserId: number,
  pageNo: number,
  pageSize: number,
): Promise<GetMemberGroupInfoRes> {
  return http.get(`${serveIp.ip}/app-api/member/service/memberGroupInfo`, {
    memberUserId,
    pageNo,
    pageSize,
  });
}
