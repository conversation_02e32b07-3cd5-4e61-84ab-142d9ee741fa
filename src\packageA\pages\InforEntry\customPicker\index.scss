.customPicker_contentBox {
  width: 100vw;
  height: auto;
  max-height: calc(87vh - 96px);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .customPicker_content {
    width: 100%;
    max-height: calc(
      87vh - 96px - env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1)
    );
    padding: 0 32px;
    box-sizing: border-box;
    .customPicker_content_scroll {
      height: 100%;
      max-height: calc(
        87vh - 96px - env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1)
      );
      overflow: auto;
    }
  }
}
.nut-popup-title {
  justify-content: start;
}
