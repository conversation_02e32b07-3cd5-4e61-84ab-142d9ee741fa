import { serveIp } from './config';
import HTTP from './http';
import { CommonResponse } from './interface/miniProgram';
interface SocialLoginRes {
  env: any;
  scvtDO?: any;
  /*用户编号 */
  userId: number;

  /*访问令牌 */
  accessToken: string;

  /*刷新令牌 */
  refreshToken: string;

  /*过期时间 */
  expiresTime: Record<string, unknown>;

  /*社交用户 openid */
  openid: string;
}

/**
 * 社交快捷登录，使用 code 授权码
 * @param {object} params 用户 APP - 社交快捷登录 Request VO，使用 code 授权码
 * @param {number} params.type 社交平台的类型，34：微信小程序
 * @param {string} params.code 授权码
 * @param {string} params.state state
 * @returns
 */
interface resultType {
  repaymentTime: number;
  c: number;
  debtAmount: number;
  e: number;
  f: number;
  n: number;
  p: number;
  s: number;
  t: number;
  v: number;
  scvtDO: ScvtDO;
}

interface ScvtDO {
  adaptationCase: string;
  code: string;
  coreGoals: string;
  coreGoalsDescribe: string;
  createdTime: number;
  creator: null;
  deleted: number;
  detailDescribe: string;
  id: number;
  keyWords: string;
  longTermGoals: string;
  longTermGoalsDescribe: string;
  nickName: string;
  overview: string;
  portrait: string;
  strategicValue: string;
  tenantId: null;
  updatedTime: number;
  updater: null;
}

export interface HistoryRes {
  /* */
  code: number;

  /* */
  data: {
    /*问卷调研结果id */
    id: number;

    /*还款时间 */
    repaymentTime: number;

    /*欠款金额 */
    debtAmount: Record<string, unknown>;

    /*scvt模型测算结果 */
    scvt: string;

    /*调研类型 */
    type: string;

    /*最后更新时间 */
    updateTime: Record<string, unknown>;

    /*s所占比例 */
    s: Record<string, unknown>;

    /*n所占比例 */
    n: Record<string, unknown>;

    /*c所占比例 */
    c: Record<string, unknown>;

    /*p所占比例 */
    p: Record<string, unknown>;

    /*e所占比例 */
    e: Record<string, unknown>;

    /*v所占比例 */
    v: Record<string, unknown>;

    /*t所占比例 */
    t: Record<string, unknown>;

    /*f所占比例 */
    f: Record<string, unknown>;
  }[];

  /* */
  msg: string;
}

// Response interface
export interface HisDetailRes {
  /* */
  code: number;

  /* */
  data: {
    /*还款时间 */
    repaymentTime: number;

    /* */
    scvtDO: {
      /* */
      id: number;

      /* */
      code: string;

      /* */
      nickName: string;

      /* */
      portrait: string;

      /* */
      overview: string;

      /* */
      coreGoals: string;

      /* */
      coreGoalsDescribe: string;

      /* */
      longTermGoals: string;

      /* */
      longTermGoalsDescribe: string;

      /* */
      detailDescribe: string;

      /* */
      strategicValue: string;

      /* */
      adaptationCase: string;

      /* */
      keyWords: string;

      /* */
      createTime: Record<string, unknown>;

      /* */
      updateTime: Record<string, unknown>;

      /* */
      creator: string;

      /* */
      updater: string;

      /* */
      deleted: number;

      /* */
      tenantId: number;
    };

    /*欠款金额 */
    debtAmount: Record<string, unknown>;

    /*调研类型 */
    type: string;

    /* */
    s: Record<string, unknown>;

    /* */
    n: Record<string, unknown>;

    /* */
    c: Record<string, unknown>;

    /* */
    p: Record<string, unknown>;

    /* */
    e: Record<string, unknown>;

    /* */
    v: Record<string, unknown>;

    /* */
    t: Record<string, unknown>;

    /* */
    f: Record<string, unknown>;
  };

  /* */
  msg: string;
}

/**
 * 查询历史结果详情
 * @param {string} id
 * @returns
 */
export function reportHistoryDetail(id: string | number): Promise<HisDetailRes> {
  return HTTP.get(serveIp.ip + `/app-api/report/history/${id}`);
}

export function socialLogin(param: {
  type: string;
  code: string;
}): Promise<CommonResponse<SocialLoginRes>> {
  return HTTP.post(serveIp.ip + `/app-api/member/auth/social-login`, param);
}
export function start(): Promise<SocialLoginRes> {
  return HTTP.get(serveIp.ip + `/app-api/report/start`);
}
export function result(url?: string): Promise<{ data?: resultType }> {
  return HTTP.get(serveIp.ip + `/app-api/report/result${url || ''}`);
}
/**
 * 查询问卷调研结果历史
 * @param {string} userId 用户编号
 * @returns
 */
export function getReportHistory(userId: string): Promise<HistoryRes> {
  return HTTP.get(serveIp.ip + `/app-api/report/history`, {
    userId,
  });
}
