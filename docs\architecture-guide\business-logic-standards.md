# 业务逻辑Hook划分标准

## 1. 业务逻辑分类

### 1.1 API调用逻辑
**定义**：与后端服务交互的逻辑

**判断标准**：
- 涉及网络请求
- 需要错误处理和加载状态管理
- 通常返回Promise或使用async/await

**BindMallAccount 案例**：
```typescript
// ✅ API调用逻辑示例
export const useBindMallAccountLogic = () => {
  const sendVerificationCode = useCallback(async (
    phone: string,
    onSuccess?: () => void,
    onError?: (error: any) => void
  ) => {
    try {
      // API调用
      await api.sendVerificationCode(phone);
      
      // 成功处理
      Taro.showToast({ title: '验证码已发送', icon: 'success' });
      onSuccess?.();
    } catch (error) {
      // 错误处理
      Taro.showToast({ title: '发送失败，请重试', icon: 'none' });
      onError?.(error);
    }
  }, []);
  
  return { sendVerificationCode };
};
```

### 1.2 数据处理逻辑
**定义**：对数据进行转换、验证、格式化的逻辑

**判断标准**：
- 纯函数特性
- 不涉及副作用
- 可以独立测试

```typescript
// ✅ 数据处理逻辑示例
export const useDataProcessing = () => {
  const formatPhoneNumber = useCallback((phone: string) => {
    return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3');
  }, []);
  
  const validateForm = useCallback((data: FormData) => {
    const errors: Record<string, string> = {};
    
    if (!isPhoneNumber(data.phone)) {
      errors.phone = '请输入正确的手机号';
    }
    
    if (data.code.length !== 6) {
      errors.code = '验证码必须为6位数字';
    }
    
    return { isValid: Object.keys(errors).length === 0, errors };
  }, []);
  
  return { formatPhoneNumber, validateForm };
};
```

### 1.3 副作用操作逻辑
**定义**：涉及外部系统交互或状态变更的逻辑

**判断标准**：
- 涉及DOM操作、定时器、订阅等
- 可能影响组件外部状态
- 需要清理资源

```typescript
// ✅ 副作用操作逻辑示例
export const useSideEffects = () => {
  const showSuccessToast = useCallback((message: string) => {
    Taro.showToast({
      title: message,
      icon: 'success',
      duration: 2000,
    });
  }, []);
  
  const navigateToPage = useCallback((url: string) => {
    Taro.navigateTo({ url });
  }, []);
  
  return { showSuccessToast, navigateToPage };
};
```

## 2. 业务逻辑Hook与状态管理Hook协作模式

### 2.1 回调协作模式
业务逻辑Hook通过回调函数与状态管理Hook协作

```typescript
// 主组件中的协作示例
const BindMallAccount = () => {
  // 状态管理
  const { 
    phone, code, isPhoneValid, 
    setCodeSentStatus, setBoundStatus 
  } = useBindMallAccountState();
  
  // 业务逻辑
  const { sendVerificationCode, bindMallAccount } = useBindMallAccountLogic();
  
  // 协作处理
  const handleSendCode = async () => {
    await sendVerificationCode(
      phone,
      () => setCodeSentStatus(true),  // 成功回调
      (error) => console.error(error) // 失败回调
    );
  };
  
  const handleBind = async () => {
    await bindMallAccount(
      phone,
      code,
      () => setBoundStatus(true)      // 成功回调
    );
  };
  
  return (
    <UnboundForm 
      onSendCode={handleSendCode}
      onBind={handleBind}
    />
  );
};
```

### 2.2 参数传递模式
业务逻辑Hook接收状态作为参数，保持纯函数特性

```typescript
// ✅ 推荐的参数传递模式
export const useBindMallAccountLogic = () => {
  const bindMallAccount = useCallback(async (
    phone: string,
    code: string,
    isValid: boolean,
    onSuccess?: () => void
  ) => {
    if (!isValid) return;
    
    try {
      await api.bindAccount({ phone, code });
      onSuccess?.();
    } catch (error) {
      // 错误处理
    }
  }, []);
  
  return { bindMallAccount };
};

// ❌ 避免的耦合模式
export const useBindMallAccountLogic = (stateHook: any) => {
  const bindMallAccount = useCallback(async () => {
    // 直接访问状态Hook，造成耦合
    const { phone, code } = stateHook;
    // ...
  }, [stateHook]);
};
```

## 3. 业务逻辑Hook设计原则

### 3.1 纯函数优先
尽可能设计为纯函数，便于测试和复用

### 3.2 错误处理统一
统一的错误处理策略和用户反馈

### 3.3 异步操作规范
```typescript
// ✅ 规范的异步操作设计
export const useAsyncOperation = () => {
  const executeOperation = useCallback(async (
    params: OperationParams,
    callbacks?: {
      onSuccess?: (result: any) => void;
      onError?: (error: any) => void;
      onFinally?: () => void;
    }
  ) => {
    try {
      const result = await api.operation(params);
      callbacks?.onSuccess?.(result);
      return result;
    } catch (error) {
      callbacks?.onError?.(error);
      throw error;
    } finally {
      callbacks?.onFinally?.();
    }
  }, []);
  
  return { executeOperation };
};
```

### 3.4 可测试性设计
```typescript
// ✅ 便于测试的设计
export const useBusinessLogic = (dependencies?: {
  apiClient?: ApiClient;
  validator?: Validator;
}) => {
  const api = dependencies?.apiClient || defaultApiClient;
  const validate = dependencies?.validator || defaultValidator;
  
  const processData = useCallback(async (data: any) => {
    const validationResult = validate(data);
    if (!validationResult.isValid) {
      throw new Error('Validation failed');
    }
    
    return await api.process(data);
  }, [api, validate]);
  
  return { processData };
};
```
