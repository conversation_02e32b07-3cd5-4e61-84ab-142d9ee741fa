import { Text, View } from '@tarojs/components';
import './index.scss';

interface InfoItemProps {
  topText: string;
  bottomText: string;
  onClick?: () => void;
}

const InfoItem = (props: InfoItemProps) => {
  return (
    <View className="info-item" onClick={props.onClick}>
      <Text className="top">{props.topText}</Text>
      <Text className="bottom">{props.bottomText}</Text>
    </View>
  );
};

export default InfoItem;
