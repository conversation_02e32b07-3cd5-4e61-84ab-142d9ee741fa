/**
 * @description 合同金额计算相关接口类型
 */

/**
 * @description 合同金额计算结果 Request VO
 */
export interface ContractMoneyCalculateVO {
  /**
   * @description 委案ID
   */
  caseEntrustId: number;
  /**
   * @description 化债期数（月，必须>0）
   */
  debtReductionPeriods: number;
  /**
   * @description 化债总金额（元）
   */
  totalDebtReductionAmount?: number;
  /**
   * @description 实际减免金额（元）
   */
  actualReductionAmount?: number;
  /**
   * @description 实际化债总额（元）
   */
  actualDebtReductionAmount?: number;
  /**
   * @description 每月应化债金额（元）
   */
  monthlyDebtReductionAmount?: number;
  /**
   * @description 委案金额（元）
   */
  entrustedAmount?: number;
  /**
   * @description 本金减免金额（元）
   */
  principalReductionAmount?: number;
  /**
   * @description 利息滞纳金总额（元）
   */
  interestAndLateFeeAmount?: number;
  /**
   * @description 利息滞纳金减免金额（元）
   */
  interestReductionAmount?: number;
}

/**
 * @description 合同金额计算结果 Response VO
 */
export interface ContractMoneyCalculateRes extends ContractMoneyCalculateVO {}
