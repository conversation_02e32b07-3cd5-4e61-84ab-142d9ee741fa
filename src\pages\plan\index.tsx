import PageContainerTemp from '@/components/pageContainerTemp';
import useRequestWithLifecycle from '@/hooks/use-request-with-lifecycle';
import useTabBar from '@/hooks/use-tab-bar';
import Repayment from '@/pages/plan/components/repayment';
import { getContractList, getDebtMonth } from '@/serves/debt';
import useUserStore from '@/store/useUserStore';
import { Tabs } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import { useState } from 'react';
import './index.scss';

const Index = () => {
  useTabBar(1);

  return (
    <PageContainerTemp title="计划" showTabbar>
      <View className="tabs-container">
        <TabsOfList />
      </View>
    </PageContainerTemp>
  );
};

const TabsOfList = () => {
  const [tabValue, setTabValue] = useState<string | number>(0);

  const { userInfo } = useUserStore((state) => state);
  //  查询当月化债情况
  const { data } = useRequestWithLifecycle(() => getDebtMonth(userInfo?.userId), {
    cacheKey: 'debtMonth',
    refreshDeps: [userInfo?.userId],
    ready: !!userInfo?.userId,
    refreshOnShow: true, // 页面显示时自动刷新
  });

  // 根据会员id获取债务合同列表
  const { data: contractList } = useRequestWithLifecycle(() => getContractList(userInfo?.userId), {
    cacheKey: 'contractList',
    refreshDeps: [userInfo?.userId],
    ready: !!userInfo?.userId,
    refreshOnShow: true, // 页面显示时自动刷新
  });

  return (
    <Tabs
      duration={0}
      value={tabValue}
      onChange={(value) => {
        setTabValue(value);
      }}
      tabStyle={{ padding: 0 }}
    >
      <Tabs.TabPane title="我的债务" value={0}>
        <Repayment data={data} contractList={contractList} />
      </Tabs.TabPane>
      <Tabs.TabPane title="我的债权" value={1}>
        <Repayment type="credit" />
        {/* <EmptyContainer title="暂无债权" /> */}
      </Tabs.TabPane>
    </Tabs>
  );
};
export default Index;
