// 结果弹窗
.result-modal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 32px;
  border-radius: 16px * 2;

  &.loading {
    .result-modal-icon {
      width: 98px * 2;
      height: 98px * 2;
      flex-shrink: 0;
      position: relative;

      .back-gif {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 20px * 2;
        background: linear-gradient(
          180deg,
          rgba(255, 147, 147, 1) 0%,
          // 顶部完全不透明
          rgba(255, 147, 147, 0.8) 25%,
          // 25%位置稍微透明
          rgba(255, 147, 147, 0.5) 50%,
          // 中间位置半透明
          rgba(255, 147, 147, 0.2) 75%,
          // 75%位置更透明
          rgba(255, 147, 147, 0) 100% // 底部完全透明
        );
        opacity: 0.4;
        animation: back-gif 2s linear infinite;
      }

      @keyframes back-gif {
        0% {
          width: 75%;
          transform: translate(-50%, -180%);
        }
        50% {
          width: 100%;
          transform: translate(-50%, -50%);
        }
        100% {
          width: 90%;
          height: 14px * 2;
          transform: translate(-50%, 90%);
        }
      }
    }
  }

  .result-modal-icon {
    width: 131px * 2;
    height: 62px * 2;
    flex-shrink: 0;
  }

  text:first-of-type {
    color: #131313;
    text-align: right;
    font-size: 18px * 2;
    font-weight: 500;
  }

  text:last-of-type {
    color: #9d9d9d;
    font-size: 14px * 2;
  }
}
// loading 弹窗样式
.nut-dialog-outer {
  &.loading {
    border-radius: 16px * 2;
    background: linear-gradient(180deg, #ffe2e2 0%, #fff 50%);
  }
}
// 关闭按钮样式
.nut-dialog-close-top-right {
  color: #7e828f !important;
}
