// components/StepList/index.jsx
import Arrow from '@/components/Arrow';
//  import { ArrowDown, ArrowUp } from '@nutui/icons-react-taro';

import EmptyContainer from '@/components/empty-container';
import { GetRepaymentPlanRes } from '@/serves/debt/interface';
import useDict from '@/serves/dict/hooks';
import { Tag } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useCallback, useMemo, useState } from 'react';
import StepItem from '../stepItem';
import './index.scss';

const manualRepayment = (item: GetRepaymentPlanRes['data']['list'][0]) => {
  const params = {
    periodSeq: item.periodSeq, // 期数
    actualDebtAmount: item.actualDebtAmount, // 实际化债金额
    outstandingAmount: item.outstandingAmount, // 剩余未结清金额
    dueDate: item.dueDate, // 应还款日期
  };
  // const encodedParams = encodeURIComponent(JSON.stringify(params));
  Taro.navigateTo({
    url: `/packageB/pages/repayment/index`,
    success: (res: any) => {
      res.eventChannel.emit('manualRepaymentParams', params);
    },
  });
};

const statusMap = {
  1: {
    status: 'undone',
    value: '待还款',
    tag: 'default',
    background: 'rgba(102, 102, 102, 0.10)',
    color: '#666666',
    button: (item: GetRepaymentPlanRes['data']['list'][0]) => (
      <View
        className="manual-btn"
        onClick={() => {
          manualRepayment(item);
        }}
      >
        手动还款
      </View>
    ),
  },
  2: {
    status: 'done',
    value: '已结清',
    tag: 'success',
    background: 'rgba(75, 129, 238, 0.10)',
    color: '#4B81EE',
  },
  4: {
    status: 'unsettled',
    value: '未结清',
    background: 'rgba(207, 48, 48, 0.10);',
    color: '#CF3030',
    button: (item) => (
      <View
        className="manual-btn"
        onClick={() => {
          console.log(item);
          manualRepayment(item);
        }}
      >
        手动结清
      </View>
    ),
  },
  3: {
    status: 'toward',
    value: '进行中',
    background: 'rgba(251, 160, 48, 0.10)',
    color: '#FBA030',
    button: (item) => (
      <View
        className="manual-btn"
        onClick={() => {
          manualRepayment(item);
        }}
      >
        手动还款
      </View>
    ),
  },
};

export default function StepList({ data }: { data: GetRepaymentPlanRes['data'] }) {
  const { dictData } = useDict();
  const [isShowMore, setIsShowMore] = useState(false);
  const repaymentPlanStatus = useMemo(() => {
    return dictData?.filter((item) => item.dictType === 'repayment_status');
  }, [dictData]);

  const getPaymentPlanStatus = useCallback(
    (statusCode: number) => {
      return repaymentPlanStatus?.find((item) => item.value === statusCode + '')?.label;
    },
    [repaymentPlanStatus],
  );

  return (
    <>
      <View className={`debt-plan-list ${isShowMore ? 'debt-plan-list-more' : ''}`}>
        <View className="debt-plan-list-header">还款计划</View>

        {data.list.length > 0 ? (
          data.list?.map((item, index) => {
            return (
              <StepItem
                className="debt-plan-list-item"
                key={index}
                status={item.statusCode === 1 ? 'undone' : ''}
                left={
                  <View className="debt-plan-list-item-left">
                    <Text>{item.periodSeq}期</Text>
                    <Text>{item.dueDate.replace(/-/g, '/')}</Text>
                  </View>
                }
                right={
                  <View className="debt-plan-list-item-right">
                    <View className="top">
                      <Text>￥{item.principal}</Text>
                      <Tag
                        background={statusMap[item.statusCode].background}
                        color={statusMap[item.statusCode].color}
                        style={{
                          height: '18px',
                          padding: '0 6px',
                        }}
                      >
                        {getPaymentPlanStatus(item.statusCode)}
                      </Tag>
                      {statusMap[item.statusCode]?.button
                        ? statusMap[item.statusCode]?.button(item)
                        : null}
                    </View>
                    {item.statusCode !== 1 && (
                      <View className="detail-container">
                        <Text className="details">消费化债{item.actualDebtAmount ?? 0}元</Text>
                        {item.outstandingAmount > 0 && (
                          <Text className="details">剩余{item.outstandingAmount}元</Text>
                        )}
                      </View>
                    )}
                  </View>
                }
              />
            );
          })
        ) : (
          <EmptyContainer title="暂无还款计划" />
        )}
      </View>
      <View
        className="more-control"
        onClick={() => {
          setIsShowMore(!isShowMore);
        }}
      >
        {isShowMore ? <Arrow up={true} size={14} /> : <Arrow up={false} size={14} />}
        <Text>查看更多</Text>
      </View>
    </>
  );
}
