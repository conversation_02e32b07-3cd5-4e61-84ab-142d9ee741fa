import { svgStore } from '@/utils/svgLoader';
import { Dialog, Image } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import './index.scss';

/**
 * 结果模态框类型
 * success - 成功
 * fail - 失败
 * loading - 加载中
 */
type ModalType = 'success' | 'fail' | 'loading';

/**
 * 类型配置接口
 */
interface TypeConfig {
  icon: string; // 图标路径
  title?: string; // 标题文本
  desc?: string; // 描述文本
}

/**
 * 结果模态框组件属性接口
 */
interface ResultModalProps {
  /**
   * 是否显示模态框
   */
  visible: boolean;
  /**
   * 关闭模态框回调函数
   */
  onClose: () => void;
  /**
   * 模态框类型，默认为success
   */
  type?: ModalType | string;
  /**
   * 成功状态标题
   */
  successTitle?: string;
  /**
   * 成功状态描述
   */
  successDesc?: string;
  /**
   * 失败状态标题
   */
  failTitle?: string;
  /**
   * 失败状态描述
   */
  failDesc?: string;
  /**
   * 加载状态标题
   */
  loadingTitle?: string;
  /**
   * 加载状态描述
   */
  loadingDesc?: string;
}

/**
 * 结果模态框组件
 * 用于显示操作结果的模态框，支持成功、失败、加载中三种状态
 *
 * @param {object} props - 组件属性对象
 * @param {boolean} props.visible - 是否显示模态框
 * @param {Function} props.onClose - 关闭模态框回调函数
 * @param {ModalType|string} [props.type='success'] - 模态框类型
 * @param {string} [props.successTitle] - 成功状态标题
 * @param {string} [props.successDesc] - 成功状态描述
 * @param {string} [props.failTitle] - 失败状态标题
 * @param {string} [props.failDesc] - 失败状态描述
 * @param {string} [props.loadingTitle] - 加载状态标题
 * @param {string} [props.loadingDesc] - 加载状态描述
 * @returns {JSX.Element} 结果模态框组件
 */
export const ResultModal = ({
  visible,
  onClose,
  type = 'success',
  successTitle,
  successDesc,
  failTitle,
  failDesc,
  loadingTitle,
  loadingDesc,
}: ResultModalProps): JSX.Element => {
  // 定义不同类型模态框的配置
  const typeConfig: Record<ModalType, TypeConfig> = {
    success: {
      icon: svgStore.successSvg,
      title: successTitle,
      desc: successDesc,
    },
    fail: {
      icon: svgStore.failSvg,
      title: failTitle,
      desc: failDesc,
    },
    loading: {
      icon: svgStore.loadingSvg,
      title: loadingTitle,
      desc: loadingDesc,
    },
  };

  // 根据传入的类型获取对应的配置
  const { icon, title, desc } = typeConfig[type as ModalType];

  return (
    <Dialog
      visible={visible}
      closeIcon
      closeIconPosition="top-right"
      hideConfirmButton
      hideCancelButton
      onClose={onClose}
      className={type}
    >
      <View className={`result-modal ${type}`}>
        <Image src={icon} svg className="result-modal-icon">
          <View className="back-gif"></View>
        </Image>
        <Text>{title}</Text>
        <Text>{desc}</Text>
      </View>
    </Dialog>
  );
};
