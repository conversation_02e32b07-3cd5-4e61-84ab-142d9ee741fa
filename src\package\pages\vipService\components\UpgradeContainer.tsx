import { Button, Popup } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import { memo } from 'react';
import { useUpgradePopup } from '../hooks/use-upgrade-popup';
import UpgradeConditionsList from './UpgradeConditionsList';
import './UpgradeContainer.scss';

interface IUpgradeContainerProps {
  cardId: number | string;
  cardName: string;
}

/**
 * 会员升级容器组件
 * 包含升级按钮和升级条件弹窗
 *
 * @param {IUpgradeContainerProps} props - 组件属性对象
 * @param {string} props.cardId - 当前卡片ID
 * @param {string} props.cardName - 卡片名称
 * @returns {JSX.Element} 会员升级容器组件
 */
const UpgradeContainer = memo(({ cardId, cardName }: IUpgradeContainerProps) => {
  // 使用自定义Hook处理所有状态逻辑
  const { showPopup, openPopup, closePopup, upgradeWays, memberFee, canUpgrade, handleUpgrade } =
    useUpgradePopup(cardId);

  return (
    <>
      <View className="vip-equity-upgrade">
        <View className="vip-equity-upgrade-text">
          <Text>选择开卡套餐</Text>
          <Text>升级为{cardName}，享受更多权益</Text>
        </View>
        <View className="vip-equity-upgrade-btn" onClick={openPopup}>
          立即升级
        </View>
      </View>
      <Popup
        closeOnOverlayClick={false}
        closeable
        visible={showPopup}
        position="bottom"
        onClose={closePopup}
      >
        <View className="upgrade-popup-content">
          <View className="upgrade-popup-content-title">
            <View className="text">升级条件</View>
            <View className="desc">以下任意方式的任务全部达标即可升级</View>
          </View>
          <View className="upgrade-popup-content-list">
            <UpgradeConditionsList data={upgradeWays} />
          </View>
          {/* 支付 */}
          <View className="upgrade-popup-content-pay">
            <Button type="primary" onClick={handleUpgrade} disabled={!canUpgrade} block>
              {memberFee > 0 ? `立即支付 ¥${memberFee}` : '立即升级'}
            </Button>
          </View>
        </View>
      </Popup>
    </>
  );
});

export default UpgradeContainer;
