import NoteEditorToolbar from '@/components/NoteEditorToolbar';
import { Editor, View } from '@tarojs/components';
import Taro, { useDidShow, useReady } from '@tarojs/taro';
import { useRef, useState } from 'react';
import './index.scss';

const NewNote = () => {
  const [editorCtx, setEditorCtx] = useState<any>(null);
  const editorReadyRef = useRef(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [systemInfo] = useState<Taro.getSystemInfoSync.Result>(Taro.getSystemInfoSync);

  useReady(() => {
    // 编辑器初始化
    if (!editorReadyRef.current) {
      editorReadyRef.current = true;
      Taro.createSelectorQuery()
        .select('#editor')
        .context((res) => {
          setEditorCtx(res.context);
        })
        .exec();
    }
  });

  useDidShow(() => {
    // 监听键盘高度变化
    Taro.onKeyboardHeightChange((res) => {
      console.log('res', res.height, systemInfo?.platform);
      // 区分安卓和iOS平台处理键盘高度
      if (systemInfo && systemInfo.platform === 'android') {
        // 安卓平台键盘高度可能需要调整，测试机发现键盘会自动将底部的操作工具栏顶起
        setKeyboardHeight(0);
      } else {
        // iOS平台直接使用键盘高度
        setKeyboardHeight(res.height);
        console.log('ios', res.height);
      }
    });
  });

  // 添加图片
  const handleAddImage = () => {
    // 先隐藏键盘
    Taro.hideKeyboard();

    setTimeout(() => {
      Taro.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: function (res) {
          // 这里可以添加图片上传逻辑，上传成功后插入编辑器
          const tempFilePath = res.tempFilePaths[0];

          // 模拟上传成功，直接插入本地图片
          // 实际应用中应该先上传图片到服务器，然后插入服务器图片地址
          if (editorCtx) {
            editorCtx.insertImage({
              src: tempFilePath,
              width: '90%',
              extClass: 'note-image',
              data: {
                custom: 'note-image',
              },
              success: function () {
                console.log('插入图片成功');
              },
            });
          }
        },
      });
    }, 100);
  };

  // 添加有序列表
  const handleAddOrderedList = () => {
    Taro.hideKeyboard();
    setTimeout(() => {
      if (editorCtx) {
        editorCtx.format('list', 'ordered');
      }
    }, 100);
  };

  // 添加无序列表
  const handleAddUnorderedList = () => {
    Taro.hideKeyboard();
    setTimeout(() => {
      if (editorCtx) {
        editorCtx.format('list', 'bullet');
      }
    }, 100);
  };

  // 撤销
  const handleUndo = () => {
    Taro.hideKeyboard();
    setTimeout(() => {
      if (editorCtx) {
        editorCtx.undo();
      }
    }, 100);
  };

  // 保存笔记
  const handleSubmit = () => {
    Taro.hideKeyboard();

    // 获取编辑器内容
    if (editorCtx) {
      editorCtx.getContents({
        success: function (res) {
          console.log('保存笔记', {
            content: res.html,
            text: res.text,
          });

          // 保存成功后返回笔记列表页
          Taro.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                Taro.navigateBack();
              }, 1500);
            },
          });
        },
      });
    }
  };

  // 分享
  const handleShare = () => {
    Taro.hideKeyboard();
    setTimeout(() => {
      Taro.showShareMenu({
        withShareTicket: true,
      });
    }, 100);
  };

  // 删除
  const handleDelete = () => {
    Taro.hideKeyboard();
    setTimeout(() => {
      Taro.showModal({
        title: '提示',
        content: '确定要删除此笔记吗？',
        success: function (res) {
          if (res.confirm) {
            Taro.showToast({
              title: '删除成功',
              icon: 'success',
              duration: 1500,
              success: () => {
                setTimeout(() => {
                  Taro.navigateBack();
                }, 1500);
              },
            });
          }
        },
      });
    }, 100);
  };

  // 计算适合当前平台的键盘高度
  const getAdjustedKeyboardHeight = () => {
    if (!keyboardHeight) return 0;

    if (systemInfo && systemInfo.platform === 'android') {
      // 安卓平台可能需要额外调整
      return keyboardHeight;
    }
    return keyboardHeight;
  };

  return (
    <View className="new-note-container">
      <View className="editor-container">
        <Editor
          id="editor"
          className="editor"
          placeholder="请输入笔记内容"
          showImgSize
          showImgToolbar
          showImgResize
        />
      </View>

      <NoteEditorToolbar
        keyboardHeight={getAdjustedKeyboardHeight()}
        onAddImage={handleAddImage}
        onAddOrderedList={handleAddOrderedList}
        onAddUnorderedList={handleAddUnorderedList}
        onShare={handleShare}
        onDelete={handleDelete}
        onSubmit={handleSubmit}
      />
    </View>
  );
};

export default NewNote;
