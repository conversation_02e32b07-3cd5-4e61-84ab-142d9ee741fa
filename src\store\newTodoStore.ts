import { PickerOptions } from '@nutui/nutui-react-taro';
import { create } from 'zustand';

interface IForm {
  title: string;
  address: string;
  backup: string;
  startTime: Date;
  endTime: Date;
}

interface TodoStore {
  formData: IForm | undefined;
  reminderList: PickerOptions;
  reminderTypes: {
    wechat: boolean;
    sms: boolean;
  };
  setFormData: (data: IForm) => void;
  setReminderList: (list: PickerOptions) => void;
  setReminderTypes: (types: { wechat: boolean; sms: boolean }) => void;
}

export const useTodoStore = create<TodoStore>((set) => ({
  formData: undefined,
  reminderList: [],
  reminderTypes: {
    wechat: false,
    sms: false
  },
  setFormData: (data) => set({ formData: data }),
  setReminderList: (list) => set({ reminderList: list }),
  setReminderTypes: (types) => set({ reminderTypes: types })
}));