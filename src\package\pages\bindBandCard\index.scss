.bind-band-card {
  padding: 20px;
  width: 100%;
  height: 100%;
  background-color: #f5f4f9;

  .code-container {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .tips {
    padding: 20px;

    font-size: 14px * 2;
    color: #9d9d9d;

    .tips-list {
      margin-top: 10px;
      display: flex;
      flex-direction: column;
      gap: 24px;
    }
  }

  .submit-btn {
    position: fixed;
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 28px;
    align-items: center;

    &-desc {
      height: 20px * 2;

      display: flex;
      align-items: center;
      gap: 10px;
      color: #999;
      font-size: 14px * 2;
    }
  }
}

.popup {
  height: fit-content;
  padding: 20px * 2;
  .popup-desc-title {
    font-weight: bold;
    font-size: 14px * 2;
    margin-bottom: 12px * 2;
    text-align: center;
    color: #666;
  }

  .popup-table {
    width: 100%;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    font-size: 14px * 2;
  }

  .popup-table-header {
    display: flex;
    background: #f7f7f7;
    font-weight: bold;
    border-bottom: 2px solid #eee;
    gap: 40px;

    .popup-table-col:first-of-type {
      width: 64px * 2;
      text-align: left;
    }
  }

  .popup-table-row {
    display: flex;
    border-bottom: 2px solid #f0f0f0;
    gap: 40px;
  }

  .popup-table-col {
    padding: 16px 8px;
    text-align: left;
    word-break: break-all;
    color: #666;

    &:first-of-type {
      width: 64px * 2;
      text-align: left;
    }
  }

  .popup-table-col.not-support {
    color: #e23c3c;
    font-weight: bold;
    text-align: center;
  }
}
