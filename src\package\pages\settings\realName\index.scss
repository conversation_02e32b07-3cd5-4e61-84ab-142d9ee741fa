.real-name {
  padding: 28px 24px;
  display: flex;
  flex-direction: column;
  gap: 28px;
  background-color: #f5f4f9;
  height: 100%;
  width: 100%;
}

.code-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tips {
  padding: 20px;

  font-size: 14px * 2;
  color: #9d9d9d;

  .tips-list {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
}

.submit-btn {
  position: fixed;
  bottom: 10%;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  right: 0;
}

// 成功认证后的显示样式
.read-name-info {
  --nutui-cell-title-color: #666 !important;
  --nutui-cell-title-font-size: 14px * 2;
  --nutui-cell-extra-color: #131313;
  --nutui-cell-extra-font-size: 14px * 2;

  .nut-cell-left {
    flex: none;
  }
}
