.notebook-detail {
  &-container {
    padding: 32px;
    background-color: #fff;
    min-height: 100vh;
  }

  &-loading,
  &-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    color: #999;
    font-size: 32px;
  }

  &-header {
    margin-bottom: 40px;
    border-bottom: 2px solid #f2f2f2;
    padding-bottom: 32px;
  }

  &-title {
    font-size: 40px;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 16px;
  }

  &-date {
    font-size: 28px;
    color: #999;
    display: block;
  }

  &-content {
    line-height: 1.6;
  }

  &-text {
    font-size: 32px;
    color: #333;
  }

  &-rich-text {
    font-size: 32px;
    color: #333;

    img {
      max-width: 90% !important;
      max-height: 800px !important;
      object-fit: contain;
      margin: 20px 0;
      border-radius: 16px;
      display: block;
    }

    p {
      margin-bottom: 24px;
    }
  }
}
