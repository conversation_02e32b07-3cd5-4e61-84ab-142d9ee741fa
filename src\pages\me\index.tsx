import NavigationBar from '@/components/navigation-bar';
import CustomTabBar from '@/components/tabbar';

import usePlanByMonth from '@/hooks/use-plan-by-month';
import useProfile from '@/hooks/use-profile';
import useRequestWithLifecycle from '@/hooks/use-request-with-lifecycle';
import { getContractSignPageList } from '@/serves/contruct';
import { getMmenumenuPermission } from '@/serves/member';
import useUserStore from '@/store/useUserStore';
import { formatDate } from '@/utils/date';
import { svgStore } from '@/utils/svgLoader';
import { Message, Setting } from '@nutui/icons-react-taro';
import { Cell, Image } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useEnv, useRequest } from 'taro-hooks';
import IconItem from './components/cardItem';
import InfoItem from './components/InfoItem';
import ListItem, { IListItemProps } from './components/listItem';
import './index.scss';

// 第一组列表项配置
const ListItems: IListItemProps[] = [
  {
    icon: 'list_gzrw',
    title: '工作任务',
    desc: [],
    path: '/pages/me-work-plan/index',
  },
  {
    icon: 'list_hzmx',
    title: '化债明细',
    path: '/package/pages/debtDetailOfMe/index',
  },
  // {
  //   icon: 'list_1',
  //   title: '债券抵扣明细',
  //   desc: ['待开发'],
  // },
  // {
  //   icon: 'list_3',
  //   title: '我的名片',
  //   desc: ['待开发'],
  // },
  // {
  //   icon: 'list_4',
  //   title: '拉新邀请',
  // },
];

// 第二组列表项配置
const ListItems2: IListItemProps[] = [
  // {
  //   icon: 'list_5',
  //   title: 'AI顾问  ',
  //   desc: ['待开发'],
  // },
  {
    icon: 'list_6',
    title: '我的合同',
    desc: [],
    path: '/package/pages/contract/index',
  },
  // {
  //   icon: 'list_7',
  //   title: '法律知识库',
  //   desc: ['待开发'],
  // },
  {
    icon: 'list_8',
    title: '建议与反馈',
    path: '/package/pages/feedback/index',
  },
];

// 个人中心页面组件
const Index = () => {
  const env = useEnv();
  const { userInfo } = useUserStore((state) => state);
  const [list1, setList1] = useState<IListItemProps[]>(ListItems);
  const [contractSignPageList, setContractSignPageList] = useState(ListItems2);

  const { data: menuPermission } = useRequestWithLifecycle(getMmenumenuPermission, {
    ready: !!userInfo?.userId,
    refreshOnShow: true,
  });
  const ListItems3: IListItemProps[] = useMemo(() => {
    return [
      {
        icon: 'me_accounting',
        title: '账管顾问',
        path: '/package/pages/me/accounting/index',
        visible: menuPermission?.data?.accountingManagement,
      },
      {
        icon: 'entry_icon',
        title: '客户信息录入',
        // TODO: 待开发
        path: '/packageA/pages/InforEntry/index',
        visible: menuPermission?.data?.accountingManagement,
      },
    ];
  }, [menuPermission?.data?.accountingManagement]);

  const { getCurrentDayWorks } = usePlanByMonth({
    date: formatDate(new Date(), { template: 'YYYY-MM' }),
  });

  // 获取当前月的工作任务
  useEffect(() => {
    if (getCurrentDayWorks) {
      const newList = ListItems.map((item) => {
        if (item.title === '工作任务') {
          return {
            ...item,
            desc: [...(getCurrentDayWorks(new Date())?.map((item) => item.workName) || [])],
          };
        }
        return item;
      });
      setList1([...newList]);
    }
  }, [getCurrentDayWorks]);

  // 获取待签署合同列表
  useRequest(
    () =>
      getContractSignPageList({
        pageNo: 1,
        pageSize: 100,
        partyCSignStatus: 0,
      }),
    {
      cacheKey: 'contract-sign-page-list-0',
      onSuccess: (res) => {
        if (res?.data?.list?.length) {
          const newList = ListItems2.map((item) => {
            if (item.title === '我的合同') {
              return {
                ...item,
                desc: [
                  <View className="contract-item">
                    <Text>待签署</Text>
                    <View className="dot"></View>
                  </View>,
                ],
              };
            }
            return item;
          });
          setContractSignPageList(newList);
        }
      },
      ready: !!userInfo?.userId,
    },
  );

  return (
    <View className="tabbarContainer">
      <View className="wrapper">
        {/* 根据环境判断是否显示导航栏 */}
        {env !== 'TT' && <NavigationBar title="我的"></NavigationBar>}

        <MemberInfo />

        <View className="list-container">
          <Cell.Group>
            {ListItems3.map((item, index) => {
              return <ListItem key={index} {...item} />;
            })}
          </Cell.Group>

          {/* 第一组功能列表 */}
          <Cell.Group>
            {list1.map((item, index) => {
              return <ListItem key={index} {...item} />;
            })}
          </Cell.Group>

          {/* 第二组功能列表 */}
          <Cell.Group>
            {contractSignPageList.map((item, index) => {
              return <ListItem key={index} {...item} />;
            })}
          </Cell.Group>
        </View>

        {/* 背景装饰元素 */}
        <View className="marks">
          <View className="markright"></View>
          <View className="markleft"></View>
        </View>
      </View>

      {/* 底部标签栏 */}
      <CustomTabBar></CustomTabBar>
    </View>
  );
};

const MemberInfo = memo(() => {
  const { userInfo: data } = useProfile();

  const handleCardClick = useCallback(() => {
    Taro.navigateTo({
      url: `/package/pages/vipService/index?cardId=${data?.data?.cardId}`,
    });
  }, []);

  return (
    <>
      {/* 顶部用户信息区域 */}
      <View className="title">
        <View
          className="avatar-container"
          onClick={() => {
            Taro.navigateTo({
              url: '/package/pages/settings/profile/index',
            });
          }}
        >
          {/* <Image src="https://x" width={20} height={20} error={<SvgIcon name="avatar" />} /> */}
          <Image
            src={data?.data?.avatar || svgStore.avatar}
            error={<Image src={svgStore.avatar} />}
          />
        </View>
        <View className="card-container">
          <View className="card-container-top">
            <Text
              className="card-name"
              onClick={() => {
                Taro.navigateTo({
                  url: '/package/pages/settings/profile/index',
                });
              }}
            >
              您好, {data?.data?.nickname}
            </Text>
            <View className="title-right">
              {/* <Badge dot right={2} top={2}> */}
              <Message
                width={22}
                height={22}
                onClick={() => {
                  Taro.navigateTo({
                    url: '/package/pages/messageList/index',
                  });
                }}
              />
              {/* </Badge> */}
              <Setting
                width={22}
                height={22}
                onClick={() => {
                  Taro.navigateTo({ url: '/package/pages/settings/index' });
                }}
              />
            </View>
          </View>

          <View className="card-info">
            {data?.data?.cardId ? (
              <View onClick={handleCardClick} style={{ height: '32rpx' }}>
                <Image src={data?.data?.smallIcon} mode="heightFix" />
              </View>
            ) : (
              <View onClick={handleCardClick} style={{ height: '32rpx' }}>
                <Image src={svgStore.upgradeIcon} mode="heightFix" />
              </View>
            )}

            <View
              onClick={() => {
                Taro.navigateTo({
                  url: '/package/pages/settings/realName/index',
                  success: function (res) {
                    // 通过eventChannel向被打开页面传送数据
                    res.eventChannel.emit('acceptDataFromOpenerPage', {
                      data: data?.data,
                    });
                  },
                });
              }}
            >
              {data?.data?.realAuthentication === '1' ? (
                <IconItem icon="card_sm" text="已实名认证" size={10} className="sm" />
              ) : (
                <IconItem icon="card_no_sm" text="未实名认证" size={10} className="no-sm" />
              )}
            </View>
          </View>
        </View>
      </View>

      {/* 团队信息展示区域 */}
      <View className="team-info">
        <InfoItem
          topText={data?.data?.cashCouponNumber?.toString() ?? '0'}
          bottomText="抵扣券(张)"
        />
        <InfoItem
          topText={data?.data?.myCommission?.toString() ?? '0.00'}
          bottomText="我的佣金(元)"
          onClick={() => {
            Taro.navigateTo({
              url: '/package/pages/me/myCommissions/index',
            });
          }}
        />
        <InfoItem
          onClick={() => {
            Taro.navigateTo({
              url: '/package/pages/me/myTeam/index',
            });
          }}
          topText={data?.data?.myGroupNumber?.toString() ?? '0'}
          bottomText="我的团队(人)"
        />
      </View>

      {/* VIP卡片展示区域 */}
      <View>
        {data?.data?.cardSloganImage && (
          <Image onClick={handleCardClick} src={data?.data?.cardSloganImage} mode="widthFix" />
        )}
      </View>
    </>
  );
});

export default Index;
