import useUserStore from '@/store/useUserStore';
import Taro from '@tarojs/taro';
const app = Taro.getApp();
interface SocialLoginRes {
  /* */
  code: number;

  /* */
  data: {
    /*用户编号 */
    userId: number;

    /*访问令牌 */
    accessToken: string;

    /*刷新令牌 */
    refreshToken: string;

    /*过期时间 */
    expiresTime: Record<string, unknown>;

    /*社交用户 openid */
    openid: string;
  };

  /* */
  msg: string;
}
class httpRequest {
  baseOptions(
    params: { url?: any; data?: any; contentType?: any; hasToken?: boolean },
    method = 'GET',
  ) {
    let { url, data, hasToken = true } = params;
    let contentType = 'application/json;charset=utf-8';
    contentType = params.contentType || contentType;
    const option: any = {
      url: url,
      data: data,
      method: method,
      header: {
        'content-type': contentType,
      },
    };
    const { accessToken } = useUserStore.getState();
    if (accessToken && hasToken) {
      option.header.Authorization = 'Bearer ' + accessToken; // 添加授权头
    }
    return new Promise<any>((resolve, reject) => {
      Taro.request({
        ...option,
        success: (res: any) => {
          if (res.statusCode === 200) {
            if (res.data.code === 500 || res.data.code === 400) {
              Taro.showToast({
                title: res.data.msg || '请求失败',
                icon: 'none',
              });
              reject(res.data);
            }
            resolve(res.data);
          } else {
            Taro.showToast({
              title: res.data.msg || '请求失败',
              icon: 'none',
            });
            reject(res.data);
          }
        },
        fail: (err) => {
          Taro.showToast({
            title: '请求失败，请稍后重试',
            icon: 'none',
          });
          reject(err);
        },
      });
    });
  }

  get(url: string, data = {}, hasToken?: boolean) {
    let option = { url: url + '?' + new URLSearchParams(data).toString(), hasToken };
    return this.baseOptions(option);
  }

  post(url: string, data: any, contentType?: string, hasToken?: boolean) {
    let params = { url, data, contentType, hasToken };
    return this.baseOptions(params, 'POST');
  }

  put(url: any, data: any, contentType?: string, hasToken?: boolean) {
    let option = { url, data, contentType, hasToken };
    return this.baseOptions(option, 'PUT');
  }

  delete(url: any, data = '') {
    let option = { url, data };
    return this.baseOptions(option, 'DELETE');
  }
}

export default new httpRequest();
