.my-team-container {
  padding: 20px 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 28px;
}

.my-team-content {
  display: flex;
  flex-direction: column;
  gap: 16px;

  &-title {
    color: #000;
    font-size: 16px * 2;
    font-weight: 500;
  }
}

// 列表项
.card-list-item {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e5e5e5;
  padding: 24px 32px 20px 32px;
  background-color: #fff;
  gap: 20px;
  border-radius: 8px * 2;

  &-avatar,
  &-avatar-name {
    width: var(--avatar-size, 48px * 2);
    height: var(--avatar-size, 48px * 2);
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
  }

  &-avatar-name {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px * 2;
    color: #fff;
    font-weight: 500;
  }

  &-content {
    display: flex;
    flex-direction: column;
    gap: 12px;

    &-name {
      color: #000;
      font-size: var(--name-font-size, 16px * 2);
    }

    &-time {
      color: #9d9d9d;
      font-size: var(--time-font-size, 12px * 2);
    }
  }

  &-right {
    color: #131313;
    text-align: right;
    font-weight: 600;
    font-size: var(--extra-font-size, 16px * 2);
    flex-shrink: 0;
    margin-left: auto;
  }
}
