import Taro from '@tarojs/taro';

export const downloadFile = (url: string) => {
  const accessToken = Taro.getStorageSync('accessToken');
  return new Promise((resolve, reject) => {
    Taro.downloadFile({
      header: {
        Authorization: 'Bearer ' + accessToken,
      },
      // url: `${serveIp.ip}${serveIp.downloadFilePath}${url}`,
      url,
      success: (res) => {
        var filePath = res.tempFilePath;

        Taro.openDocument({
          filePath: filePath,
          success: function (res) {
            console.log('打开文档成功', res);
            resolve(res);
          },
          complete: function (res) {
            resolve(res);
          },
          fail: (err) => {
            reject(err);
          },
        });
      },
    });
  });
};
