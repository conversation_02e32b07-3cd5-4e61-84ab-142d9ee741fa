import useProfile from '@/hooks/use-profile';
import useRequestWithLifecycle from '@/hooks/use-request-with-lifecycle';
import useUploadFile from '@/hooks/use-upload-file';
import { updateUser } from '@/serves/updateProfile';
import { svgStore } from '@/utils/svgLoader';
import { ArrowRight } from '@nutui/icons-react-taro';
import { Cell, Image, Input, Button as NutButton, Popup } from '@nutui/nutui-react-taro';
import { Button, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useCallback, useMemo, useState } from 'react';
import './index.scss';

// 定义Profile项的接口
interface ProfileItem {
  label: string;
  value: string;
  isAvatar?: boolean;
  render?: () => JSX.Element;
  extra?: () => JSX.Element;
  onClick?: () => void;
}

const Profile = () => {
  // 获取用户信息
  const { userInfo, refresh } = useProfile();
  const { runAsync: updateUserRun } = useRequestWithLifecycle(updateUser, {
    manual: true,
  });
  const { uploadFile } = useUploadFile();
  // 添加昵称编辑相关状态
  const [showNicknamePopup, setShowNicknamePopup] = useState(false);
  const [newNickname, setNewNickname] = useState('');
  const MAX_NICKNAME_LENGTH = 12; // 最大昵称长度

  // 使用useCallback包装showToast函数
  const showToast = useCallback(
    (msg: string, type: 'success' | 'fail' | 'warning' | 'loading' = 'success') => {
      Taro.showToast({
        title: msg,
        icon: type === 'success' ? 'success' : type === 'fail' ? 'error' : 'none',
      });
    },
    [],
  );

  /**
   * 更新用户信息
   */
  const updateUserInfo = useCallback(
    async ({ nickname, avatar }: { nickname?: string; avatar?: string }) => {
      if (!userInfo?.data?.memberUserId || !userInfo?.data?.mobile) {
        // showToast('昵称修改失败', 'fail');
        return;
      }
      const res = await updateUserRun({
        nickname: nickname || userInfo?.data?.nickname,
        avatar: avatar || userInfo?.data?.avatar,
        id: userInfo?.data?.memberUserId,
        mobile: userInfo?.data?.mobile,
      });
      if (res.code === 0) {
        // showToast('昵称修改成功');
        refresh();
      } else {
        // showToast('昵称修改失败', 'fail');
      }
    },
    [userInfo?.data, updateUserRun, showToast, refresh],
  );

  const uploadAvatar = useCallback(
    async (tempFilePath: string) => {
      const imageData = {
        tempFilePath,
        fileType: 'image',
      };
      const uploadRes = await uploadFile(undefined, [imageData] as Taro.chooseMedia.ChooseMedia[]);
      if (uploadRes.url) {
        updateUserInfo({
          avatar: uploadRes.url,
        });
        showToast('头像上传成功');
        refresh();
      }
    },
    [uploadFile, updateUserInfo, showToast, refresh],
  );

  const handleChooseAvatar = useCallback(
    (event: any) => {
      const { avatarUrl } = event.detail;
      if (avatarUrl) {
        uploadAvatar(avatarUrl);
      } else {
        showToast('获取头像失败', 'fail');
      }
    },
    [uploadAvatar, showToast],
  );

  // 处理昵称修改，使用useCallback优化
  const handleNicknameEdit = useCallback(() => {
    setNewNickname(userInfo?.data?.nickname || '');
    setShowNicknamePopup(true);
  }, [userInfo?.data]);

  // 处理昵称保存
  const handleSaveNickname = useCallback(async () => {
    if (newNickname === userInfo?.data?.nickname) {
      setShowNicknamePopup(false);
      return;
    }

    await updateUserInfo({
      nickname: newNickname,
    });
    showToast('昵称修改成功');
    setShowNicknamePopup(false);
  }, [newNickname, userInfo?.data?.nickname, updateUserInfo]);

  // 处理昵称输入
  const handleNicknameChange = useCallback((val: string) => {
    // 限制输入长度
    if (val.length <= MAX_NICKNAME_LENGTH) {
      setNewNickname(val);
    }
  }, []);

  // 使用useMemo缓存profileItems，避免每次渲染重新创建
  const profileItems = useMemo<ProfileItem[]>(
    () => [
      {
        label: '头像',
        value: '',
        isAvatar: true,
        render: () => (
          <Button
            openType="chooseAvatar"
            onChooseAvatar={handleChooseAvatar}
            className="avatar-button"
          >
            <Image
              src={userInfo?.data?.avatar || svgStore.avatar}
              error={<Image src={svgStore.avatar} />}
              width={40}
              height={40}
              radius={20}
            />
          </Button>
        ),
        extra: () => <ArrowRight size={12} />,
      },
      {
        label: '昵称',
        value: userInfo?.data?.nickname || '',
        extra: () => <ArrowRight size={12} />,
        onClick: handleNicknameEdit,
      },
      {
        label: '手机号',
        value: userInfo?.data?.mobile
          ? userInfo.data.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
          : '',
        onClick: () => {
          showToast('手机号不可修改', 'warning');
        },
      },
      {
        label: '账号ID',
        value: userInfo?.data?.username || '',
        onClick: () => {
          // 复制账号ID到剪贴板
          if (userInfo?.data?.username) {
            Taro.setClipboardData({
              data: userInfo.data.username,
              success: () => {
                showToast('账号ID已复制到剪贴板');
              },
            });
          }
        },
      },
      {
        label: '性别',
        // 根据性别代码显示对应文本，这里假设0是男，1是女
        value: '未知',
        // extra: () => <ArrowRight size={12} />,
      },
      {
        label: '注册时间',
        value: userInfo?.data?.createTime || '',
      },
      {
        label: '推荐人',
        value: '',
      },
      {
        label: '账号状态',
        value: '正常',
      },
    ],
    [userInfo, showToast, handleNicknameEdit, handleChooseAvatar],
  );

  return (
    <View className="profile">
      <Cell.Group>
        {profileItems.map((item, index) => (
          <Cell
            align="center"
            key={index}
            title={<Text>{item.label}</Text>}
            extra={
              <View className="extra-content">
                {item.isAvatar && item.render
                  ? item.render()
                  : item.value && <Text className="value-text">{item.value}</Text>}
                {item.extra && item.extra()}
              </View>
            }
            {...(item.onClick && { onClick: item.onClick })}
          />
        ))}
      </Cell.Group>

      {/* 昵称编辑底部弹窗 */}
      <Popup
        visible={showNicknamePopup}
        position="bottom"
        closeable
        title="修改昵称"
        onClose={() => setShowNicknamePopup(false)}
        className="nickname-popup"
      >
        <View className="nickname-input-container">
          <Input
            placeholder="请输入新昵称"
            value={newNickname}
            onChange={(val) => handleNicknameChange(val)}
            className="nickname-input"
            clearable
          />
          <View className="nickname-counter">
            {newNickname.length}/{MAX_NICKNAME_LENGTH}
          </View>
          <View className="nickname-button-container">
            <NutButton onClick={() => setShowNicknamePopup(false)} className="cancel-button">
              取消
            </NutButton>
            <NutButton type="primary" onClick={handleSaveNickname} className="save-button">
              保存
            </NutButton>
          </View>
        </View>
      </Popup>
    </View>
  );
};

export default Profile;
