import { Cell, CellGroup } from '@nutui/nutui-react-taro';

import Arrow from '@/components/Arrow';
import PageContainerTemp from '@/components/pageContainerTemp';
import useRequestWithLifecycle from '@/hooks/use-request-with-lifecycle';
import SvgIcon from '@/pages/me/components/SvgIcon';
import { getContractVOByCase } from '@/serves/accounting/caseEntrustDetail';
import { ContractVO } from '@/serves/accounting/caseEntrustDetail/interface';
import { getRepaymentPlan } from '@/serves/debt';
import { downloadFile } from '@/utils/downloadFIle';
import { formatMoneyForList } from '@/utils/money';
import { View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { FC, useCallback, useMemo, useState } from 'react';
import DebtPlanList from '../debtPlanList/DebtPlanList';
import './index.scss';

interface debtDetailProps {}

const debtDetail: FC<debtDetailProps> = () => {
  const [data, setData] = useState<
    {
      title: string;
      extra: any;
    }[]
  >([]);
  const [contractId, setContractId] = useState<string>(); // 合同id
  const { params } = useRouter();
  const { caseEntrustId = '' } = params; // 委案id

  const { data: repaymentPlanData } = useRequestWithLifecycle(() => getRepaymentPlan(contractId!), {
    refreshDeps: [contractId],
    ready: !!contractId,
  });

  const { data: contractData } = useRequestWithLifecycle(
    () => getContractVOByCase(caseEntrustId!),
    {
      refreshDeps: [caseEntrustId],
      ready: !!caseEntrustId,
      onSuccess(res) {
        handleData(res.data);
      },
    },
  );

  console.log('contractData', contractData);

  const defaultData = useMemo(() => {
    return { list: [], total: 0 };
  }, []);

  const handleData = useCallback((item: ContractVO) => {
    console.log('debtDetail', item);
    setContractId(item?.id?.toString());
    // TODO: 对其账管顾问的数据
    const data = [
      {
        title: '债务金额',
        extra: formatMoneyForList(item.actualDebtReductionAmount, { unit: '元' }),
      },
      {
        title: '已化债金额',
        extra: formatMoneyForList(item.convertedDebtAmount, { unit: '元' }),
      },
      { title: '计划化债期数', extra: item.debtReductionPeriods + '期' },
      { title: '开卡银行', extra: item.bank },
      { title: '还款银行账户', extra: item.overdueAccount }, // 逾期账户代替
      { title: '债权人', extra: item.creditorName },
      { title: '逾期账户', extra: item.overdueAccount },
      { title: '委案金额', extra: formatMoneyForList(item.entrustedAmount, { unit: '元' }) },
      { title: '利息', extra: formatMoneyForList(item.interest, { unit: '元' }) },
      { title: '年利率', extra: item.annualizedRate ? item.annualizedRate + '%' : '--' },
      { title: '滞纳金', extra: formatMoneyForList(item.lateFee, { unit: '元' }) },
      { title: '最大减免金额', extra: formatMoneyForList(item.maxReduction, { unit: '元' }) },
      {
        title: '服务合同',
        extra: (
          <View
            className="debt-detail-more-icon"
            style={{ display: 'flex', alignItems: 'center', gap: 4 }}
            onClick={async () => {
              if (!item.contractUrl) {
                Taro.showToast({
                  title: '合同地址不存在',
                  icon: 'none',
                });
                return;
              }
              try {
                Taro.showLoading({
                  title: '加载中，请稍后',
                });
                // 设置超时隐藏
                const timeout = setTimeout(() => {
                  Taro.hideLoading();
                  Taro.showToast({
                    title: '加载失败，请检查网络',
                    icon: 'none',
                  });
                }, 5000); // 15秒后自动隐藏
                await downloadFile(item.contractUrl);
                clearTimeout(timeout); // 成功下载后清除超时
                Taro.hideLoading();
              } catch (error) {
                Taro.hideLoading();
                Taro.showToast({
                  title: '下载失败, 请重试',
                  icon: 'none',
                });
              }
            }}
          >
            查看
            <SvgIcon name="arrow-right-small" size={12}></SvgIcon>
          </View>
        ),
      },
    ];
    setData(data);
  }, []);

  return (
    <PageContainerTemp title="债务详情" isRet align="center" showTabbar={false}>
      <View className="debt-detail-container">
        <DebtDetailCell data={data} />
        <DebtPlanList data={repaymentPlanData?.data ?? defaultData} />
      </View>
    </PageContainerTemp>
  );
};

interface debtDetailCellProps {
  data: {
    title: string;
    extra: string;
  }[];
}
const DebtDetailCell: FC<debtDetailCellProps> = ({ data }) => {
  const [isShowMore, setIsShowMore] = useState(false);
  return (
    <>
      <CellGroup className={`debt-detail-cell-group ${isShowMore ? 'show-more' : ''}`}>
        {data.map((item, index) => (
          <Cell
            className="nutui-cell-clickable"
            key={index}
            title={item.title}
            align="center"
            extra={item.extra}
          />
        ))}
      </CellGroup>
      <View
        className="debt-detail-more"
        onClick={() => {
          setIsShowMore(!isShowMore);
        }}
      >
        {isShowMore ? <Arrow up={true} size={14} /> : <Arrow up={false} size={14} />}
      </View>
    </>
  );
};

export default debtDetail;
