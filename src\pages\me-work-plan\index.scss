.work-plan-container {
  // padding: 20px 24px;
  // background-color: #fff;
  height: 100%;

  --nutui-hoverbutton-item-background: #f00;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.date-select {
  display: flex;
  flex-direction: column;
  gap: 20px;

  &-control {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;

    text:first-of-type {
      color: #000;
      text-align: center;
      font-size: 16px * 2;
      font-weight: 550;
    }
  }
}

.todo-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0px 24px;

  &-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    // padding: 0px 24px;
  }

  .title {
    color: #131313;
    font-size: 16px * 2;
    font-weight: 550;
  }

  &-item {
    // height: 44px * 2;
    flex-shrink: 0;
    border-radius: 8px * 2;
    background: #fff;
    display: flex;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
    gap: 12px;

    .item-tag {
      border: 1px solid #6a81fa;
      color: #6a81fa;
      padding: 4px;
      text-align: left;
      font-size: 10px * 2;
      z-index: 2;

      &.system {
        color: #fe5655;
        border: 1px solid #fe5655;
      }

      &.person {
        color: #6a81fa;
        border: 1px solid #6a81fa;
      }
    }

    text:first-of-type {
      color: #131313;
      font-size: 14px * 2;
      font-weight: 500;
    }

    text:last-of-type {
      margin-left: auto;
      color: #9d9d9d;
      font-size: 11px * 2;
      font-weight: 400;
    }
  }
}

.add-todo-btn {
  border-radius: 50%;
}
