.repayment-container {
  padding: 16px;
  --nutui-cell-title-color: #666;
  --nutui-cell-title-font-size: 14px * 2;
  --nutui-cell-extra-color: #131313;
  --nutui-cell-extra-font-size: 14px * 2;
  background: #f5f4f9;
  height: 100%;

  .nut-cell-extra {
    font-weight: 500;
  }

  .repayment-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 16px;
  }
}

.repayment-chart {
  background-color: #fff;
  border-radius: 8px * 2;
  padding: 32px 34px;
  display: flex;
  flex-direction: column;
  gap: 20px;

  &-title {
    color: #000;
    font-size: 16px * 2;
  }

  &-content {
    padding-bottom: 10px * 2;
    border-bottom: 1px solid #f2f2f2;

    .number-keyboard-input {
      .nut-input-native {
        color: #131313;
        font-size: 24px * 2;
        font-weight: 600;
      }

      &-edit-btn {
        color: #666;
        font-size: 14px * 2;
        border: none;
      }
    }
  }

  &-desc {
    color: #9d9d9d;
    font-size: 12px * 2;
  }
}

.repayment-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  padding: 16px;
  background-color: #fff;
  padding: 32px 40px;
  width: 100%;
  padding-bottom: calc(env(safe-area-inset-bottom) + 32px);

  &-confirm {
    background: #f00;
    border-radius: 12px * 2;

    color: #fcfcfc;
    text-align: center;
    font-size: 18px * 2;
    font-weight: 500;
  }
}

.nut-numberkeyboard-sidebar {
  flex-basis: 0%;
}
