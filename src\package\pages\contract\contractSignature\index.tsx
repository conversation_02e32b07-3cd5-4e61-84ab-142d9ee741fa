import useUploadFile from '@/hooks/use-upload-file';
import { contractSign } from '@/serves/contruct';
import { Signature } from '@nutui/nutui-react-taro';
import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { FC, useEffect, useRef, useState } from 'react';
import { useRequest, useRouter } from 'taro-hooks';
import './index.scss';

interface ContractDetailProps {}

const ContractDetail: FC<ContractDetailProps> = () => {
  const [signatureUrl, setSignatureUrl] = useState<string>('');
  const [routerInfo] = useRouter();
  const { id, contractId } = routerInfo.params;
  const { uploadFile } = useUploadFile();

  const { run: contractSignFn } = useRequest(contractSign, {
    manual: true,
    onSuccess: () => {
      Taro.showToast({ title: '签署成功', icon: 'success' });
      setTimeout(() => {
        Taro.navigateBack();
      }, 1000);
    },
    onError: () => {
      Taro.showToast({ title: '签署失败', icon: 'none' });
    },
    onFinally: () => {
      Taro.hideLoading();
    },
  });

  useEffect(() => {
    console.log('signatureUrl', signatureUrl);
  }, [signatureUrl]);

  const confirm = async (dataurl: string) => {
    console.log('图片地址', dataurl);
    const tmp = [
      {
        tempFilePath: dataurl,
        fileType: 'image',
      },
    ];
    const res = await uploadFile(undefined, tmp as any);
    console.log(res.url);
    if (res.url) {
      contractSignFn({ id, singImage: res.url });
    }
    setSignatureUrl(dataurl);
  };
  const clear = () => {
    setSignatureUrl('');
  };
  const signatureRef1 = useRef<any>(null);

  return (
    <View className="contract-detail-container">
      <View className="utils-btn">
        <View className="utils-btn-left">{`合同编号：${contractId}`}</View>
        <View className="utils-btn-right">
          <View
            onClick={() => {
              setSignatureUrl('');
              signatureRef1.current?.clear();
            }}
          >
            清除
          </View>
          <View
            onClick={() => {
              signatureRef1.current?.confirm();
            }}
            className="confirm-btn"
          >
            <Text>确定</Text>
          </View>
        </View>
      </View>
      {signatureUrl ? (
        <View className="signature-preview">
          <Image src={signatureUrl} mode="aspectFill" />
        </View>
      ) : (
        <View className="signature-container">
          <Signature
            lineWidth={2}
            onConfirm={confirm}
            onClear={clear}
            canvasId="Signature"
            ref={signatureRef1}
          />
          <View className="signature-tips">签名区</View>
        </View>
      )}
    </View>
  );
};

export default ContractDetail;
