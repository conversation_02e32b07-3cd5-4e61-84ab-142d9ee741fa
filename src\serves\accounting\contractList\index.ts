import { serveIp } from '@/serves/config';
import http from '@/serves/http';
import { SelectAppTemplateRes, SelectAppTemplateVO } from './interface';

/**
 * @description 获得合同模板分页列表
 * @param {SelectAppTemplateVO} params
 * @returns {Promise<SelectAppTemplateRes>}
 */
export function selectAppTemplate(params: SelectAppTemplateVO): Promise<SelectAppTemplateRes> {
  return http.post(`${serveIp.ip}/app-api/crm/contract/templatePage`, params);
}
