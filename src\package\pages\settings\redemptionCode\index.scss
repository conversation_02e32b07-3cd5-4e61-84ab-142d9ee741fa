.content {
  background-color: #f5f4f9;
  z-index: 1;
  // padding-top: 132px * 2;
}

.code-container {
  margin: 0 24px;
  padding: 48px 40px;
  background-color: #fff;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  margin-top: 145px * 2;
  gap: 32px;

  .code-input {
    background-color: #f8f8f8;

    :global(.nut-input-inner) {
      background-color: #f8f8f8;
    }
  }
}

.custom-nav {
  z-index: 2;
}

.custom-marks {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1; // 确保 z-index 高于原始的 marks

  .code-bg {
    width: 375px * 2;
    height: 271px * 2;
    flex-shrink: 0;
  }
}
