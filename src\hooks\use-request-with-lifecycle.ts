import { Options } from '@taro-hooks/use-request/lib/types';
import { useDidHide, useDidShow } from '@tarojs/taro';
import { useCallback, useRef, useState } from 'react';
import { useRequest } from 'taro-hooks';

export interface UseRequestWithLifecycleOptions<TData, TParams extends any[]>
  extends Omit<Options<TData, TParams>, 'refreshDeps'> {
  /**
   * 是否在页面显示时重新请求数据
   * @default true
   */
  refreshOnShow?: boolean;

  /**
   * 原始的 refreshDeps，会与内部的 lifecycleRefresh 状态合并
   */
  refreshDeps?: any[];

  /**
   * 页面显示时的回调函数
   */
  onDidShow?: () => void;

  /**
   * 页面隐藏时的回调函数
   */
  onDidHide?: () => void;

  /**
   * 请求超时时间，单位为毫秒
   * @default undefined 不设置超时
   */
  timeout?: number;

  /**
   * 请求超时回调函数
   * @param params 请求参数
   */
  onTimeout?: (params: TParams) => void;
}

/**
 * 结合 useRequest、useDidShow 和 useDidHide 的自定义 hook
 *
 * @param service 请求函数
 * @param options 配置选项
 * @returns useRequest 的返回值加上额外的控制方法
 */
function useRequestWithLifecycle<TData, TParams extends any[]>(
  service: (...args: TParams) => Promise<TData>,
  options: UseRequestWithLifecycleOptions<TData, TParams> = {},
) {
  const {
    refreshOnShow = true,
    refreshDeps = [],
    onDidShow,
    onDidHide,
    timeout,
    onTimeout,
    ...restOptions
  } = options;
  const isFirstShow = useRef(true);

  // 内部状态，用于触发重新请求
  const [lifecycleRefresh, setLifecycleRefresh] = useState(0);

  // 包装原始服务函数，添加超时功能
  const serviceWithTimeout = useCallback(
    async (...args: TParams): Promise<TData> => {
      if (!timeout) {
        return service(...args);
      }

      return new Promise<TData>((resolve, reject) => {
        // 创建超时计时器
        const timeoutId = setTimeout(() => {
          // 调用超时回调函数
          if (onTimeout) {
            onTimeout(args);
          }
          reject(new Error(`请求超时，超过 ${timeout} 毫秒`));
        }, timeout);

        // 执行原始请求
        service(...args)
          .then((result) => {
            clearTimeout(timeoutId);
            resolve(result);
          })
          .catch((error) => {
            clearTimeout(timeoutId);
            reject(error);
          });
      });
    },
    [service, timeout, onTimeout],
  );

  // 合并 refreshDeps 和内部的 lifecycleRefresh 状态
  const mergedRefreshDeps = [...refreshDeps, lifecycleRefresh];
  // 使用 useRequest
  const requestResult = useRequest(serviceWithTimeout, {
    ...restOptions,
    refreshDeps: mergedRefreshDeps,
  });

  // 手动触发重新请求的方法
  const triggerRefresh = useCallback(() => {
    setLifecycleRefresh((prev) => prev + 1);
  }, []);

  // 页面显示时的处理
  useDidShow(() => {
    if (refreshOnShow) {
      if (isFirstShow.current) {
        isFirstShow.current = false;
        if (restOptions.manual) {
          triggerRefresh();
        }
        return;
      }
      // 如果启用了页面显示时刷新，则触发重新请求
      triggerRefresh();
    }

    // 执行用户自定义的 onDidShow 回调
    onDidShow?.();
  });

  // 页面隐藏时的处理
  useDidHide(() => {
    // 执行用户自定义的 onDidHide 回调
    onDidHide?.();
  });

  return {
    ...requestResult,
    /**
     * 手动触发重新请求
     */
    triggerRefresh,
    /**
     * 当前的生命周期刷新计数器值
     */
    lifecycleRefreshCount: lifecycleRefresh,
  };
}

export default useRequestWithLifecycle;
