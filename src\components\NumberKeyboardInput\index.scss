.number-keyboard-input {
  width: 100%;

  &-content {
    display: flex;
    align-items: center;

    .nut-input {
      width: 100%;
      background-color: transparent;
      padding: 8px 0;
      font-size: 16px;
    }
  }

  &-field {
    flex: 1;
    position: relative;
  }

  &-clear-wrapper {
    position: absolute;
    right: 0;
    top: 0;
    width: 44px * 2;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
  }

  &-clear {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 28px;
    z-index: 2;
  }

  &-edit-btn {
    margin-left: 10px;
    padding: 4px 10px;
    color: #0080ff;
    font-size: 14px;
    border: 1px solid #0080ff;
    border-radius: 4px;
    white-space: nowrap;
  }
}


.nut-overlay-slide-enter-done {
  z-index: 0 !important;
}