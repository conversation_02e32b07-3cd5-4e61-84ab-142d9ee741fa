// 完整版：自动分配至左右列的 Masonry 瀑布流 + 虚拟滚动（Taro 小程序兼容）
import { ScrollView, View } from '@tarojs/components';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

interface Props {
  items: React.ReactNode[];
  containerHeight?: number;
  onRefresh?: () => Promise<void>;
  onLoadMore?: () => Promise<void>;
  hasMore?: boolean;
  loading?: boolean;
  gap?: number;
  pageSize?: number;
}

export const SmartMasonryVirtualList: React.FC<Props> = ({
  items,
  containerHeight = 700,
  onRefresh,
  onLoadMore,
  hasMore = true,
  loading = false,
  gap = 18,
  pageSize = 10,
}) => {
  const [visibleItems, setVisibleItems] = useState<React.ReactNode[]>([]);
  const currentPage = useRef(1);
  const isScrolling = useRef(false);

  // 使用 useMemo 缓存左右列数据
  const { leftItems, rightItems } = useMemo(() => {
    const left: React.ReactNode[] = [];
    const right: React.ReactNode[] = [];

    visibleItems.forEach((item, index) => {
      if (index % 2 === 0) {
        left.push(item);
      } else {
        right.push(item);
      }
    });

    return { leftItems: left, rightItems: right };
  }, [visibleItems]);

  // 初始化可见项目
  useEffect(() => {
    currentPage.current = 1;
    setVisibleItems(items.slice(0, pageSize));
  }, [items, pageSize]);

  const handleScroll = useCallback(
    (e: any) => {
      if (isScrolling.current || loading || !hasMore) return;

      const { scrollTop, scrollHeight } = e.detail;
      const isNearBottom = scrollHeight - scrollTop - containerHeight < 50;

      if (isNearBottom) {
        isScrolling.current = true;
        currentPage.current += 1;
        const nextItems = items.slice(0, currentPage.current * pageSize);
        setVisibleItems(nextItems);
        handleLoadMore();
        isScrolling.current = false;
      }
    },
    [containerHeight, hasMore, items, loading, pageSize],
  );

  const handleRefresh = useCallback(async () => {
    if (!onRefresh) return;
    currentPage.current = 1;
    await onRefresh();
    setVisibleItems(items.slice(0, pageSize));
  }, [items, onRefresh, pageSize]);

  const handleLoadMore = useCallback(async () => {
    if (loading || !hasMore || !onLoadMore) return;
    await onLoadMore();
  }, [hasMore, loading, onLoadMore]);

  return (
    <ScrollView
      scrollY
      refresherEnabled={!!onRefresh}
      onRefresherRefresh={handleRefresh}
      onScroll={handleScroll}
      // style={{
      //   height: containerHeight,
      // }}
    >
      <View
        style={{
          width: '100%',
          display: 'flex',
          flexDirection: 'row',
          gap: `${gap}rpx`,
          padding: `${gap}rpx`,
          boxSizing: 'border-box',
        }}
      >
        {/* 左列 */}
        <View
          style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            gap: `${gap}rpx`,
          }}
        >
          {leftItems.map((item, index) => (
            <View key={index}>{item}</View>
          ))}
        </View>

        {/* 右列 */}
        <View
          style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            gap: `${gap}rpx`,
          }}
        >
          {rightItems.map((item, index) => (
            <View key={index}>{item}</View>
          ))}
        </View>
      </View>
      {loading && <View style={{ textAlign: 'center', padding: '20rpx' }}>加载中...</View>}
      {!hasMore && <View style={{ textAlign: 'center', padding: '20rpx' }}>没有更多了</View>}
    </ScrollView>
  );
};
