export interface GetGoodsListRes {
  /*总数 */
  count: number;

  /*商品列表 */
  list: {
    /*商品ID */
    productId: number;

    /*商品名称 */
    storeName: string;

    /*商品图片 */
    image: string;

    /*活动ID */
    activityId: number;

    /*关键词 */
    keyword: string;

    /*价格 */
    price: string;

    /*商户ID */
    merId: number;

    /*SPU ID */
    spuId: number;

    /*状态 */
    status: number;

    /*商品信息 */
    storeInfo: string;

    /*品牌ID */
    brandId: number;

    /*分类ID */
    cateId: number;

    /*单位名称 */
    unitName: string;

    /*星级 */
    star: number;

    /*排序 */
    sort: number;

    /*销量 */
    sales: number;

    /*商品类型 */
    productType: number;

    /*评分 */
    rate: string;

    /*评论数 */
    replyCount: number;

    /*系统标签 */
    sysLabels: Record<string, unknown>[];

    /*商户标签 */
    merLabels: Record<string, unknown>[];

    /*配送方式 */
    deliveryWay: string;

    /*是否包邮 */
    deliveryFree: number;

    /*库存 */
    stock: number;

    /* */
    merchant: {
      /*商户ID */
      merId: number;

      /*商户名称 */
      merName: string;

      /*商户头像 */
      merAvatar: string;

      /*商户信息 */
      merInfo: string;

      /*商户类型ID */
      typeId: number;

      /*商户类型名称 */
      typeName: string;
    };

    /*SVIP价格 */
    svipPrice: number;

    /* */
    showSvipInfo: {
      /*是否显示SVIP */
      showSvip: boolean;

      /*是否是SVIP */
      isSvip: boolean;

      /*是否显示SVIP价格 */
      showSvipPrice: boolean;

      /*节省金额 */
      saveMoney: number;
    };
  }[];
}
