import { serveIp } from '../config';
import HTTP from '../http';
import { CommonResponse } from '../interface/miniProgram';
import {
  FeedbackAppDTO,
  FeedbackDetailResponse,
  FeedbackListResponse,
  FeedbackTypeResponse,
} from './interface';

/**
 * 新增反馈
 * @param data 反馈信息
 * @returns 反馈信息
 */
export function addFeedback(data: FeedbackAppDTO) {
  return HTTP.post(serveIp.ip + '/app-api/crm/app/feedback/submit', data);
}

/**
 * 获取反馈列表
 * @returns 反馈列表响应
 */
export function getFeedbackList() {
  return HTTP.get(serveIp.ip + '/app-api/crm/app/feedback/list-by-user') as Promise<
    CommonResponse<FeedbackListResponse[]>
  >;
}

/**
 * 获取反馈类型
 * @returns 反馈类型
 */
export function getFeedbackType() {
  return HTTP.get(serveIp.ip + '/app-api/system/dict-data/type', {
    type: 'feedback_type',
  }) as Promise<FeedbackTypeResponse>;
}

/**
 * 获取反馈详情
 * @param id 反馈id
 * @returns 反馈详情
 */
export function getFeedbackDetail(id: string) {
  return HTTP.get(`${serveIp.ip}/app-api/crm/app/feedback/detail/${id}`) as Promise<
    CommonResponse<FeedbackDetailResponse>
  >;
}
