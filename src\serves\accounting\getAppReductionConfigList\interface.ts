/**
 * @description 管理后台 - 减免金额配置 Response VO
 */
export interface ReductionAmountConfigVO {
  /**
   * @description 主键ID
   * @type {number}
   */
  id: number;
  /**
   * @description 机构ID
   * @type {number}
   */
  mechanismId: number;
  /**
   * @description 期数
   * @type {number}
   */
  period: number;
  /**
   * @description 本金减免比例
   * @type {number}
   */
  principalReductionRatio: number;
  /**
   * @description 利息减免比例
   * @type {number}
   */
  interestReductionRatio: number;
  /**
   * @description 状态
   * @type {number}
   */
  status: number;
  /**
   * @description 创建时间
   * @type {string}
   */
  createTime?: string;
  /**
   * @description 更新时间
   * @type {string}
   */
  updateTime?: string;
}

/**
 * @description 响应参数
 */
export type GetAppReductionConfigListRes = ReductionAmountConfigVO[];

/**
 * @description 请求参数
 */
export interface GetAppReductionConfigListVO {
  /**
   * @description 债权人ID
   * @type {number}
   */
  creditorId: number;
}
