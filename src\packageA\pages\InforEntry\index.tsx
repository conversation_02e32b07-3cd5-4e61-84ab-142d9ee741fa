import ElevatorThreeLevel from '@/components/area';
import NavigationBar from '@/components/navigation-bar';
import useLogin from '@/hooks/use-login';
import { importUserInfo } from '@/serves/caseManagement';
import useDictStore from '@/store/useDictStore';
import { getKeyValue, getOptionsByDictType } from '@/utils/common';
import { Add, ArrowRight } from '@nutui/icons-react-taro';
import { Button, Form, Input, SafeArea } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import { debounce } from '@tarojs/runtime/dist/utils/lodash';
import Taro, { pxTransform, useLoad } from '@tarojs/taro';
import {
  JSXElementConstructor,
  ReactElement,
  ReactNode,
  ReactPortal,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import CustomPicker from './customPicker';
import './index.scss';
import { RadioGroupTemplete } from './pickerComponents';
const type = {
  TT: 44,
  WEAPP: 34,
};
const Index = () => {
  const [data] = useDictStore((state) => [state.data]);
  const [isRet, setIsRet] = useState<boolean>(false);
  const [UserProfileform] = Form.useForm();
  const debtsTypeLabel = Form.useWatch('debtsTypeLabel', UserProfileform);
  const debtsType = Form.useWatch('debtsType', UserProfileform);
  const [pickerTitle, setPickerTitle] = useState<string>('请选择');
  const [pickerVisible, setPickerVisible] = useState<boolean>(false);
  const [isNext, setIsNext] = useState<boolean>(false);
  const [pickerTemplete, setPickerTemplete] = useState<React.ReactNode>(null);
  const [debtsInfo, setdebtsInfo] = useState<any>([]);
  const submitFailed = (error: any) => {
    Taro.showToast({ title: JSON.stringify(error), icon: 'error' });
  };
  const submitSucceed = async (values: any) => {
    console.log('submitSucceed', values);
    setIsNext(true);
  };
  useLoad(() => {
    const pages = Taro.getCurrentPages();
    if (pages.length === 1) {
      console.log('我是第一个页面 (栈底)');
      // 在这里做只在入口页执行的初始化逻辑
    } else {
      console.log('我不是入口页');
      setIsRet(true);
    }
  });
  const { accessToken } = useLogin();
  useEffect(() => {
    console.log(accessToken);
  }, [accessToken]);
  const sexpickerOptions = useMemo(() => {
    return getOptionsByDictType('system_user_sex', data);
  }, [data]);
  const debtsInfoDom = useMemo(() => {
    return debtsInfo.map(
      (
        item: {
          identityLabel:
            | string
            | number
            | boolean
            | ReactElement<any, string | JSXElementConstructor<any>>
            | Iterable<ReactNode>
            | ReactPortal
            | null
            | undefined;
          debtsTypeLabel:
            | string
            | number
            | boolean
            | ReactElement<any, string | JSXElementConstructor<any>>
            | Iterable<ReactNode>
            | null
            | undefined;
          amountLabel:
            | string
            | number
            | boolean
            | ReactElement<any, string | JSXElementConstructor<any>>
            | Iterable<ReactNode>
            | ReactPortal
            | null
            | undefined;
          debtsType: string;
          cardAddressCode: { name: any }[];
          debtstypeInfoLabel:
            | string
            | number
            | boolean
            | ReactElement<any, string | JSXElementConstructor<any>>
            | Iterable<ReactNode>
            | null
            | undefined;
        },
        index: number,
      ) => {
        return (
          <View className="debtsInfoItem">
            <View className="identityLabel">{item.identityLabel}</View>
            <View className="debtsTypeamount">
              <View className="normaltext">
                <Text>债务类型</Text>
                {item.debtsTypeLabel}
              </View>
              <View className="normaltext">
                <Text>债务金额</Text>
                {item.amountLabel}
              </View>
            </View>
            {item.debtsType === 'customer_info_type_card' && (
              <>
                <View className="normaltext">
                  <Text>所属银行</Text>
                  {item.debtsTypeLabel}
                </View>
                <View className="normaltext">
                  <Text>归属城市</Text>
                  {item.cardAddressCode[0]?.name +
                    item.cardAddressCode[1]?.name +
                    item.cardAddressCode[2]?.name}
                </View>
              </>
            )}
            {item.debtsType === 'customer_info_type_lending' && (
              <View className="normaltext">
                <Text>网贷机构</Text>
                {item.debtstypeInfoLabel}
              </View>
            )}
            {item.debtsType === 'customer_info_type_loan' && (
              <View className="normaltext">
                <Text>私人借贷人数</Text>
                {item.debtstypeInfoLabel}
              </View>
            )}
            <View className="detBut">
              {' '}
              <Button
                color="#F6F6F6"
                size="mini"
                onClick={() => {
                  setdebtsInfo((prevList: any[]) =>
                    prevList.filter((_: any, i: number) => i !== index),
                  );
                }}
              >
                <Text style={{ color: '#131313' }}>删除</Text>
              </Button>
            </View>
          </View>
        );
      },
    );
  }, [debtsInfo]);
  const InforEntrySubmit = useCallback(
    debounce(async () => {
      console.log();
      const UserProfileformData = UserProfileform.getFieldsValue(true);
      const [addressProvince, addressCity, addressCode] = UserProfileformData.addressCode || [];
      const creditInfoList = debtsInfo.map((item: any) => {
        const creditInfo = {
          debtsType: item.debtsType,
          money: item.money,
          moneyType: item.moneyType,
          other: item.other,
          lendingName: '',
          loanNumber: '',
          cardName: '',
          cardDetail: [undefined, undefined, undefined],
        };
        if (creditInfo.debtsType === 'customer_info_type_card') {
          creditInfo.cardName = item.cardName;
          creditInfo.cardDetail = [
            item.cardDetail[0]?.id,
            item.cardDetail[1]?.id,
            item.cardDetail[2]?.id,
          ];
        } else if (creditInfo.debtsType === 'customer_info_type_lending') {
          creditInfo.lendingName = item.lendingName;
        } else if (creditInfo.debtsType === 'customer_info_type_loan') {
          creditInfo.loanNumber = item.lendingName;
        }
        return creditInfo;
      });
      await importUserInfo({
        customerName: UserProfileformData.customerName,
        customerPhone: UserProfileformData.customerPhone,
        customerSex: UserProfileformData.customerSex || '0',
        age: UserProfileformData.age || '0',
        addressDetail: UserProfileformData.addressDetail || '',
        addressProvince: addressProvince?.id,
        addressCity: addressCity?.id,
        addressCode: addressCode?.id,
        creditInfoList,
      });
      Taro.showToast({ title: '录入成功', icon: 'success' });
      setdebtsInfo([]);
      UserProfileform.resetFields();
      setIsNext(false);
    }, 500),
    [debtsInfo],
  );
  return (
    <View className="InforEntryWrapper">
      <NavigationBar isRet title="信息录入" align="center"></NavigationBar>
      <View className="InforEntry">
        <View className="InforEntry_header">
          <View className="InforEntry_title">客户债务危机处理信息收集</View>
        </View>
        {!isNext ? (
          <Form
            form={UserProfileform}
            divider
            labelPosition="left"
            onFinish={(values) => submitSucceed(values)}
            onFinishFailed={(values, errors) => submitFailed(errors)}
            footer={
              <Button block nativeType="submit" type="primary">
                下一步：填写债务/债权信息
              </Button>
            }
          >
            <View className="userBaseInfor">
              <View className="form_header">基本信息</View>
              <Form.Item
                label="姓名"
                name="customerName"
                rules={[{ required: true, message: '请输入姓名' }]}
              >
                <Input placeholder="请输入姓名（必填）" clearable align="right" type="text" />
              </Form.Item>
              <Form.Item
                label="性别"
                name="sexLabel"
                shouldUpdate
                onClick={() => {
                  setPickerTitle('请选择性别');
                  console.log('性别', sexpickerOptions);
                  setPickerVisible(true);
                  setPickerTemplete(
                    <RadioGroupTemplete
                      data={sexpickerOptions}
                      onChange={(value: any) => {
                        console.log(value);
                        console.log(getKeyValue(sexpickerOptions, value));
                        UserProfileform.setFieldsValue({
                          customerSex: getKeyValue(sexpickerOptions, value)[0],
                          sexLabel: getKeyValue(sexpickerOptions, value)[1],
                        });
                        setPickerVisible(false);
                      }}
                      defaultValue={UserProfileform.getFieldValue('customerSex')}
                    />,
                  );
                }}
              >
                {() => (
                  <View className="customCell">
                    <Input
                      type={'text'}
                      align="right"
                      value={UserProfileform.getFieldValue('sexLabel')}
                      placeholder="请选择"
                      readOnly
                    />
                    <ArrowRight
                      color="var(--nutui-gray-5)"
                      size={14}
                      style={{
                        marginRight: pxTransform(10),
                      }}
                    />
                  </View>
                )}
              </Form.Item>
              <Form.Item
                label="联系方式"
                name="customerPhone"
                rules={[
                  { required: true, message: '请输入联系方式' },
                  {
                    pattern: /^1[3-9]\d{9}$/,
                    message: '请输入正确的手机号码',
                  },
                ]}
              >
                <Input placeholder="请输入联系方式（必填）" clearable align="right" type="text" />
              </Form.Item>
              <Form.Item label="年龄" name="age" rules={[{ max: 3, message: '请输入年龄' }]}>
                <Input type="number" placeholder="请输入年龄（必填）" clearable align="right" />
              </Form.Item>
              <Form.Item
                className="form_item_no_border"
                label="地址"
                name="addressCode"
                shouldUpdate
                onClick={() => {
                  setPickerTitle('请选择地址');
                  setPickerVisible(true);
                  setPickerTemplete(
                    <ElevatorThreeLevel
                      onchange={(data) => {
                        UserProfileform.setFieldsValue({
                          addressCode: data,
                        });
                      }}
                      initvalue={UserProfileform.getFieldValue('addressCode') || [null, null, null]}
                    />,
                  );
                }}
              >
                {() => {
                  let data = UserProfileform.getFieldValue('addressCode') || undefined;
                  if (data) {
                    data = [data[0]?.name ?? '', data[1]?.name ?? '', data[2]?.name ?? ''].join('');
                  }
                  return (
                    <View className="customCell">
                      <Input
                        type={'text'}
                        align="right"
                        value={data}
                        placeholder="请选择"
                        readOnly
                      />
                      <ArrowRight
                        color="var(--nutui-gray-5)"
                        size={14}
                        style={{
                          marginRight: pxTransform(10),
                        }}
                      />
                    </View>
                  );
                }}
              </Form.Item>
              <Form.Item
                label="详细地址"
                name="addressDetail"
                rules={[{ message: '请输入详细地址' }]}
              >
                <Input placeholder="请输入详细地址" clearable align="right" type="text" />
              </Form.Item>
            </View>
          </Form>
        ) : (
          <View className="InforEntryNextPage">
            <View className="InforEntryNextPageHeader">
              <View className="debtsInfo">{debtsInfoDom}</View>
              <Button
                color="#fff"
                block
                onClick={() => {
                  console.log('dadadd');
                  Taro.navigateTo({
                    url: '/packageA/pages/InforEntry/InforEntryTwoPage/index',
                    events: {
                      // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
                      acceptDataFromOpenedPage: function (data: any) {
                        setdebtsInfo((debtsInfo: any) => [...debtsInfo, data.data]);
                      },
                    },
                  });
                }}
              >
                <View style={{ color: '#f00' }}>
                  {' '}
                  <Add />
                  <Text> 新增债务/债权信息</Text>
                </View>
              </Button>
            </View>

            <View className="InforEntryNextPageFooter">
              <View>
                <Button
                  color="#fff"
                  onClick={() => {
                    setIsNext(false);
                    setdebtsInfo([]);
                  }}
                >
                  <Text style={{ color: '#f00' }}>上一步</Text>
                </Button>
                <Button type="primary" onClick={InforEntrySubmit}>
                  提交
                </Button>
              </View>
              <SafeArea position="bottom" />
            </View>
          </View>
        )}
      </View>
      <CustomPicker
        title={pickerTitle}
        visible={pickerVisible}
        position="bottom"
        onClose={() => {
          setPickerVisible(false);
        }}
        customNode={pickerTemplete}
      />
    </View>
  );
};

export default Index;
