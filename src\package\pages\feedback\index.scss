.feedback {
  padding: 28px 24px;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  height: 100%;
  box-sizing: border-box;

  .feedback-container {
    border-radius: 24px;
    display: flex;
    flex-direction: column;
    gap: 28px;
  }

  // 反馈类型部分
  .feedback-type-section {
    background-color: #fff;
    padding: 24px 32px;

    .section-title {
      font-size: 32px;
      color: #131313;
      margin-bottom: 40px;
    }

    .feedback-types {
      display: flex;
      flex-wrap: wrap;
      gap: 24px;

      .type-item {
        padding: 8px 32px;
        font-size: 28px;
        color: #666;
        height: 29px * 2;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;

        border-radius: 15px * 2;
        background: #f8f9fa;

        &.selected {
          border: 1px solid #f00;
          background: rgba(255, 0, 0, 0.1);
          font-size: 14px * 2;
          color: #f00;
        }
      }
    }
  }

  // 问题描述部分
  .feedback-content-section {
    background-color: #fff;
    padding: 24px 32px;
    .feedback-input {
      width: 100%;
      padding-left: 0 !important;
      height: 200px;
      border: none;
      border-radius: 16px;
      padding: 24px;
      font-size: 28px;
      box-sizing: border-box;
    }

    .char-count {
      text-align: right;
      color: #999;
      font-size: 24px;
      margin-top: 16px;
    }
  }

  // 上传图片部分
  .feedback-upload-section {
    background-color: #fff;
    padding: 24px 32px;
    position: relative;

    .upload-title {
      color: #131313;
      font-size: 16px * 2;
      font-weight: 400;
      margin-bottom: 16px;
    }

    .nut-uploader-preview-img,
    .nut-uploader-upload {
      width: 80px * 2;
      height: 80px * 2;
    }

    .tips {
      position: absolute;
      top: 10px;
      right: 16px;
      color: #999;
      font-size: 24px;
    }
  }

  // 历史问题部分
  .history-feedback {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px * 2;
    color: #999;
    justify-content: center;
  }

  // 提交反馈部分
  .submit-button {
    border-radius: 12px * 2;
    background: #f00;
    color: #fcfcfc;
    text-align: center;
    font-size: 18px * 2;
    font-weight: 500;
    margin-bottom: 40px;
  }
}

/* 自定义图片预览样式 */
.custom-preview-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;

  .preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
  }

  .preview-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .preview-image {
    max-width: 100%;
    max-height: 80%;
  }

  .preview-actions {
    position: absolute;
    bottom: 100px;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 50px;
  }

  .preview-delete,
  .preview-close {
    padding: 20px 40px;
    border-radius: 30px;
    color: #fff;
    font-size: 28px;
    text-align: center;
  }

  .preview-delete {
    background-color: #ff4d4f;
  }

  .preview-close {
    background-color: #666;
  }
}
