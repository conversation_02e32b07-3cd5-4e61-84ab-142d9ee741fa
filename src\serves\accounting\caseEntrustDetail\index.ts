/*固定部分*/
import { serveIp } from '@/serves/config'; // 使用serverIp.ip作为baseUrl
import http from '@/serves/http';
import { CommonResponse } from '@/serves/interface/miniProgram';

/*根据api json文档需要替换的部分，但参考模板实现*/
import { ContractVO } from './interface'; // 新定义的类型

// 根据api文档生成的请求函数
/**
 * @description 根据委案id查询合同信息
 * @param {number} caseEntrustId 委案id
 * @returns {Promise<CommonResponse<ContractVO>>}
 */
export function getContractVOByCase(caseEntrustId: string): Promise<CommonResponse<ContractVO>> {
  return http.get(`${serveIp.ip}/app-api/crm/business/case/${caseEntrustId}`);
}
