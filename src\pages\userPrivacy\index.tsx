import { View } from '@tarojs/components';
  
import Taro from '@tarojs/taro';
import './index.scss';

const Index = () => {
  return (
    <View
      className="wrapper"
      onClick={(e) => {
        Taro.navigateTo({
          url: '../qaSmart/index?url=' + e.target.dataset.value,
        });
      }}
    >
      <View
        className="viewBox"
        data-value="https://guanjia.hunantianxing.com/userPrivacy/userServes.htm"
      >
        用户服务信息
      </View>
      <View
        className="viewBox"
        data-value="https://guanjia.hunantianxing.com/userPrivacy/privacy.htm"
      >
        用户隐私协议
      </View>
    </View>
  );
};

export default Index;
