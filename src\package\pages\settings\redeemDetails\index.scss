.redeem-details {
  width: 100%;
  padding: 28px 24px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 26px;
  align-items: start;
}

.redeem-detail-card {
  border-radius: 8px * 2;
  background: #fff;
  padding: 24px;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 7px * 2;
}

.redeem-detail-card-image {
  width: 145px * 2;
  height: 70px * 2;
  flex-shrink: 0;
}

.redeem-detail-card-name {
  color: #131313;
  font-size: 14px * 2;
  font-weight: 400;
  text-align: left;
}

// 兑换按钮
.redeem-btn {
  width: 100%;
  height: 26px * 2;
  flex-shrink: 0;
  border-radius: 13px * 2;
  background: rgba(255, 0, 0, 0.1);
  border-style: none;

  color: #f00;
  font-size: 14px * 2;
  font-weight: 500;
  line-height: 26px * 2;
  text-align: center;

  &.disabled {
    background: #f4f4f4;
    color: #9d9d9d;
  }
}

// 售罄角标
.sold-out-badge {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px * 2;
  height: 20px * 2;
  background-color: rgba(0, 0, 0, 0.6);
  clip-path: path('M0 8C0 3.58172 3.58172 0 8 0L40 0V12C40 16.4183 36.4183 20 32 20H0L0 8Z');
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 12px * 2;
  font-weight: 500;
  z-index: 1;
}

// 兑换规则弹窗
.redeem-popup-content {
  width: 100%;
  padding: 48px 40px;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 16px;
  height: 628px * 2;

  .redeem-popup-header {
    color: #000;
    font-size: 18px * 2;
    font-weight: 500;
    line-height: 28px * 2;
    position: relative;

    .close-btn {
      position: absolute;
      right: 0;
      top: 0;
      font-size: 24px * 2;
      line-height: 28px * 2;
      color: #666;
      cursor: pointer;
    }
  }

  .redeem-popup-card {
    display: flex;
    gap: 11px * 2;
    margin-top: 8px * 2;
    margin-bottom: 16px * 2;

    .redeem-popup-card-image {
      width: 145px * 2;
      height: 70px * 2;
      flex-shrink: 0;
    }

    .redeem-popup-card-title {
      color: #131313;
      font-size: 16px * 2;
      font-weight: 400;
      align-self: flex-end;
    }
  }

  .redeem-popup-description {
    display: flex;
    flex-direction: column;
    gap: 16px * 2;
    flex: 1;
    overflow-y: auto;

    .section {
      display: flex;
      flex-direction: column;
      gap: 10px * 2;
    }

    .section-title {
      color: #131313;
      font-size: 16px * 2;
      font-weight: 500;
      line-height: 24px * 2;
    }

    .rule-item {
      color: #666;
      font-size: 16px * 2;
      font-weight: 400;
      line-height: 24px * 2;
    }

    // .redeem-popup-footer {
    //   .redeem-popup-footer-btn {
    //     --nutui-button-default-font-size: 18px * 2;
    //   }
    // }
  }
}

// 添加PageContainer相关样式
.page-container-class {
  height: 628px * 2;
}
