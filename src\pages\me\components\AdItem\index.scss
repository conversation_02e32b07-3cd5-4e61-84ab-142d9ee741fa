.basic {
  width: '100%';
  height: 120px;
  border-radius: 12px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  padding: 24px;
  padding-bottom: 26px;
  gap: 12px;


  .split {
    .top {
      display: flex;
      gap: 12px;

      .title {
        font-size: 26px;
        font-weight: 400;
      }
    }

    .desc {
      font-size: 22px;
      font-weight: 400;
    }
  }

}



.bg-svg {
  background-size: cover;
  background-repeat: no-repeat;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.common {
  background: linear-gradient(90deg, #9691C2 0%, #D2CFEB 28.14%, #D2CFEB 75.42%, #9691C2 100%);

  .top {
    .title {
      color: #3C3466;
    }
  }

  .desc {
    color: #635C94;
  }

  .split {
    border-right: 1px solid rgba(118, 113, 160, 0.4);
  }

  .split:last-child {
    border-right: none !important;
  }
}

.advanced {
  background: linear-gradient(90deg, #E9C890 0%, #F7E4C0 28.14%, #F7E4C0 75.42%, #E9C890 100%);

  .split {
    border-right: 1px solid rgba(118, 113, 160, 0.4);
  }

  .split:last-child {
    border-right: none !important;
  }

  .top {
    .title {
      color: #76260D;
    }
  }

  .desc {
    color: #AE783B;
  }
}