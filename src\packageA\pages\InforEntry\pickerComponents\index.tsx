import { getRandom } from '@/utils/common';
import { Check, TriangleUp } from '@nutui/icons-react-taro';
import { Button, Cell, Checkbox, Input, Radio } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import { useState } from 'react';

export const RadioGroupTemplete = (props: {
  data: any[];
  onChange: (data?: any) => void;
  defaultValue?: string;
}) => {
  return (
    <Radio.Group
      className="chooseRadio"
      labelPosition="left"
      value={props.defaultValue}
      onChange={(value: string) => {
        props.onChange(value);
      }}
    >
      {props.data.map((item) => {
        return (
          <Radio
            key={getRandom(32)}
            icon={<View />}
            activeIcon={<Check color="red" />}
            value={item.value}
          >
            {item.label}
          </Radio>
        );
      })}
    </Radio.Group>
  );
};
export const CheckboxGroupTemplete = (props: {
  data: any[];
  onChange: (data?: any) => void;
  defaultValue?: string[];
}) => {
  return (
    <Checkbox.Group
      className="chooseRadio"
      labelPosition="left"
      defaultValue={props.defaultValue || ['']}
      onChange={(value: string[]) => {
        props.onChange(value);
      }}
    >
      {props.data.map((item) => {
        return (
          <Checkbox
            key={getRandom(32)}
            icon={<View />}
            activeIcon={<Check className="nut-checkbox-icon-checked" />}
            value={item.value}
          >
            {item.label}
          </Checkbox>
        );
      })}
    </Checkbox.Group>
  );
};
export const CellGroupTemplete = (props: {
  data: any[];
  onChange: (data?: any) => void;
  defaultValue?: string;
}) => {
  return (
    <Cell.Group>
      {props.data.map((item) => {
        return (
          <Cell
            key={getRandom(32)}
            title={item.label}
            onClick={() => {
              props.onChange(item);
            }}
            extra={<TriangleUp size={12} className="rotate90" />}
          />
        );
      })}
    </Cell.Group>
  );
};
export const CustomInputTemplete = (props: {
  onChange: (data?: string) => void;
  defaultValue?: string;
  placeholder?: string;
}) => {
  const [value, setValue] = useState(props.defaultValue || '');
  return (
    <View className="customInputBox">
      <View className="customInput">
        <Input
          placeholder={props.placeholder || '请输入'}
          value={value}
          onClear={() => setValue('')}
          onChange={(value) => {
            setValue(value);
          }}
          clearable
        />
      </View>
      <View
        className="customInputBtn"
        onClick={() => {
          props.onChange(value);
        }}
      >
        <Button block type="primary">
          确认
        </Button>
      </View>{' '}
    </View>
  );
};
export const getKeyValue = (
  data: { label: string; value: string | number }[],
  value: string | number,
) => {
  const result = data.find((item) => item.value === value);
  return result ? [result.value, result.label] : [undefined, undefined];
};
