import { getFeedbackDetail } from '@/serves/feedback';
import { AppDictDataRespVO, FeedbackDetailResponse } from '@/serves/feedback/interface';
import { Image, ImagePreview } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useCallback, useState } from 'react';
import { useRequest } from 'taro-hooks';
import './index.scss';

const testData = {
  type: '消费化债',
  content: '消耗化债换算有偏差，麻烦核对一下，并更正',
  response: '感谢您的反馈，我们正在紧急排查。',
  contact: '18888888888',
  images: [
    'https://storage.360buyimg.com/imgtools/e067cd5b69-07c864c0-dd02-11ed-8b2c-d7f58b17086a.png',
    'https://storage.360buyimg.com/imgtools/e067cd5b69-07c864c0-dd02-11ed-8b2c-d7f58b17086a.png',
    'https://storage.360buyimg.com/imgtools/e067cd5b69-07c864c0-dd02-11ed-8b2c-d7f58b17086a.png',
  ],
};

export default function HistoryDetail() {
  const [showPreview, setShowPreview] = useState(false);
  const { params } = useRouter();
  const { id } = params;
  const [detail, setDetail] = useState<FeedbackDetailResponse>();
  useRequest(() => getFeedbackDetailMap());

  const getFeedbackDetailMap = useCallback(async () => {
    try {
      const res = await getFeedbackDetail(id!);
      const typeRes = await Taro.getStorage<AppDictDataRespVO[]>({ key: 'feedbackType' });
      const data = res.data ?? undefined;
      const type = typeRes?.data?.find((item) => item.value === data?.type);
      setDetail({ ...data, type: type?.label ?? '' });
    } catch (error) {}
  }, [id]);

  return (
    <View className="history-detail">
      <HistoryDetailCard title="反馈类型" content={detail?.type} />
      <HistoryDetailCard title="问题描述" content={detail?.problem} />
      <HistoryDetailCard
        title="图片"
        visible={Boolean(detail?.attachments?.length)}
        content={
          <View className="history-detail-card-images">
            {detail?.attachments?.map((image, index) => (
              <Image
                key={index}
                width={80}
                // mode="widthFix"
                height={80}
                src={image}
                onClick={() => setShowPreview(true)}
              />
            ))}
            <ImagePreview
              autoPlay={false}
              images={detail?.attachments?.map((image) => ({
                src: image,
                title: '图片',
              }))}
              visible={showPreview}
              onClose={() => setShowPreview(false)}
            />
          </View>
        }
      />
      <HistoryDetailCard title="联系方式" content={detail?.contact} />
      <HistoryDetailCard title="反馈回复" content={detail?.systemRevert} />
    </View>
  );
}

const HistoryDetailCard = ({
  title,
  content,
  visible = true,
}: {
  title: string;
  content: string | string[] | React.ReactNode;
  visible?: boolean;
}) => {
  return (
    <>
      {visible && (
        <View className="history-detail-card">
          <View className="title">{title}</View>
          <View className="content">{content}</View>
        </View>
      )}
    </>
  );
};
