export interface getCaseSearchType {
  pageNo: number;
  pageSize: number;
  caseNumber?: string;
  debtor?: string;
  creditor?: string;
  caseStage?: string;
  caseLevel?: string;
  caseState?: string;
  allocationState?: number;
  courtRetentionState?: number;
  salesman?: number;
  sourceCase?: string;
  minAmount?: number;
  maxAmount?: number;
  minReduction?: number;
  maxReduction?: number;
}
// Response interface
export interface ImportCaseRes {
  /* */
  code: number;

  /* */
  message: string;

  /* */
  data: string;
}
export interface getCaseResponse {
  records: Record[];
  total: number;
  size: number;
  current: number;
  orders: Order[];
  optimizeCountSql: OptimizeCountSql;
  searchCount: OptimizeCountSql;
  optimizeJoinOfCountSql: boolean;
  maxLimit: number;
  countId: string;
  pages: number;
}

interface OptimizeCountSql {}

interface Order {
  column: string;
  asc: boolean;
}

export interface Record {
  id: number;
  caseNumber: string;
  bank: string;
  overdueAccount: string;
  entrustedAmount: number;
  interest: number;
  lateFee: number;
  maxReduction: number;
  overdueDays: number;
  cardDate: string;
  accountBranch: string;
  debtor: string;
  creditor: string;
  caseStage: string;
  caseState: string;
  allocationState: number;
  courtRetentionState: number;
  salesman: number;
  sourceCase: string;
  idNumber: string;
  gender: string;
  age: number;
  registeredAddress: string;
  caseLevel: string;
  employer: string;
  position: string;
  employerAddress: string;
  billingDay: number;
  billingAddress: string;
  currentAddress: string;
  contact1Name: string;
  contact1Relation: string;
  contact1Gender: string;
  contact1Phone: string;
  contact2Name: string;
  contact2Relation: string;
  contact2Gender: string;
  contact2Phone: string;
  contact3Name: string;
  contact3Relation: string;
  contact3Gender: string;
  contact3Phone: string;
  createdTime: string;
  updatedTime: string;
  creator: string;
  updater: string;
  deleted: number;
  tenantId: number;
}
/**信息录入 */
export interface CreateCustomerCollecParams {
  /*主键ID */
  id?: number;

  /*客户姓名 */
  customerName: string;

  /*客户性别（字典类型：system_user_sex） */
  customerSex?: number;

  /*客户联系方式 */
  customerPhone: string;

  /*年龄 */
  age?: number;

  /*地址编码省 */
  addressProvince?: string;

  /*地址编码市 */
  addressCity?: string;

  /*地址编码 */
  addressCode?: string;

  /*地址 */
  addressName?: string;

  /*地址详情 */
  addressDetail?: string;

  creditInfoList?: {
    /*主键ID */
    id?: number;

    /*信息收集ID */
    customerCollectId?: number;

    /*类型(字典类型：customer_dept_type) */
    type: string;

    /*债权/债务金额（字典类型：customer_info_money） */
    money: string;

    /*债务/债权类型（字典类型：customer_info_identity） */
    moneyType: string;

    /*信用卡名称 */
    cardName?: string;

    /*网贷名称 */
    lendingName?: string;

    /*借贷人数 */
    loanNumber?: string;

    /*其他 */
    other?: string;

    /*省 */
    cardProvince?: string;

    /*市 */
    cardCity?: string;

    /*编号 */
    cardCode?: string;

    /*详情 */
    cardDetail?: string;

    /*补充说明 */
    remark?: string;
  }[];

  /*补充说明 */
  remark?: string;
}
export interface GetCustomerCollecPageRes {
  /*数据 */
  list: CustomerCollecPageRecord[];
  total: number;
}
export interface CustomerCollecPageRecord {
  /*主键ID */
  id: number;

  /*客户姓名 */
  customerName: string;

  /*客户性别（字典类型：system_user_sex） */
  customerSex: number;

  /*客户联系方式 */
  customerPhone: string;

  /*年龄 */
  age: number;

  /*地址编码省 */
  addressProvince: string;

  /*地址编码市 */
  addressCity: string;

  /*地址编码 */
  addressCode: string;

  /*地址 */
  addressName: string;

  /*地址详情 */
  addressDetail: string;

  /*身份(字典类型：customer_info_identity) */
  type: any;

  /*债权/债务金额（字典类型：customer_info_money） */
  money: number;

  /*债务/债权类型（字典类型：customer_dept_type） */
  moneyType: number;

  /*信用卡省编码 */
  cardProvince: string;

  /*信用卡市编码 */
  cardCity: string;

  /*信用卡相关银行（字典类型：信用卡：customer_info_type_card 网贷：customer_info_type_lending 借贷：customer_info_type_loan） */
  cardCode: any;

  /*信用卡其他 */
  cardCodeOther: string;

  /*网贷其他 */
  cardLendingOther: string;

  /*信用卡开卡城市 */
  cardAddr: string;

  /*信用卡开卡城市名称 */
  cardAddrName: string;

  /*补充说明 */
  remark: string;

  /*客户编号 */
  customerNum: string;

  /*收集人 */
  creator: string;

  /*收集时间 */
  createdTime: string;
  displayDeleteButton: boolean;

  displayQueryButton: boolean;

  displayUpdateButton: boolean;
  other: string;
}
