.reporter-list-container {
  display: flex;
  flex-direction: column;
  --nutui-tabs-tabpane-padding: 16px 24px;
  gap: 28px;
}

.reporter-list {
  border-radius: 8px * 2;
  background: #fff;
  // height: 88px * 2;
  flex-shrink: 0;
  border: 1px solid #f5f5f5;
  // box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15);
  padding: 24px 32px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  gap: 16px;

  .r-top {
    display: flex;
    justify-content: space-between;

    &-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .title {
        color: #131313;
        font-size: 32px;
        font-weight: 600;
      }
    }

    .time {
      color: #9d9d9d;
      font-size: 22px;
    }
  }

  .r-bottom {
    display: grid;
    grid-template-columns: 1fr 1fr;
    justify-items: flex-start;
    flex: 1;
    align-items: center;

    &-item {
      display: flex;
      flex-direction: column;
      gap: 4px;

      text:first-child {
        color: #131313;
        font-size: 28px;
        font-weight: 500;
      }

      text:nth-child(2) {
        color: #666;
        font-size: 28px;
      }
    }
  }
}
