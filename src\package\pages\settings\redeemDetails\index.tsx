import { View } from '@tarojs/components';

import PageContainerTmp from '@/components/pageContainerTemp';
import { ResultModal } from '@/components/ResultModal';
import RedeemDetailCard from './components/RedeemDetailCard';
import RedeemPopup from './components/RedeemPopup';
import { useRedeemDetails } from './hooks/use-redeem-details';
import './index.scss';

/**
 * 兑换详情页组件
 * 展示可兑换的商品列表和处理兑换操作
 *
 * @returns {JSX.Element} 兑换详情页组件
 */
const RedeemDetails = () => {
  // 使用自定义Hook获取状态和处理函数
  const {
    showPopup,
    showResultModal,
    redeemItems,
    handleRedeem,
    handleCloseResultModal,
    handleOpenPopup,
    handleClosePopup,
  } = useRedeemDetails();

  return (
    <PageContainerTmp title="兑换" showMarks={false} showTabbar={false} isRet align="center">
      <View className="redeem-details">
        {redeemItems.map((item) => (
          <RedeemDetailCard key={item.id} data={item} onRedeem={handleOpenPopup} />
        ))}
      </View>
      <RedeemPopup visible={showPopup} onClose={handleClosePopup} onRedeem={handleRedeem} />
      <ResultModal
        visible={showResultModal}
        onClose={handleCloseResultModal}
        successTitle="兑换成功"
      />
    </PageContainerTmp>
  );
};

export default RedeemDetails;
