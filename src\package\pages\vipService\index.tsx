import useProfile from '@/hooks/use-profile';
import SvgIcon from '@/pages/me/components/SvgIcon';
import { getAppCardAndBenefits } from '@/serves/member/card-benefits';
import { svgStore } from '@/utils/svgLoader';
import { Image, Loading, Swiper } from '@nutui/nutui-react-taro';
import {
  CommonEventFunction,
  SwiperProps as TaroSwiperProps,
  Text,
  View,
} from '@tarojs/components';
import { memo, useState } from 'react';
import { useRequest } from 'taro-hooks';
import UpgradeContainer from './components/UpgradeContainer';
import './index.scss';

/**
 * VIP服务组件
 * 显示用户会员卡信息、会员权益以及升级选项
 *
 * @returns {JSX.Element} VIP服务组件
 */

const VipService = memo(() => {
  // 当前选中的轮播图索引
  const [currentIndex, setCurrentIndex] = useState(0);

  const onChange: CommonEventFunction<TaroSwiperProps.onChangeEventDetail> = (e) => {
    setCurrentIndex(e.detail.current);
  };

  const { userInfo: data } = useProfile();
  const { cardId } = data?.data || {};

  const { data: appCardAndBenefits } = useRequest(
    () => getAppCardAndBenefits({ type: cardId ? 1 : 0, cardId: cardId ? cardId : '' }),
    {
      cacheKey: 'appCardAndBenefits',
    },
  );

  // 获取所有卡片图片用于轮播
  const cardImages =
    appCardAndBenefits?.data?.map((item) => item.memberCardDO?.cardImage).filter(Boolean) || [];

  // 获取当前选中卡片的权益列表
  const currentCardBenefits = appCardAndBenefits?.data?.[currentIndex]?.benefitList || [];

  // 获取当前选中卡片的信息
  const currentCard = appCardAndBenefits?.data?.[currentIndex]?.memberCardDO || null;

  return (
    <View className="vip-service">
      <View className="vip-service-title">
        <View className="avatar">
          <Image
            src={data?.data?.avatar || svgStore.avatar}
            error={<Image src={svgStore.avatar} />}
            className="avatar"
          />
        </View>
        <View className="right">
          <View className="right-name">
            <Text>您好{data?.data?.nickname ? `, ${data?.data?.nickname}` : ''}</Text>
            <View style={{ height: '32rpx' }}>
              <Image
                style={{ height: '32rpx' }}
                src={cardId ? data?.data?.smallIcon : svgStore.upgradeIcon}
                mode="heightFix"
              />
            </View>
          </View>
          <View className="right-time">
            <Text>会员总时长：</Text>
            <Text className="highlight">{data?.data?.expireDate ?? ''}</Text>
            <Text>{data?.data?.expireDate ? '到期' : '--'}</Text>
          </View>
        </View>
      </View>

      {/* 使用appCardAndBenefits数据的卡片图片进行轮播 */}
      <Swiper defaultValue={0} loop previousMargin="10px" nextMargin="10px" onChange={onChange}>
        {cardImages.length > 0 ? (
          cardImages.map((item, index) => (
            <Swiper.Item
              key={index}
              className={`swiper-item-${index} swiper-item-spacing ${currentIndex !== index ? 'swiper-item-inactive' : ''}`}
            >
              <Image style={{ width: '87.7vw', height: '43.13vw' }} lazy loading src={item} />
            </Swiper.Item>
          ))
        ) : (
          <Swiper.Item>
            <View
              style={{
                width: '87.7vw',
                height: '43.13vw',
                backgroundColor: 'transparent',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                color: '#f3c6b0',
              }}
            >
              <Text>暂无会员卡信息</Text>
            </View>
          </Swiper.Item>
        )}
      </Swiper>

      <View className="vip-equity">
        <View className="vip-equity-title">
          <SvgIcon name="titleLeft" />
          <Text>{currentCard?.cardName || '会员权益'}</Text>
          <SvgIcon name="titleRight" />
        </View>
        {/* 显示当前选中卡片的权益列表 */}
        <View className="vip-equity-list">
          {currentCardBenefits.length > 0 &&
            currentCardBenefits.map((benefit, index) => (
              <View className="vip-equity-list-item" key={index}>
                <Image
                  src={benefit?.benefitIcon}
                  className="vip-equity-list-item-icon"
                  width={36}
                  height={36}
                  loading={<Loading className="nut-icon-loading" />}
                />
                <Text>{benefit.benefitName?.trim() || '未命名权益'}</Text>
                <Text>{benefit.rule?.trim() || '暂无规则说明'}</Text>
              </View>
            ))}
        </View>
      </View>
      {/* 升级按钮, 当前卡不显示, 其他卡显示 */}
      {currentIndex !== 0 && (
        <UpgradeContainer cardId={currentCard?.id || ''} cardName={currentCard?.cardName || ''} />
      )}
    </View>
  );
});

export default VipService;
