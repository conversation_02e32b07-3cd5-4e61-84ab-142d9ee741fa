export interface FeedbackAppDTO {
  id?: number; // 主键
  type?: string; // 类型 字典
  problem?: string; // 问题描述
  contact?: string; // 联系方式
  attachments?: string[]; // 附件
}

// 获取反馈类型请求
export interface FeedbackTypeRequest {
  type: string;
}

// 获取反馈类型响应
export interface AppDictDataRespVO {
  id: number; // 字典数据编号
  label: string; // 字典标签
  value: string; // 字典值
  dictType: string; // 字典类型
}

// 获取反馈类型响应
export interface FeedbackTypeResponse {
  code: number;
  data: AppDictDataRespVO[];
  msg: string;
}

// 获取反馈列表响应
export interface FeedbackListResponse {
  id: number; // 主键
  type: string; // 类型
  feedbackUserId: number; // 反馈建议用户id
  feedbackTime: number; // 反馈时间
  problem: string; // 问题描述
  systemRevert: string; // 系统回复
  contact: string; // 联系方式
  dealStatus: boolean; // 处理状态 0未处理 1已处理
  dealId: number; // 处理人userId
  dealTime: number; // 处理时间
  createTime: number; // 创建时间
  updateTime: number; // 更新时间
  creator: string; // 创建人
  deleted: boolean; // 删除标志（0: 未删除，1: 已删除）
  account: string; // 反馈账号
  handler: string; // 处理人
  attachments: string[]; // 附件
}

export interface FeedbackDetailResponse {
  id: number; // 主键
  type: string; // 类型 字典
  feedbackUserId: number; // 反馈建议用户id
  feedbackTime: number; // 反馈时间
  problem: string; // 问题描述
  systemRevert: string; // 系统回复
  contact: string; // 联系方式
  dealStatus: boolean; // 处理状态 0未处理 1已处理
  dealId: number; // 处理人userId
  dealTime: number; // 处理时间
  createTime: number; // 创建时间
  updateTime: number; // 更新时间
  creator: string; // 创建人
  deleted: boolean; // 删除标志（0: 未删除，1: 已删除）
  account: string; // 反馈账号
  attachments: string[]; // 附件
  handler: string; // 处理人
}
