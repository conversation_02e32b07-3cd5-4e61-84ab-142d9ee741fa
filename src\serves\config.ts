export const HTTP_STATUS = {
  SUCCESS: 200,
  CREATED: 201,
  ACCEPTED: 202,
  CLIENT_ERROR: 400,
  AUTHENTICATE: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
};
export const serveIp = {
  sonsystemIp: 'https://guanjia.hunantianxing.com/api/',
  // ip: 'https://guanjia.hunantianxing.com/api/',
  ip: 'http://**************:48080', // 开发环境
  ImageUrl: 'https://guanjia.hunantianxing.com/static/guanjiaMiniprogram/', // 开发环境
  downloadFilePath: `/app-api/infra/file/appDownload?url=`, // 获取内网文件下载地址
};
