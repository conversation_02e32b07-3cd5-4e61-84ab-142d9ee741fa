/* 委案明细页面样式 */
.deep-container {
  padding: 28px 24px;
  background-color: #f5f4f9;
  padding-bottom: env(safe-area-inset-bottom);

  --nutui-cell-title-color: #666;
  --nutui-cell-title-font-size: 14px * 2;
  --nutui-cell-extra-color: #131313;
  --nutui-cell-extra-font-size: 14px * 2;

  .nut-cell-left {
    flex: none;
  }
  .nut-cell-divider {
    background-color: #f5f6fa;
    opacity: 0.2;
  }
}

.deep-container-list {
  .deep-container-list-item {
    background-color: #fff;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 24px;

    &-top {
      display: flex;
      align-items: center;
      justify-content: space-between;

      &-title {
        font-size: 14px * 2;
        color: #666;
      }

      &-extra {
        font-size: 14px * 2;
        color: #131313;
      }
    }

    .content {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;

      &-item {
        width: 72px * 2;
        height: 28px * 2;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 14px * 2;
        border: 2px solid #e3e3e3;
        background: #fff;
        padding: 8px 20px;
        flex-shrink: 0;

        color: #131313;
        font-size: 14px * 2;

        &.active {
          color: #f00;
          border: 2px solid #f00;
          background: rgba(255, 0, 0, 0.1);
        }
      }
    }
  }
}

.isSign-title {
  color: #131313;
  font-size: 16px * 2;
  font-weight: 600;
}
