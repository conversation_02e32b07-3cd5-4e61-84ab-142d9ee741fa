import PageContainerTmp from '@/components/pageContainerTemp';
import { svgStore } from '@/utils/svgLoader';
import { Button, Image, Input } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { FC, useState } from 'react';
import './index.scss';

interface RedemptionCodeProps {}

const RedemptionCode: FC<RedemptionCodeProps> = () => {
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);

  const handleInputChange = (value: string) => {
    setCode(value);
  };

  const handleSubmit = async () => {
    if (!code.trim() || loading) {
      return;
    }

    try {
      setLoading(true);
      // TODO: Implement redemption code verification logic
      console.log('Redeeming code:', code);

      // TODO: 模拟API请求
      // await new Promise((resolve) => setTimeout(resolve, 1000));

      // TODO: 跳转到兑换界面
      Taro.navigateTo({
        url: '/package/pages/settings/redeemDetails/index',
      });
      setCode('');
    } catch (error) {
      console.error('兑换失败:', error);
      Taro.showToast({ title: '兑换失败，请重试', icon: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // 自定义背景装饰
  const customBackgroundMarks = (
    <View className="custom-marks">
      <Image src={svgStore['code-bg']} className="code-bg" />
    </View>
  );

  return (
    <PageContainerTmp
      title="兑换码"
      isRet={true}
      align="center"
      showMarks={true}
      transparentNav={true}
      customMarks={customBackgroundMarks}
    >
      <View className="content">
        <View className="code-container">
          <Input
            clearable
            placeholder="请输入兑换码"
            className="code-input"
            onChange={handleInputChange}
            maxLength={20}
            value={code}
          />
          <Button
            disabled={!code.trim() || loading}
            type="primary"
            className="code-button"
            onClick={handleSubmit}
            loading={loading}
          >
            {loading ? '兑换中...' : '立即兑换'}
          </Button>
        </View>
      </View>
    </PageContainerTmp>
  );
};

export default RedemptionCode;
