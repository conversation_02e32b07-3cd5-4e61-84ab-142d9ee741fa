# 状态管理Hook划分标准

## 1. 状态分类体系

### 1.1 基础状态（Primary State）
**定义**：组件的核心数据状态，通常对应用户输入或外部数据源

**判断标准**：
- 需要通过 `useState` 或 `useReducer` 直接管理
- 状态变化会触发组件重新渲染
- 通常对应表单字段、开关状态、数据列表等

**BindMallAccount 案例**：
```typescript
// ✅ 基础状态示例
const [phone, setPhone] = useState('');           // 用户输入的手机号
const [code, setCode] = useState('');             // 用户输入的验证码
const [isBound, setIsBound] = useState(false);    // 绑定状态
const [codeSent, setCodeSent] = useState(false);  // 验证码发送状态
```

### 1.2 派生状态（Derived State）
**定义**：基于基础状态计算得出的状态，不需要独立存储

**判断标准**：
- 可以通过其他状态计算得出
- 不应该使用 `useState` 存储
- 通常用于UI显示逻辑判断

**BindMallAccount 案例**：
```typescript
// ✅ 派生状态示例
const isPhoneValid = isPhoneNumber(phone);                    // 手机号有效性
const isCodeInputEnabled = codeSent;                          // 验证码输入框启用状态
const isBindButtonEnabled = isPhoneValid && code.length === 6; // 绑定按钮启用状态

// ❌ 错误做法 - 不应该用 useState 存储派生状态
const [isPhoneValid, setIsPhoneValid] = useState(false);
```

### 1.3 临时状态（Temporary State）
**定义**：短期存在的状态，通常用于UI交互或异步操作

**判断标准**：
- 生命周期较短
- 主要用于UI反馈或过渡效果
- 可能在操作完成后被重置

**示例**：
```typescript
// ✅ 临时状态示例
const [loading, setLoading] = useState(false);     // 加载状态
const [showModal, setShowModal] = useState(false); // 弹窗显示状态
const [error, setError] = useState<string | null>(null); // 错误信息
```

## 2. 状态管理Hook的职责边界

### 2.1 核心职责
1. **状态定义与初始化**
2. **状态更新函数封装**
3. **派生状态计算**
4. **状态重置与清理**

### 2.2 职责边界示例

```typescript
// ✅ 正确的状态管理Hook
export const useBindMallAccountState = () => {
  // 基础状态管理
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  
  // 派生状态计算
  const isPhoneValid = isPhoneNumber(phone);
  
  // 状态操作封装
  const clearFormData = useCallback(() => {
    setPhone('');
    setCode('');
  }, []);
  
  return {
    // 状态
    phone, code, isPhoneValid,
    // 操作
    setPhone, setCode, clearFormData
  };
};

// ❌ 错误做法 - 包含业务逻辑
export const useBindMallAccountState = () => {
  const [phone, setPhone] = useState('');
  
  // ❌ 不应该在状态Hook中处理API调用
  const sendCode = async () => {
    const result = await api.sendVerificationCode(phone);
    // ...
  };
  
  return { phone, setPhone, sendCode };
};
```

## 3. 状态管理Hook设计原则

### 3.1 单一职责原则
每个状态Hook只负责特定领域的状态管理

### 3.2 最小化原则
只暴露必要的状态和操作函数

### 3.3 不可变性原则
状态更新应该遵循不可变性

```typescript
// ✅ 正确的状态更新
const updateUserInfo = useCallback((newInfo: Partial<UserInfo>) => {
  setUserInfo(prev => ({ ...prev, ...newInfo }));
}, []);

// ❌ 错误做法 - 直接修改状态
const updateUserInfo = useCallback((newInfo: Partial<UserInfo>) => {
  userInfo.name = newInfo.name; // 直接修改
  setUserInfo(userInfo);
}, [userInfo]);
```
