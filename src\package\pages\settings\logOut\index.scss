/* src/package/pages/settings/logOut/index.scss */
.log-out-container {
  background-color: #f5f4f9;
  padding: 48px 32px;
}

.log-out-container-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 28px;
  height: 100%;
}

.icon-box {
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-box {
  color: #131313;
  font-size: 14px * 2;
}

.log-out-card {
  width: 100%;
  background-color: #fff;
  padding: 32px;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  gap: 32px;

  .list-box {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .desc {
    color: #9d9d9d;
    font-size: 14px * 2;
    font-weight: 400;
  }

  .list {
    display: flex;
    // flex-direction: column;
    align-items: center;
    gap: 16px;
    &::before {
      content: '';
      display: inline-block;
      width: 12px;
      height: 12px;
      background-color: #000;
      border-radius: 50%;
    }

    .item {
      color: #000;
      font-size: 14px * 2;
      font-weight: 400;
    }
  }
}

.log-out-footer {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 40px;
  align-items: center;
  justify-content: center;
  margin-top: auto;

  .agree-box {
    display: flex;
    gap: 16px;
    align-items: center;

    .agree-box-text {
      color: #9d9d9d;
      font-size: 12px * 2;
      font-weight: 400;
    }

    .agree-box-text-link {
      color: #131313;
      font-size: 12px * 2;
      font-weight: 400;
    }
  }

  .log-out-footer-btn {
    width: 100%;
    margin: 0 32px;
  }
}
