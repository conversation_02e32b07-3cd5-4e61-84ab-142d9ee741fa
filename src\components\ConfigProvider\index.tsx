import { ConfigProvider } from '@nutui/nutui-react-taro';
import zhCN from '@nutui/nutui-react-taro/dist/es/locales/zh-CN';
import 'dayjs/locale/zh-cn';
import { useState, type PropsWithChildren } from 'react';
import themeConfig from './themeConfig';

const Config = ({ children }: PropsWithChildren) => {
  const [config, setConfig] = useState(themeConfig());

  const resetConfig = () => {
    setConfig(themeConfig());
  };
  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        nutuiColorPrimary: '#d9001b',
        nutuiColorPrimaryStop1: '#d9001b',
        nutuiColorPrimaryStop2: '#d9001b',
      }}
    >
      {children}
    </ConfigProvider>
  );
};
export default Config;
