import EmptyContainer from '@/components/empty-container';
import { Add } from '@nutui/icons-react-taro';
import { Drag, HoverButton } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useState } from 'react';
import './index.scss';

const testData = [
  {
    id: '1',
    title: '笔记1',
    date: '2021-01-01',
    content: '这是笔记1的内容',
  },
  {
    id: '2',
    title: '笔记2',
    date: '2021-01-02',
    content: '这是笔记2的内容，长文本示例，测试文本截断效果。'.repeat(10),
  },
];

const Notebook = () => {
  const [notes, setNotes] = useState<any[]>(testData);

  // 处理笔记点击
  const handleNoteClick = (id: string) => {
    Taro.navigateTo({
      url: `/package/pages/notebook/notebookDetail/index?id=${id}`,
    });
  };

  return (
    <View className="notebook-container">
      <View className="notebook-content">
        {notes.length > 0 ? (
          notes.map((note, index) => (
            <View key={index} className="note-item" onClick={() => handleNoteClick(note.id)}>
              <Text className="note-title">{note.title}</Text>
              <Text className="ellipsis note-content">{note.content}</Text>
              {/* <Ellipsis content={note.content} direction="end" rows={1} /> */}
              <Text className="note-date">{note.date}</Text>
            </View>
          ))
        ) : (
          <EmptyContainer title="暂无笔记" />
        )}
      </View>

      {/* 修改悬浮按钮实现 */}
      <Drag
        direction="y"
        style={{ bottom: '80rpx', right: '10rpx', position: 'fixed', zIndex: 9999 }}
      >
        <HoverButton
          onClick={(e) => {
            // 阻止冒泡
            e.stopPropagation();
            Taro.navigateTo({
              url: '/package/pages/notebook/newNote/index',
            });
          }}
          icon={<Add size={20} color="#fff" />}
        />
      </Drag>
    </View>
  );
};

export default Notebook;
