import { getSvgPath, type SvgName } from '@/utils/svgLoader';
import { Image, ImageProps } from '@tarojs/components';

interface SvgIconProps {
  name: SvgName; // SVG 文件名（不带扩展名）
  size?: number; // 图标尺寸
  className?: string; // 自定义类名
  style?: React.CSSProperties;
  imageStyle?: Omit<ImageProps, 'src'>;
  round?: boolean;
  onClick?: () => void;
}

export default function SvgIcon({
  name,
  size = 24,
  className = '',
  style,
  imageStyle,
  round = false,
  onClick,
}: SvgIconProps) {
  const svgPath = getSvgPath(name);

  if (!svgPath) {
    console.warn(`SVG "${name}" not found in store`);
    return null;
  }

  return (
    <Image
      className={`svg-icon ${className}`}
      src={svgPath}
      style={{
        width: `${size}px`,
        height: `${size}px`,
        ...style,
        borderRadius: round ? `50%` : '0',
      }}
      mode="widthFix"
      svg
      onClick={onClick}
      {...imageStyle}
    ></Image>
  );
}
