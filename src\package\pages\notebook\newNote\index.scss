.new-note-container {
  // padding: 40px;
  background-color: #fff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  // padding-bottom: 120px; /* 为底部工具栏预留空间 */
}

.editor-container {
  flex: 1;
  background-color: #fff;
}

.editor {
  width: 100%;
  height: calc(100vh - 160px); /* 减去工具栏和padding的高度 */
  padding: 20px 20px;
  box-sizing: border-box;

  /* 编辑器中图片的样式 */
  .note-image {
    max-width: 90% !important;
    max-height: 400px !important;
    object-fit: contain;
    margin: 10px 0;
    border-radius: 8px;
    display: block;
  }
}

.button-container {
  margin-top: 40px;
  padding: 0 40px;
}
