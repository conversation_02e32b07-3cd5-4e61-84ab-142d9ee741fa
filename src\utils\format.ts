/**
 * 数据脱敏
 * @param str 需要脱敏的字符串
 * @param start 保留前几位
 * @param end 保留后几位
 * @returns 脱敏后的字符串
 */
export const desensitize = (str: string, start: number, end: number): string => {
  if (!str) {
    return '';
  }
  const len = str.length;
  if (start + end >= len) {
    return str;
  }
  const middle = '*'.repeat(len - start - end);
  return str.substring(0, start) + middle + str.substring(len - end);
};
