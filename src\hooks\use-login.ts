import useDict from '@/serves/dict/hooks';
import useUserStore from '@/store/useUserStore';
import Taro, { useLoad } from '@tarojs/taro';
import { useEnv } from 'taro-hooks';
import { socialLogin } from '../serves';
const type = {
  TT: 44,
  WEAPP: 34,
};
export default function useLogin() {
  const env = useEnv();
  useDict();
  const [accessToken, setAccessToken, setRefreshToken, setUserInfo] = useUserStore((state) => [
    state.accessToken,
    state.setAccessToken,
    state.setRefreshToken,
    state.setUserInfo,
  ]);
  useLoad(() => {
    if (accessToken) {
      console.log('已经登录了');
      return;
    }
    Taro.login({
      success: async (res) => {
        if (res.code) {
          //发起网络请求
          console.log('登录code:' + res.code);
          const result = await socialLogin({
            code: res.code,
            type: type[env],
          });
          if (result.data) {
            setUserInfo(result.data);
            setAccessToken(result.data.accessToken);
            Taro.setStorageSync('accessToken', result.data.accessToken);
          }
        } else {
          console.log('登录失败！' + res.errMsg);
        }
      },
    });
  });
  return {
    accessToken,
  };
}
