import { formatDate } from '@/utils/date';
import { Picker, PickerOptions, PickerValue } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import { FC, memo, useMemo, useState } from 'react';
import Arrow from '../Arrow';
import './index.scss';

/**
 * 日期选择器组件属性接口
 * @interface DateSelectProps
 * @property {Function} onConfirm - 日期确认回调函数，参数为格式化的日期字符串(YYYYMM)
 * @property {string} initialValue - 初始日期值，格式：YYYYMM
 */
interface DateSelectProps {
  /** 日期确认回调函数 */
  onConfirm?: (date: string) => void;
  /** 初始日期值，格式：YYYYMM */
  initialValue?: string;
}

/**
 * 日期选择器组件
 * 提供年月选择功能，默认显示当前年月
 * 点击后弹出选择器，可以选择年份和月份
 * 
 * @param {DateSelectProps} props - 组件属性
 * @returns {JSX.Element} 日期选择器组件
 */
const DateSelect: FC<DateSelectProps> = ({ onConfirm, initialValue }) => {
  // 控制选择器弹窗的显示状态
  const [isVisible, setIsVisible] = useState(false);

  /**
   * 获取初始显示的日期描述文本
   * 根据initialValue格式化为"YYYY年MM月"的形式
   * 如果没有initialValue则使用当前日期
   * 
   * @returns {string} 格式化的日期描述
   */
  const getInitialDesc = () => {
    if (initialValue) {
      const year = initialValue.substring(0, 4);
      const month = initialValue.substring(4, 6);
      return `${year}年${month}月`;
    }
    return formatDate(new Date(), { template: 'YYYY年MM月' });
  };

  // 日期描述文本，显示在界面上
  const [desc, setDesc] = useState(getInitialDesc());

  /**
   * 获取初始选择器的值
   * 根据initialValue解析出年月数组
   * 如果没有initialValue则使用当前年月
   * 
   * @returns {[string, string]} 年月数组，格式为[年份, 月份]
   */
  const getInitialPickerValue = () => {
    if (initialValue) {
      const year = initialValue.substring(0, 4);
      const month = initialValue.substring(4, 6);
      return [year, month];
    }
    const now = new Date();
    const year = now.getFullYear().toString();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    return [year, month];
  };

  // 选择器的当前选中值
  const [pickerValue, setPickerValue] = useState(getInitialPickerValue());

  /**
   * 生成选择器的选项数据
   * 年份范围为当前年份的前后10年
   * 月份为1-12月
   */
  const options = useMemo(() => {
    // 生成年份选项，范围为当前年份的前后10年
    const currentYear = new Date().getFullYear();
    const years: any[] = [];
    for (let i = currentYear - 10; i <= currentYear + 10; i++) {
      years.push({ value: `${i}`, label: `${i}年` });
    }
    // 生成月份选项，1-12月
    const months: any[] = [];
    for (let i = 1; i <= 12; i++) {
      const monthStr = i.toString().padStart(2, '0');
      months.push({ value: monthStr, label: `${monthStr}月` });
    }
    return [years, months];
  }, []);

  /**
   * 处理日期选择确认事件
   * 更新显示的日期描述和选择器的值
   * 调用onConfirm回调函数
   * 
   * @param {PickerOptions} _ - 选择器选项（未使用）
   * @param {PickerValue[]} values - 选择的值，包含年份和月份
   */
  const handleConfirm = (_: PickerOptions, values: PickerValue[]) => {
    const year = values[0];
    const month = values[1];
    // 创建新的日期对象
    const newSelectedDate = new Date(Number(year), Number(month) - 1);

    // 格式化日期为显示文本
    const formattedDesc = formatDate(newSelectedDate, {
      template: 'YYYY年MM月',
    });

    // 更新状态
    setDesc(formattedDesc);
    setPickerValue([String(year), String(month)]);
    // 调用回调函数，传递格式化的日期字符串（YYYYMM）
    onConfirm?.(`${year}${String(month).padStart(2, '0')}`);
  };

  return (
    <>
      {/* 日期选择器触发区域 */}
      <View className="date-select" onClick={() => setIsVisible(true)}>
        <View className="date-select-control">
          <Text>{desc}</Text>
          <Arrow size={14} up={false} />
        </View>
      </View>
      {/* 日期选择器弹窗 */}
      <Picker
        visible={isVisible}
        options={options}
        defaultValue={pickerValue}
        onConfirm={handleConfirm}
        onClose={() => setIsVisible(false)}
      />
    </>
  );
};

// 使用memo包装组件，避免不必要的重渲染
export default memo(DateSelect);
