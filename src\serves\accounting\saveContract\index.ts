import { serveIp } from '@/serves/config'; // 使用serverIp.ip作为baseUrl
import http from '@/serves/http';
import { CommonResponse } from '@/serves/interface/miniProgram';
import { ContractEnterVO, SaveContractRes } from './interface'; // 新定义的类型

// 根据api文档生成的请求函数
/**
 * @description 录入合同
 * @param {ContractEnterVO} params
 * @returns {Promise<SaveContractRes>}
 */
export function saveAppContract(params: ContractEnterVO): Promise<CommonResponse<SaveContractRes>> {
  return http.post(`${serveIp.ip}/app-api/crm/contract/appSave`, params);
}
