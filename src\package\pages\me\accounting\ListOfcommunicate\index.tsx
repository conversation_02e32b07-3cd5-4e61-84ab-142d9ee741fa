import SvgIcon from '@/pages/me/components/SvgIcon';
import { formatCreditCode } from '@/utils/common';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { memo } from 'react';
import './index.scss';

// ListOfCommunicate 组件接口定义
interface ListOfCommunicateProps {
  list: any[]; // 您可以根据实际的 item 类型来定义更精确的类型
}

// 待沟通列表
const ListOfCommunicate: React.FC<ListOfCommunicateProps> = memo(({ list }) => {
  // 跳转到委案详情页面
  const handleItemClick = (item: any) => {
    Taro.navigateTo({
      url: `/package/pages/me/commissionDetail/index?debtorId=${item.debtorId}`,
    });
  };

  return (
    <View className="list-of-communicate">
      {list?.map((item) => (
        <View
          className="list-of-communicate-item"
          key={item.debtorId}
          onClick={() => handleItemClick(item)}
        >
          <View className="list-of-communicate-item-left">
            <View className="list-of-communicate-item-left-name">{item.debtorName}</View>
            <View className="list-of-communicate-item-left-phone">
              {formatCreditCode(item.debtorCreditCode)}
            </View>
          </View>
          <View className="list-of-communicate-item-right">
            <View className="list-of-communicate-item-right-commission">
              {item.caseCount}份委案
            </View>
            <SvgIcon name="arrow-right-small" size={14} />
          </View>
        </View>
      ))}
    </View>
  );
});

export default ListOfCommunicate;
