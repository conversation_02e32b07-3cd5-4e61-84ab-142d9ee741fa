import EmptyContainer from '@/components/empty-container';
import SvgIcon from '@/pages/me/components/SvgIcon';
import { GetContractListRes } from '@/serves/debt/interface';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { memo } from 'react';
import './index.scss';

const DebtList = ({ data }: { data: GetContractListRes['data'] }) => {
  if (data.length === 0) {
    return <EmptyContainer title="暂无还款债务" />;
  }
  return (
    <View className="my-debt-container">
      <View className="debt-list-container">
        {data.map((item, index) => (
          <View
            key={index}
            className="debt-list-item"
            onClick={() => {
              Taro.navigateTo({
                url: `/packageB/pages/debtDetail/index?caseEntrustId=${item.caseEntrustId}`,
              });
            }}
          >
            <View className="debt-list-item-header">
              <SvgIcon name="bankCard" size={16}></SvgIcon>
              <Text className="title">
                {item.creditorName} | 第{item.currentPeriod ?? 0}期/总{item.debtReductionPeriods}期
              </Text>
              <SvgIcon className="arrow-right" name="arrow-right-small" size={12}></SvgIcon>
            </View>
            {/* <View className="dividing"></View> */}
            <View className="debt-list-item-content">
              <View>
                <Text>{item.actualDebtReductionAmount || '-'}</Text>
                <Text>债务总额(元)</Text>
              </View>
              <View>
                <Text>{item.convertedDebtAmount || '-'}</Text>
                <Text>已化债金额(元)</Text>
              </View>
              <View>
                <Text>{item.remainingDebtAmount || '-'}</Text>
                <Text>剩余待化债</Text>
              </View>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

export default memo(DebtList);
