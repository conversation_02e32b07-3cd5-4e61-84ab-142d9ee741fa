Component({
  data: {
    selected: 0,
    color: '#000000',
    selectedColor: '#DC143C',
    list: [
      {
        pagePath: '/pages/home/<USER>',
        text: '首页',
      },
      {
        pagePath: '/pages/plan/index',
        text: '计划',
      },
      {
        pagePath: '/pages/evaluation/index',
        text: '测评',
      },
      // {
      //   pagePath: '/pages/message/index',
      //   text: '资讯',
      // },
      {
        pagePath: '/pages/me/index',
        text: '我的',
      },
    ],
  },
  attached() {},
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      wx.switchTab({ url });
      this.setData({
        selected: data.index,
      });
    },
  },
});
