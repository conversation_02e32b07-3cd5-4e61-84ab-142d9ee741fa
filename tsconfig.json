{"compilerOptions": {"target": "es2017", "module": "commonjs", "removeComments": false, "preserveConstEnums": true, "moduleResolution": "node", "experimentalDecorators": true, "noImplicitAny": false, "allowSyntheticDefaultImports": true, "outDir": "lib", "noUnusedLocals": true, "noUnusedParameters": true, "strictNullChecks": true, "sourceMap": true, "baseUrl": ".", "rootDir": ".", "jsx": "react-jsx", "paths": {"@/*": ["./src/*"], "@/images/*": ["./src/images/*"], "@/hooks/*": ["./src/hooks/*"], "@/store/*": ["./src/store/*"], "@/components/*": ["./src/components/*"], "@/pages/*": ["./src/pages/*"], "@/utils/*": ["./src/utils/*"], "@/serves/*": ["./src/serves/*"], "@/package": ["./package.json"], "@/project": ["./project.config.json"], "@/packageA/*": ["./src/packageA/*"]}, "allowJs": true, "resolveJsonModule": true, "typeRoots": ["node_modules/@types"]}, "include": ["./src", "./types"], "compileOnSave": false}