.calendar {
  .nut-calendarcard-day {
    height: 180px;

    justify-content: flex-start;

    &.current {
      min-width: 53px * 2;
    }
    &.header {
      width: 53px * 2;
      height: 40px * 2;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #000;
      text-align: center;

      text {
        color: #131313;
        font-size: 16px * 2;
        font-weight: 500;
      }
    }

    &.active {
      background-color: transparent;
    }

    &.next,
    &.prev {
      height: 0px;

      .nut-calendarcard-day-bottom {
        display: none;
        margin-top: 10px;
        flex: 1;
      }
    }
  }

  .nut-calendarcard-content {
    // padding: 40px 20px;
  }

  .nut-calendarcard-header {
    display: none;
  }

  .nut-calendarcard-day-top {
    display: none;
  }

  .nut-calendarcard-day-bottom {
    // height: fit-content;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    max-height: 20px * 2 * 3; // 最多3个
  }

  .day-todo-list {
    padding: 0 4px;
    width: 100%;
    .todo-item,
    .system {
      width: 100%;
      // display: inline-block;
      margin-top: 4px * 2;
      border-left: 4px solid #6a81fa;
      background-color: #ebf0fe;
      // width: fit-content;
      height: 17px * 2;
      padding: 0 8px;
      flex-shrink: 0;
      border-radius: 2px;
      // background: rgba(255, 0, 0, 0.10);
      display: block;
      color: #6a81fa;
      text-align: left;
      font-size: 10px * 2;
      line-height: 17px * 2;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .system {
      background-color: #ffefee;
      border-left: 4px solid #f00;
      color: #fe5655;
    }

    .not-current-day {
      opacity: 0.7;
    }
  }

  .day {
    color: #000;
    width: 50px;
    height: 50px;
    display: flex;
    &.active {
      color: #fff;
      background-color: #f00;
      border-radius: 50%;
      padding: 2px;
      box-sizing: border-box;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &-today {
      color: #f00;
    }

    &-weekend {
      color: #999;
    }

    &-other {
      display: none;
    }
  }
}
