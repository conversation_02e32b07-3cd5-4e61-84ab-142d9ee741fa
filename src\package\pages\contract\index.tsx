import EmptyContainer from '@/components/empty-container';
import LoadingPage from '@/components/loadingPage';
import { getContractSignPageList } from '@/serves/contruct';
import { ContractInfo } from '@/serves/contruct/interface';
import { downloadFile } from '@/utils/downloadFIle';
import { Button } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import Taro, { useDidShow } from '@tarojs/taro';
import { FC, useCallback } from 'react';
import { useRequest } from 'taro-hooks';
import './index.scss';

interface ContractOfMeProps {}

const contractList = [
  {
    title: '合同编号',
    val: 'contractNumber',
  },
  {
    title: '委案金额',
    val: 'caseEntrusAmount',
  },
  {
    title: '委案企业',
    val: 'caseEntrusCorporation',
  },
  {
    title: '签署方',
    val: 'signatory',
  },
  {
    title: '签署方式',
    val: 'signType',
  },
  {
    title: '签署时间',
    val: 'partyCTime',
  },
];
const ContractOfMe: FC<ContractOfMeProps> = () => {
  const { loading, data, refresh } = useRequest(
    () =>
      getContractSignPageList({
        pageNo: 1,
        pageSize: 100,
      }),
    {
      cacheKey: 'contract-sign-page-list',
      manual: true,
    },
  );

  useDidShow(() => {
    refresh();
  });

  const handleItemClick = useCallback(async (item: ContractInfo) => {
    try {
      Taro.showLoading({
        title: '加载中，请稍后',
        mask: true,
      });

      // 设置超时隐藏
      const timeout = setTimeout(() => {
        Taro.hideLoading();
        Taro.showToast({
          title: '加载失败，请检查网络',
          icon: 'none',
        });
      }, 5000); // 15秒后自动隐藏

      await downloadFile(item.contractUrl);
      clearTimeout(timeout); // 成功下载后清除超时
      Taro.hideLoading();
    } catch (error) {
      Taro.hideLoading();
      Taro.showToast({
        title: '下载失败, 请重试',
        icon: 'none',
      });
    }
  }, []);

  const handleSignatureClick = (event, item: ContractInfo) => {
    Taro.navigateTo({
      url: `/package/pages/contract/contractSignature/index?id=${item.id}&contractId=${item.contractNumber}`,
    });
    event.stopPropagation();
  };

  if (loading) {
    return <LoadingPage />;
  }

  if (data?.data.list?.length === 0) {
    return <EmptyContainer title="暂无合同" />;
  }

  return (
    <View className="contract-container">
      {data?.data.list?.map((item) => (
        <View
          className="contract-container-item"
          key={item.id}
          onClick={() => handleItemClick(item)}
        >
          <View className="contract-container-item-header">
            <Text>{item.contractType}</Text>
            <View className="contract-container-item-header-right">
              <Text className={item.partyCSignStatus === 1 ? 'complete' : ''}>
                {item.partyCSignStatus === 0 ? '待签署' : '签署完成'}
              </Text>
            </View>
          </View>
          <View className="contract-container-item-content">
            {contractList.map((l) => (
              <View className="contract-container-item-content-item" key={l.title}>
                <Text>{l.title}</Text>
                <Text>{item[l.val as keyof ContractInfo]}</Text>
              </View>
            ))}
          </View>

          {item.partyCSignStatus === 0 && (
            <Button
              onClick={(e) => handleSignatureClick(e, item)}
              size="mini"
              className="contract-container-item-button"
            >
              签署
            </Button>
          )}
        </View>
      ))}
    </View>
  );
};

export default ContractOfMe;
