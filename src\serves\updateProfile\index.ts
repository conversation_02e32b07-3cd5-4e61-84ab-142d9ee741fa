import { serveIp } from '@/serves/config';
import http from '@/serves/http';
import { CommonResponse } from '@/serves/interface/miniProgram';
import { MemberUserUpdateReqVO } from './interface';

/**
 * @description 更新会员用户
 * @param {MemberUserUpdateReqVO} params
 * @returns {Promise<CommonResponse<boolean>>}
 */
export function updateUser(params: MemberUserUpdateReqVO): Promise<CommonResponse<boolean>> {
  return http.put(`${serveIp.ip}/app-api/member/user/update`, params);
}
