import { reportHistoryDetail, result } from '@/serves';
import { Check } from '@nutui/icons-react-taro';
import { Progress, Radio } from '@nutui/nutui-react-taro';
import { Image, ScrollView, Text, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { useEnv } from 'taro-hooks';
import './index.scss';
const leftKey = {
  1: '7.5vw',
  2: '32.5vw',
  3: '57.5vw',
  4: '82.5vw',
  5: '107.5vw',
};
const list = [
  {
    key: 'SCET',
    value: '圈层运营官',
    color: '#FF9B44',
  },
  {
    key: 'SCEF',
    value: '迅捷捕猎手',
    color: '#B2E7ED',
  },
  {
    key: 'SCVT',
    value: '品传布道者',
    color: '#FFA970',
  },
  {
    key: 'SCVF',
    value: '内容造势官',
    color: '#F9B5C7',
  },
  {
    key: 'SPET',
    value: '生活筑构师',
    color: '#F9B4CC',
  },
  {
    key: 'SPEF',
    value: '危机洞察者',
    color: '#9BAFFA',
  },
  {
    key: 'SPVT',
    value: '人脉织网者',
    color: '#C2EFA3',
  },
  {
    key: 'SPVF',
    value: '刚需指挥官',
    color: '#95CBFE',
  },
  {
    key: 'NCET',
    value: '生态架构师',
    color: '#B2E7EB',
  },
  {
    key: 'NCEF',
    value: '商务操盘手',
    color: '#FDE573',
  },
  {
    key: 'NCVT',
    value: '心智造势者',
    color: '#FDA687',
  },
  {
    key: 'NCVF',
    value: '流量赋能师',
    color: '#9BAFFA',
  },
  {
    key: 'NPET',
    value: '洼地勘探者',
    color: '#F9B5C7',
  },
  {
    key: 'NPEF',
    value: '时空套利客',
    color: '#95CBFE',
  },
  {
    key: 'NPVT',
    value: '地推开拓者',
    color: '#FDE573',
  },
  {
    key: 'NPVF',
    value: '弱网联结者',
    color: '#7BBFFF',
  },
];
interface dataType {
  repaymentTime: string;
  c: string;
  debtAmount: string;
  e: string;
  f: string;
  n: string;
  p: string;
  s: string;
  t: string;
  v: string;
  scvtDO: {
    AdaptationCase: string[];
    code: string;
    coreGoals: string;
    coreGoalsDescribe: string;
    createdTime: number;
    creator: null;
    deleted: number;
    detailDescribe: string;
    id: number;
    KeyWords: string[];
    longTermGoals: string;
    longTermGoalsDescribe: string;
    nickName: string;
    overview: string;
    portrait: string;
    strategicValue: string;
    tenantId: null;
    updatedTime: number;
    updater: null;
  };
}
let normalData: dataType | null = null;
let parttimeData: dataType | null = null;
let fulltimeData: dataType | null = null;
const Index = () => {
  const env = useEnv();
  const [resultData, setResultData] = useState<dataType>({
    repaymentTime: '13',
    c: '50',
    debtAmount: '200',
    e: '75',
    f: '85.71428571428571',
    n: '50',
    p: '50',
    s: '50',
    t: '14.285714285714285',
    v: '25',
    scvtDO: {
      AdaptationCase: [],
      code: 'NPEF',
      coreGoals: '创造碎片化价值兑现场景',
      coreGoalsDescribe: '激活碎片化劳动价值，构建零工经济信用积累系统。',
      createdTime: 1740556990000,
      creator: null,
      deleted: 0,
      detailDescribe:
        '这类人群具备碎片化价值整合的独特天赋，通过构建零工经济信用体系激活时空差资源。其优势体现在非连续性劳动的价值转化能力，设计灵活兑换规则使社区巡逻、快递代收、问卷调查等零散服务转化为可存储的信用积分。他们建立的时空套利模型既释放闲置时间价值，又通过积分通兑体系实现跨场景债务抵扣，当就业市场波动时，其培育的零工网络可快速切换为应急收入来源。这种将间歇性劳动升维为可持续资本积累的能力，开创了灵活就业者的财务安全新模式。',
      id: 14,
      KeyWords: [],
      longTermGoals: '建立零工经济信用体系',
      longTermGoalsDescribe: '形成分布式价值交换网络，使非连续性时间投入转化为可持续消费资本。',
      nickName: '时空套利客',
      overview: '您通过时空差创造生存弹性，在灵活策略中重获生活主动权',
      portrait: '碎片化消费场景的价值兑现者',
      strategicValue: '时空差套利转化消费资本',
      tenantId: null,
      updatedTime: 1741170840000,
      updater: null,
    },
  });
  const [toTabView, setToTabView] = useState('tab1');
  const [toView, setToView] = useState('view1');
  const [activedLeft, setActivedLeft] = useState('7.5vw');
  const [currentModle, setCurrentModle] = useState('1');

  const { id } = useRouter().params;

  useEffect(() => {
    getData();
    return () => {
      normalData = null;
      parttimeData = null;
      fulltimeData = null;
    };
  }, []);
  const [colorkey, setColorKey] = useState({
    key: '',
    value: '',
    color: '',
    url: '',
  });
  const getColorkey = (key: string) => {
    let index = -1;
    let data = {
      key: '',
      value: '',
      color: '',
    };
    list.forEach((item, i) => {
      if (item.key === key) {
        index = i;
        data = item;
      }
    });
    if (index) {
      setColorKey({
        url: `https://guanjia.hunantianxing.com/static/images/${index + 1}.png`,
        ...data,
      });
    }
  };
  const getData = async (url?: string) => {
    const res = id ? await reportHistoryDetail(id as string) : await result(url);
    // const res = await reportHistoryDetail(id as string);
    if (res.data) {
      const data = res.data;
      getColorkey(data.scvtDO.code);
      const tempData = {
        c: `${data.c.toFixed(2)}`,
        e: `${data.e.toFixed(2)}`,
        f: `${data.f.toFixed(2)}`,
        n: `${data.n.toFixed(2)}`,
        p: `${data.p.toFixed(2)}`,
        s: `${data.s.toFixed(2)}`,
        t: `${data.t.toFixed(2)}`,
        v: `${data.v.toFixed(2)}`,
        repaymentTime: `${Math.floor(data.repaymentTime / 12)} 年 ${data.repaymentTime % 12} 月`,
        debtAmount: Number(data.debtAmount).toLocaleString('en-US'),
        scvtDO: {
          AdaptationCase: data.scvtDO.adaptationCase.split(';') as never[],
          KeyWords: data.scvtDO.keyWords.split(';'),
          ...data.scvtDO,
        },
      };
      if (url === '/part-time') {
        parttimeData = tempData;
      } else if (url === '/full-time') {
        fulltimeData == tempData;
      } else {
        normalData = tempData;
      }
      setResultData(tempData);
    }
  };
  const makephone = () => {
    Taro.makePhoneCall({
      phoneNumber: '400-0731-575',
      success(res) {
        // 调用成功 makePhoneCall:ok
        console.log('调用成功', res.errMsg);
      },
      fail(res) {
        // 调用失败 makePhoneCall:fail
        console.log('调用失败', res.errMsg);
      },
    });
  };
  return (
    <View className="wrapper">
      <ScrollView
        className="tabscroll"
        scroll-with-animation
        scroll-into-View={toTabView}
        enable-flex
        scroll-x
        onClick={(e) => {
          console.log(e.target);
          setActivedLeft(leftKey[e.target.dataset.value]);
          setToView('view' + e.target.dataset.value);
          if (e.target.dataset.value === '2') {
            setToTabView('tab1');
          } else if (e.target.dataset.value === '4') {
            setToTabView('tab5');
          }
        }}
      >
        <View className="tabView" id="tab1" data-value="1">
          价值实现时长
        </View>
        <View className="tabView" data-value="2">
          数据比对
        </View>
        <View className="tabView" data-value="3">
          画像解读
        </View>
        <View className="tabView" data-value="4">
          目标
        </View>
        <View className="tabView" id="tab5" data-value="5">
          策略价值锚点
        </View>
        <Text className="activedTab" style={{ transform: `translateX(${activedLeft})` }}></Text>
      </ScrollView>
      <ScrollView
        className="scrollarea"
        scroll-with-animation
        scroll-y
        enhanced
        scroll-into-View={toView}
        type="list"
      >
        <View id="view1" className="outBoxViewContainer">
          <View className="debtInfo">
            <View className="debtInfoTitle">
              <View>价值实现时长 </View>
              <Text>根据添星科技商业模式测算</Text>
            </View>

            <View className="debtInfoContent">
              <View>
                <Text>资金压力额度（元）</Text>
                <View className="price">
                  ¥{resultData.debtAmount}
                  <Text></Text>
                </View>
              </View>
              <View>
                <Text>价值实现时长预估</Text>
                <View>{resultData.repaymentTime}</View>
              </View>
            </View>
            <Radio.Group
              className="chooseMolde"
              defaultValue="1"
              value={currentModle}
              onChange={(value: string) => {
                console.log(value);
                setCurrentModle(value);
                if (value === '0') {
                  if (fulltimeData) {
                    setResultData(fulltimeData);
                  } else {
                    getData('/full-time');
                  }
                } else if (value === '1') {
                  if (normalData) {
                    setResultData(normalData);
                  } else {
                    getData();
                  }
                } else if (value === '2') {
                  if (parttimeData) {
                    setResultData(parttimeData);
                  } else {
                    getData('/part-time');
                  }
                }
              }}
            >
              <Radio
                icon={<Check size={'20rpx'} />}
                activeIcon={<Check size={'20rpx'} color="red" />}
                value="0"
              >
                标准工作制
              </Radio>
              <Radio
                icon={<Check size={'20rpx'} />}
                activeIcon={<Check size={'20rpx'} color="red" />}
                value="1"
              >
                灵活协作制
              </Radio>
              <Radio
                icon={<Check size={'20rpx'} />}
                activeIcon={<Check color="red" size={'20rpx'} />}
                value="2"
              >
                混合弹性制
              </Radio>
            </Radio.Group>
          </View>
          <View className="connectBox">
            <View className="connectUs">
              <View onClick={makephone}>
                <Image src="https://guanjia.hunantianxing.com/static/images/u385.png"></Image>
                联系我们
              </View>
              <View className="connectPhone" onClick={makephone}>
                <Text>客服电话 400-0731-575</Text>
              </View>
              <View className="Qrcode">
                <View>
                  <Image
                    src="https://guanjia.hunantianxing.com/static/images/u88.png"
                    show-menu-by-longpress="true"
                  ></Image>
                </View>
                {env !== 'TT' && (
                  <View>
                    <Image
                      show-menu-by-longpress="true"
                      src="https://guanjia.hunantianxing.com/static/images/u89.png"
                    ></Image>
                  </View>
                )}
              </View>
              <View className="QrcodeInfo">
                <View>
                  <View>@添星科技</View>
                  <View>抖音号:44144005274</View>
                  <View>湖南添星科技官方账号</View>
                </View>
                {env !== 'TT' && (
                  <View>
                    <View>@添星商城</View>
                    <View>微信公众号:添星商城</View>
                    <View>湖南添星科技官方公众号</View>
                  </View>
                )}
              </View>
            </View>
          </View>
        </View>
        <View id="view2" className="outBoxViewContainer" style="padding: 20rpx;">
          <View className="TestResult">
            <View className="TestResultBg">
              <Image src="https://guanjia.hunantianxing.com/static/miniprogramImage/tb-1.png"></Image>
            </View>
            <View className="TestContent">
              <View>
                <Text>SCVT模型人格测验</Text>
                <View>您的测试结果</View>
              </View>
              <View>
                <View style={{ backgroundColor: colorkey.color }}>
                  <Image src={colorkey.url}></Image>
                </View>
                <View style={{ color: colorkey.color }}>
                  <Text>{colorkey.key}</Text>
                  <View>{resultData.scvtDO.nickName}</View>
                </View>
              </View>
              <View>
                {resultData.scvtDO.KeyWords.map((item, index) => {
                  return item + (index === resultData.scvtDO.KeyWords.length - 1 ? '' : '/');
                })}
              </View>
            </View>
          </View>
        </View>
        <View id="view3" className="outBoxViewContainer">
          <View className="dataDetail">
            <View className="footerTitle">各项数据对比</View>
            <View className="dataAnalysis">
              <View>
                <View>
                  <Text>深度社交(S)</Text>
                  <View className="progressBox">
                    <Text>{resultData.s}%</Text>
                    <Progress
                      percent={parseFloat(resultData.s)}
                      color="#ff9d38"
                      background="#e6e5e5"
                      strokeWidth="7.5"
                      active-mode="forwards"
                    />
                  </View>
                </View>
                <View>
                  <View className="progressBox">
                    <Text>{resultData.n}%</Text>
                    <Progress
                      percent={parseFloat(resultData.n)}
                      color="#09dfc3"
                      background="#e6e5e5"
                      strokeWidth="7.5"
                      active-mode="forwards"
                    />
                  </View>
                  <Text>广度社交(N)</Text>
                </View>
              </View>
              <View>
                <View>
                  <Text>品质导向(C)</Text>
                  <View className="progressBox">
                    <Text>{resultData.c}%</Text>
                    <Progress
                      percent={parseFloat(resultData.c)}
                      color="#ff9d38"
                      background="#e6e5e5"
                      strokeWidth="7.5"
                      active-mode="forwards"
                    />
                  </View>
                </View>
                <View>
                  <View className="progressBox">
                    <Text>{resultData.p}%</Text>
                    <Progress
                      percent={parseFloat(resultData.p)}
                      color="#09dfc3"
                      background="#e6e5e5"
                      strokeWidth="7.5"
                      active-mode="forwards"
                    />
                  </View>
                  <Text>实用导向(P)</Text>
                </View>
              </View>
              <View>
                <View>
                  <Text>深度服务(E)</Text>
                  <View className="progressBox">
                    <Text>{resultData.e}%</Text>
                    <Progress
                      percent={parseFloat(resultData.e)}
                      color="#ff9d38"
                      background="#e6e5e5"
                      strokeWidth="7.5"
                      active-mode="forwards"
                    />
                  </View>
                </View>
                <View>
                  <View className="progressBox">
                    <Text>{resultData.v}%</Text>
                    <Progress
                      percent={parseFloat(resultData.v)}
                      color="#09dfc3"
                      background="#e6e5e5"
                      strokeWidth="7.5"
                      active-mode="forwards"
                    />
                  </View>
                  <Text>传播分销(V)</Text>
                </View>
              </View>
              <View>
                <View>
                  <Text>策略规划(T)</Text>
                  <View className="progressBox">
                    <Text>{resultData.t}%</Text>
                    <Progress
                      percent={parseFloat(resultData.t)}
                      color="#ff9d38"
                      background="#e6e5e5"
                      strokeWidth="7.5"
                      active-mode="forwards"
                    />
                  </View>
                </View>
                <View>
                  <View className="progressBox">
                    <Text>{resultData.f}%</Text>
                    <Progress
                      percent={parseFloat(resultData.f)}
                      color="#09dfc3"
                      background="#e6e5e5"
                      strokeWidth="7.5"
                      active-mode="forwards"
                    />
                  </View>
                  <Text>灵活响应(F)</Text>
                </View>
              </View>
            </View>
          </View>
        </View>
        <View id="view4" className="outBoxViewContainer">
          <View className="PortraitInterpretationBox">
            <View className="footerTitle">画像解读</View>
            <View className="PortraitInterpretation">
              <View>{resultData.scvtDO.portrait}</View>
              <View>
                <View className="pView">{resultData.scvtDO.overview}</View>
                <View className="pView">{resultData.scvtDO.detailDescribe}</View>
              </View>
            </View>
          </View>
        </View>
        <View id="view5" className="outBoxViewContainer">
          <View className="goalBox">
            <View className="footerTitle">目标</View>
            <View className="goal">
              <View>
                <Text>核心目标</Text>
                <View>{resultData.scvtDO.coreGoals}</View>
              </View>
              <View className="pView">{resultData.scvtDO.coreGoalsDescribe}</View>
              <View>
                <Text>长期目标</Text>
                <View>{resultData.scvtDO.longTermGoals}</View>
              </View>
              <View className="pView">{resultData.scvtDO.longTermGoalsDescribe}</View>
            </View>
          </View>
        </View>
        <View id="view6" className="outBoxViewContainer">
          <View className="strategyBox">
            <View className="footerTitle">策略价值锚点</View>
            <View className="strategyTitle"> {resultData.scvtDO.strategicValue}</View>
            <View className="strategyList">
              {resultData.scvtDO.AdaptationCase.map((item, index) => {
                return (
                  <View className="strategy" key={index + 'strategy'}>
                    <Text></Text>
                    <Text>{item}</Text>
                  </View>
                );
              })}
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default Index;
