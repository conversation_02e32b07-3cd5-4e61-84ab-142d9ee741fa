import { serveIp } from '../config';
import http from '../http';
import { CommonResponse } from '../interface/miniProgram';
import { AppPlanByIdRes, AppPlanByMonthRes } from './interface';

/**
 * 查询指定月份的工作计划列表
 * @param {string} yearMonth
 * @returns
 */
export function appPlanByMonth(yearMonth: string): Promise<CommonResponse<AppPlanByMonthRes[]>> {
  return http.get(`${serveIp.ip}/app-api/member/work-plan/list-by-month`, {
    yearMonth,
  });
}

/**
 * 查询工作计划详情
 * @param {string} id
 * @returns
 */
export function appPlanById(id: string): Promise<AppPlanByIdRes> {
  return http.get(`${serveIp.ip}/app-api/member/work-plan/${id}`);
}
