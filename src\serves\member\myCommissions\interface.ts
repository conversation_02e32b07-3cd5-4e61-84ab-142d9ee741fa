// Parameter interface
export interface GetMyEarningsParams {
  /*会员ID */
  memberUserId?: number;

  /*类型 0:收入 1:支出 2:待结算 */
  type?: number;
}

// Response interface
export interface GetMyEarningsRes {
  /* */
  code: number;

  /* */
  data: {
    /* */
    earningsCombination: {
      // 可提现
      canWithdraw: number;
      /*总收益 */
      sumEarnings: number;

      /*可用收益 */
      usableEarnings: number;

      /*待结算金额 */
      pendingSettlementMoney: number;

      /*冻结金额 */
      freezeMoney: number;
    };

    /*类型 0:收入 1:支出 */
    type: number;

    /*金额明细 */
    earningsMoneyDetailList: {
      /*债务类型 */
      debtType: string;

      /*名称 */
      name: string;

      /*金额 */
      mount: number;

      /*创建时间 */
      createTime: string;

      /*订单号 */
      orderNumber: string;

      /*助力账号 */
      cheatingAccount: string;

      /*邀请账号 */
      inviteAccount: string;

      /*审核状态 */
      auditStatus: number;
    }[];
  };

  /* */
  msg: string;
}
