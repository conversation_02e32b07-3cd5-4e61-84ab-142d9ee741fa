.my-list {
  display: flex;
  flex-direction: column;
  gap: 10px * 2;
  flex: 1;
  overflow: hidden;

  &-title {
    color: #000;
    font-size: 16px * 2;
    font-weight: 500;
  }

  &-tabs {
    display: flex;
    gap: 10px * 2;
    margin-bottom: 10px * 2;

    &-item {
      border-radius: 16px * 2;
      background: #fff;
      padding: 12px 34px;
      align-items: center;
      justify-content: center;

      &.active {
        color: #f00;
        font-size: 14px * 2;
        font-weight: 500;
      }

      &:not(.active) {
        color: #666;
        font-size: 14px * 2;
        font-weight: 400;
      }
    }
  }

  &-content {
    flex: 1;
    overflow-y: auto;
  }
}
