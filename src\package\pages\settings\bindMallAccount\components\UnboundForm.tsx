import { Input, Text, View } from '@tarojs/components';
import { memo } from 'react';

interface UnboundFormProps {
  phone: string;
  code: string;
  isPhoneValid: boolean;
  isCodeInputEnabled: boolean;
  isBindButtonEnabled: boolean;
  isRunning: boolean;
  count: number;
  onPhoneChange: (value: string) => void;
  onCodeChange: (value: string) => void;
  onSendCode: () => void;
  onBind: () => void;
}

/**
 * 未绑定状态的表单组件
 */
export const UnboundForm = memo<UnboundFormProps>(({
  phone,
  code,
  isPhoneValid,
  isCodeInputEnabled,
  isBindButtonEnabled,
  isRunning,
  count,
  onPhoneChange,
  onCodeChange,
  onSendCode,
  onBind,
}) => {
  return (
    <View className="bind-mall-account">
      <View className="context">
        {/* 手机号输入框 */}
        <View className="input-container">
          <View className="input-wrapper">
            <Text className="label">商城手机号</Text>
            <Input
              className="input"
              placeholder="请输入商城账户手机号"
              placeholderClass="placeholder"
              value={phone}
              type="number"
              maxlength={11}
              onInput={(e) => onPhoneChange(e.detail.value)}
            />
          </View>
          <View className="divider" />
        </View>

        {/* 验证码输入框 */}
        <View className="input-container">
          <View className="input-wrapper">
            <Text className="label">验证码</Text>
            <Input
              className="input code-input"
              placeholder="输入验证码"
              placeholderClass="placeholder"
              value={code}
              type="number"
              maxlength={6}
              disabled={!isCodeInputEnabled}
              onInput={(e) => onCodeChange(e.detail.value)}
            />
            <View
              className={`get-code ${!isPhoneValid || isRunning ? 'disabled' : ''}`}
              onClick={onSendCode}
            >
              {isRunning ? `重新发送(${count}s)` : '获取验证码'}
            </View>
          </View>
          <View className="divider" />
        </View>

        {/* 绑定按钮 */}
        <View
          className={`bind-button ${!isBindButtonEnabled ? 'disabled' : ''}`}
          onClick={onBind}
        >
          <Text className="bind-button-text">绑定</Text>
        </View>
      </View>
      
      <View className="footer">
        <Text>绑定商城账户要求：</Text>
        <Text>1.添星商城账户已经绑定手机号；</Text>
        <Text>2.手机号能正常接收短信；</Text>
        <Text>3.输入的短信验证码在有效期内（短信验证码5分钟）。</Text>
      </View>
    </View>
  );
});
