.contract-container {
  width: 100%;
  height: 100%;
  background-color: #f5f4f9;
  padding: 28px 24px;
  display: flex;
  flex-direction: column;
  gap: 28px;
  --nutui-button-mini-font-size: 13px * 2;
  overflow: auto;
  padding-bottom: env(safe-area-inset-bottom);

  .contract-container-item-button {
    font-weight: 500;
  }

  &-item {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 32px;
    border-radius: 16px;

    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      text:first-child {
        color: #131313;
        font-size: 14px * 2;
        font-weight: 500;

        &.complete {
          padding: 4px;
          color: #4b81ee;
          font-size: 12px * 2;
          background: rgba(75, 129, 238, 0.1);
        }
      }

      &-right {
        display: flex;
        align-items: center;
        gap: 12px;

        text:first-child {
          padding: 4px;
          color: #fba030;
          background: rgba(251, 160, 48, 0.1);
          font-size: 14px * 2;
          font-size: 12px * 2;
        }
      }
    }

    &-content {
      display: flex;
      flex-direction: column;
      gap: 12px;

      &-item {
        display: flex;
        align-items: center;
        gap: 16px;

        text:first-child {
          width: 70px * 2;
          text-align: left;
          color: #666;
          font-size: 14px * 2;
        }

        text:last-child {
          color: #131313;
          font-size: 14px * 2;
        }
      }
    }

    &-button {
      width: 74px * 2;
      height: 28px * 2;
      flex-shrink: 0;
      border-radius: 4px * 2;
      border: 1px solid #f00;
      color: #f00;
      font-size: 13px * 2;
      font-weight: 500;
      margin-left: auto;
    }
  }
}
