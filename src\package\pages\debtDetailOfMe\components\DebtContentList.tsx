import EmptyContainer from '@/components/empty-container';
import useDict from '@/serves/dict/hooks';
import { Text, View } from '@tarojs/components';
import { FC, useMemo } from 'react';
import './DebtContentList.scss';

interface Data {
  id?: number;
  debtType?: string;
  description?: string;
  orderNumber?: string;
  type?: number;
  mount?: number;
  createTime?: string;
  [key: string]: any;
}

interface DebtContentListProps {
  /** 债务数据 */

  data: Data[];
  /** 加载状态 */
  loading?: boolean;
}

enum DebtType {
  '消费' = 'consume',
  '助力' = 'help',
  '佣金' = 'commission',
}

/**
 * 债务内容列表组件 - 直接搬运原有代码
 */
const DebtContentList: FC<DebtContentListProps> = ({ data, loading }) => {
  const { dictData } = useDict();
  const debtDict = useMemo(() => {
    return dictData?.filter((item: any) => item.dictType === 'debt_type');
  }, [dictData]);

  const getDebtType = useMemo(() => {
    return (debtType: string) => {
      return debtDict?.find((dictItem: any) => dictItem.value === debtType)?.label || '';
    };
  }, [debtDict]);

  return (
    <>
      {!data?.length && !loading ? (
        <View className="all-tab-pane-loading">
          <EmptyContainer title="暂无数据" />
        </View>
      ) : (
        <View className="all-tab-pane-content">
          {data?.map((item, index) => (
            <View className="all-tab-pane-content-item" key={index}>
              <View className="header">
                <View className="header-left">
                  <View
                    className={`header-left-icon ${DebtType[item.debtType as keyof typeof DebtType]}`}
                  >
                    {getDebtType(item?.debtType || '')?.slice(0, 1) || ''}
                  </View>
                  {/* <SvgIcon name="icon_xf" size={16} /> */}
                  <Text className="header-title">{getDebtType(item?.debtType || '')}</Text>
                </View>
                <Text className="header-date">{item?.createTime}</Text>
              </View>
              <View className="content">
                <View className="content-left">
                  <Text className="content-left-title">{item?.description ?? item?.name}</Text>
                  <Text className="content-left-order-number">订单号：{item?.orderNumber}</Text>
                </View>
                <Text className="content-price">
                  {item?.type === 0 ? '+' : '-'}
                  {item?.mount}
                </Text>
              </View>
            </View>
          ))}
        </View>
      )}
    </>
  );
};

export default DebtContentList;
