import { View } from '@tarojs/components';
import React from 'react';
import './index.scss';

interface CommissionDetailInfoProps {
  label: string;
  value: string;
  desc?: string;
  icon?: React.ReactNode;
  valueLine?: boolean;
}

const CommissionDetailInfo = (props: CommissionDetailInfoProps) => {
  return (
    <View className="commission-detail-info-item">
      {props.icon}
      <View className="label">{props.label}</View>
      <View className={`value ${props.valueLine ? 'value-line' : ''}`}>{props.value}</View>
      <View className="desc">{props.desc}</View>
    </View>
  );
};

export default CommissionDetailInfo;
