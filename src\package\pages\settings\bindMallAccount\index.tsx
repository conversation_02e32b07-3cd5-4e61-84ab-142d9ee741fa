import { useLoad } from '@tarojs/taro';

import PageContainerTmp from '@/components/pageContainerTemp';
import { BoundInfo } from './components/BoundInfo';
import { UnboundForm } from './components/UnboundForm';
import { useBindMallAccountLogic } from './hooks/useBindMallAccountLogic';
import { useBindMallAccountState } from './hooks/useBindMallAccountState';
import './index.scss';

const BindMallAccount = () => {
  // 状态管理Hook
  const {
    phone,
    code,
    isBound,
    isPhoneValid,
    isCodeInputEnabled,
    isBindButtonEnabled,
    count,
    isRunning,
    startCountdown,
    setPhone,
    setCode,
    setCodeSentStatus,
    setBoundStatus,
    switchToBindPage,
  } = useBindMallAccountState();

  // 业务逻辑Hook
  const { sendVerificationCode, bindMallAccount, getBoundAccountInfo } = useBindMallAccountLogic();

  useLoad(() => {
    console.log('绑定商城账户页面加载完成');
  });

  // 发送验证码
  const handleSendCode = async () => {
    await sendVerificationCode(phone, isPhoneValid, isRunning, () => {
      setCodeSentStatus(true);
      startCountdown();
    });
  };

  // 绑定商城账户
  const handleBind = async () => {
    await bindMallAccount(phone, code, isBindButtonEnabled, () => {
      setBoundStatus(true);
    });
  };

  // 获取已绑定的账户信息
  const boundAccountInfo = getBoundAccountInfo();

  return (
    <PageContainerTmp
      title="绑定商城账户"
      marksType="none"
      marksClassName="marks-bind-mall-account"
      showTabbar={false}
      isRet
      align="center"
    >
      {!isBound ? (
        <UnboundForm
          phone={phone}
          code={code}
          isPhoneValid={isPhoneValid}
          isCodeInputEnabled={isCodeInputEnabled}
          isBindButtonEnabled={isBindButtonEnabled}
          isRunning={isRunning}
          count={count}
          onPhoneChange={setPhone}
          onCodeChange={setCode}
          onSendCode={handleSendCode}
          onBind={handleBind}
        />
      ) : (
        <BoundInfo accountInfo={boundAccountInfo} onSwitchBind={switchToBindPage} />
      )}
    </PageContainerTmp>
  );
};

export default BindMallAccount;
