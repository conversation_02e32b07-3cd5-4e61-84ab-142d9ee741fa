/* AdItem 组件样式 - CSS 类实现 */

/* 基础容器样式 */
.ad-item-basic {
  width: 100%;
  height: 120px;
  border-radius: 12px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  padding: 24px;
  padding-bottom: 26px;
  gap: 12px;
}

/* 分割项样式 */
.ad-item-split {
  /* 分割项基础样式 */
}

.ad-item-split .ad-item-top {
  display: flex;
  gap: 12px;
}

.ad-item-split .ad-item-title {
  font-size: 24px;
  font-weight: 400;
}

.ad-item-split .ad-item-desc {
  font-size: 22px;
  font-weight: 400;
}

/* 背景SVG样式 */
.ad-item-bg-svg {
  background-size: cover;
  background-repeat: no-repeat;
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 普通类型样式 */
.ad-item-common {
  background: linear-gradient(90deg, #9691C2 0%, #D2CFEB 28.14%, #D2CFEB 75.42%, #9691C2 100%);
}

.ad-item-common .ad-item-title {
  color: #3C3466;
}

.ad-item-common .ad-item-desc {
  color: #635C94;
}

.ad-item-common .ad-item-split {
  border-right: 1px solid rgba(118, 113, 160, 0.4);
}

.ad-item-common .ad-item-split:last-child {
  border-right: none !important;
}

/* 高级类型样式 */
.ad-item-advanced {
  background: linear-gradient(90deg, #E9C890 0%, #F7E4C0 28.14%, #F7E4C0 75.42%, #E9C890 100%);
}

.ad-item-advanced .ad-item-split {
  border-right: 1px solid rgba(118, 113, 160, 0.4);
}

.ad-item-advanced .ad-item-split:last-child {
  border-right: none !important;
}

.ad-item-advanced .ad-item-title {
  color: #76260D;
}

.ad-item-advanced .ad-item-desc {
  color: #AE783B;
}

/* 组合类样式 */
.ad-item-container {
  /* 可以组合使用的容器类 */
}

.ad-item-container.common {
  /* 普通类型的容器 */
}

.ad-item-container.advanced {
  /* 高级类型的容器 */
}