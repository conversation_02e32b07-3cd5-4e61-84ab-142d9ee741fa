# 子组件抽离判断标准

## 1. 子组件抽离的判断标准

### 1.1 功能独立性标准
**判断依据**：组件是否具有独立的功能职责

```typescript
// ✅ 功能独立 - 适合抽离
// UnboundForm 组件具有完整的表单功能
interface UnboundFormProps {
  phone: string;
  code: string;
  onPhoneChange: (value: string) => void;
  onCodeChange: (value: string) => void;
  onSendCode: () => void;
  onBind: () => void;
}

// ❌ 功能不独立 - 不适合抽离
// 仅仅是一个输入框，功能过于简单
interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
}
```

### 1.2 复用性标准
**判断依据**：组件是否在多个地方被使用或有被复用的潜力

```typescript
// ✅ 高复用性 - 适合抽离
// 验证码输入组件可在多个页面复用
interface VerificationCodeInputProps {
  value: string;
  length: number;
  onComplete: (code: string) => void;
  onResend: () => void;
  countdown: number;
}

// ✅ 潜在复用性 - 适合抽离
// 账户信息展示组件可能在其他账户相关页面复用
interface AccountInfoProps {
  accountInfo: {
    phone: string;
    accountId: string;
    bindTime: string;
  };
  onEdit?: () => void;
}
```

### 1.3 复杂度标准
**判断依据**：组件的JSX结构是否足够复杂

```typescript
// ✅ 复杂度适中 - 适合抽离
// BoundInfo 组件包含多个信息项和交互
const BoundInfo = ({ accountInfo, onSwitchBind }) => (
  <View className="bound-mall-account">
    <View className="bound-context">
      {/* 多个信息项 */}
      <InfoItem label="商城手机号" value={accountInfo.phone} />
      <InfoItem label="商城账号" value={accountInfo.accountId} />
      <InfoItem label="绑定时间" value={accountInfo.bindTime} />
    </View>
    <View className="switch-bind-mall-account">
      <ActionItem label="切换绑定商城账户" onClick={onSwitchBind} />
    </View>
  </View>
);

// ❌ 复杂度过低 - 不适合抽离
const SimpleText = ({ text }) => <Text>{text}</Text>;
```

### 1.4 状态边界标准
**判断依据**：组件是否有清晰的状态边界

```typescript
// ✅ 状态边界清晰 - 适合抽离
// 表单组件有明确的输入输出边界
interface FormComponentProps {
  // 输入状态
  initialData: FormData;
  // 输出回调
  onSubmit: (data: FormData) => void;
  onCancel: () => void;
}

// ❌ 状态边界模糊 - 不适合抽离
// 需要访问父组件的多个状态
interface ConfusedComponentProps {
  // 需要父组件的多个状态，边界不清晰
  parentState1: any;
  parentState2: any;
  parentState3: any;
  // ...
}
```

## 2. 子组件粒度确定原则

### 2.1 业务逻辑边界原则
按照业务功能的自然边界划分组件

```typescript
// ✅ 按业务边界划分
// 绑定表单 - 完整的绑定业务逻辑
const UnboundForm = () => { /* 绑定相关的所有UI */ };

// 账户信息 - 完整的信息展示逻辑  
const BoundInfo = () => { /* 账户信息相关的所有UI */ };

// ❌ 按技术实现划分
const InputSection = () => { /* 只包含输入框 */ };
const ButtonSection = () => { /* 只包含按钮 */ };
```

### 2.2 用户交互边界原则
按照用户的操作流程划分组件

```typescript
// ✅ 按用户交互流程划分
// 用户完成绑定的完整流程
const BindingFlow = () => (
  <>
    <PhoneInput />
    <VerificationCodeInput />
    <BindButton />
    <BindingRules />
  </>
);

// 用户查看和管理已绑定账户的流程
const AccountManagement = () => (
  <>
    <AccountInfo />
    <SwitchAccountAction />
  </>
);
```

### 2.3 数据流边界原则
按照数据的流向和依赖关系划分组件

```typescript
// ✅ 数据流边界清晰
interface UnboundFormProps {
  // 输入数据
  phone: string;
  code: string;
  // 状态数据
  isPhoneValid: boolean;
  isCodeInputEnabled: boolean;
  // 输出回调
  onPhoneChange: (value: string) => void;
  onCodeChange: (value: string) => void;
  onSendCode: () => void;
  onBind: () => void;
}
```

## 3. Props设计原则

### 3.1 最小化原则
只传递组件必需的props

```typescript
// ✅ 最小化props
interface UnboundFormProps {
  phone: string;
  code: string;
  isPhoneValid: boolean;
  onPhoneChange: (value: string) => void;
  onBind: () => void;
}

// ❌ 传递过多props
interface UnboundFormProps {
  phone: string;
  code: string;
  isPhoneValid: boolean;
  // 不必要的props
  userInfo: UserInfo;
  appConfig: AppConfig;
  theme: Theme;
}
```

### 3.2 语义化原则
Props命名应该清晰表达其用途

```typescript
// ✅ 语义化命名
interface BoundInfoProps {
  accountInfo: AccountInfo;
  onSwitchBind: () => void;
}

// ❌ 模糊命名
interface BoundInfoProps {
  data: any;
  callback: () => void;
}
```

### 3.3 类型安全原则
使用TypeScript确保类型安全

```typescript
// ✅ 类型安全
interface AccountInfo {
  phone: string;
  accountId: string;
  bindTime: string;
}

interface BoundInfoProps {
  accountInfo: AccountInfo;
  onSwitchBind: () => void;
}

// ❌ 类型不安全
interface BoundInfoProps {
  accountInfo: any;
  onSwitchBind: any;
}
```

### 3.4 可选性原则
合理使用可选属性

```typescript
// ✅ 合理的可选属性
interface FormProps {
  // 必需属性
  onSubmit: (data: FormData) => void;
  // 可选属性
  initialData?: FormData;
  disabled?: boolean;
  showValidation?: boolean;
}

// ❌ 过多可选属性
interface FormProps {
  onSubmit?: (data: FormData) => void; // 核心功能不应该可选
  data?: any;
  config?: any;
  // ...
}
```

## 4. 组件抽离决策流程

### 4.1 决策检查清单

```markdown
## 组件抽离决策检查清单

### 基础条件（必须满足）
- [ ] 组件具有独立的功能职责
- [ ] 组件有清晰的输入输出边界
- [ ] 组件的JSX结构足够复杂（>20行）

### 优化条件（满足任一即可）
- [ ] 组件在多个地方被使用
- [ ] 组件有被复用的潜力
- [ ] 组件包含复杂的交互逻辑
- [ ] 组件有独立的状态管理需求

### 质量条件（建议满足）
- [ ] Props设计合理，数量适中
- [ ] 组件命名语义化
- [ ] 类型定义完整
- [ ] 可以独立测试
```

### 4.2 决策流程图

```
开始
  ↓
功能是否独立？ → 否 → 不抽离
  ↓ 是
复杂度是否足够？ → 否 → 不抽离
  ↓ 是
是否有复用价值？ → 否 → 考虑其他因素
  ↓ 是
Props设计是否合理？ → 否 → 重新设计
  ↓ 是
抽离为子组件
```
