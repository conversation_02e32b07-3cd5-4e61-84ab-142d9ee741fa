import EmptyContainer from '@/components/empty-container';
import { Tabs } from '@nutui/nutui-react-taro';
import { memo, useCallback } from 'react';
import ListOfCommunicate from './ListOfcommunicate';

// TabsList 组件 props 接口定义
interface TabsListProps {
  list: any[];
  value: string | number;
  onChange: (value: string | number) => void;
}

// TabsList 组件
const TabsList: React.FC<TabsListProps> = memo(({ list, value, onChange }) => {
  const handleChange = useCallback(
    (val: string | number) => {
      onChange(val);
    },
    [onChange],
  );

  const renderPaneContent = useCallback(
    (paneValue: string, emptyTitle: string) => (
      <div
        style={{
          display: value === paneValue ? 'block' : 'none',
        }}
      >
        {list.length > 0 ? (
          <ListOfCommunicate list={list} />
        ) : (
          <EmptyContainer title={emptyTitle} />
        )}
      </div>
    ),
    [list, value],
  );

  return (
    <Tabs value={value} onChange={handleChange} className="tabs-list">
      <Tabs.TabPane title="待沟通" value="0">
        {renderPaneContent('0', '暂无待沟通记录')}
      </Tabs.TabPane>
      <Tabs.TabPane title="跟进" value="1">
        {renderPaneContent('1', '暂无跟进记录')}
      </Tabs.TabPane>
      <Tabs.TabPane title="已完成" value="2">
        {renderPaneContent('2', '暂无已完成记录')}
      </Tabs.TabPane>
      <Tabs.TabPane title="全部" value="3">
        {renderPaneContent('3', '暂无记录')}
      </Tabs.TabPane>
    </Tabs>
  );
});

export default TabsList;
