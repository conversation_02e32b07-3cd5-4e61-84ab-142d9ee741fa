import ElevatorThreeLevel from '@/components/area';
import CustomPicker from '@/packageA/pages/InforEntry/customPicker';
import { ArrowRight } from '@nutui/icons-react-taro';
import { Form } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import { useState } from 'react';

/**
 * 地址选择表单项组件接口定义
 * @interface AddressFormItemProps
 * @property {any} formRef - 表单引用对象，用于操作表单数据
 * @property {string} label - 表单项标签文本
 * @property {string} name - 表单项字段名
 */
interface AddressFormItemProps {
  formRef: any;
  label: string;
  name: string;
}

/**
 * 地址选择表单项组件
 * 提供一个可点击的表单项，点击后弹出三级地址选择器
 * 
 * @param {AddressFormItemProps} props - 组件属性
 * @returns {JSX.Element} 地址选择表单项组件
 */
const AddressFormItem = ({ formRef, label, name }: AddressFormItemProps) => {
  // 控制地址选择弹窗的显示状态
  const [addressPickerVisible, setAddressPickerVisible] = useState(false);
  // 存储地址选择器组件的模板
  const [pickerTemplete, setPickerTemplete] = useState<React.ReactNode>(null);

  return (
    <>
      <Form.Item
        label={label}
        name={name}
        rules={[{ required: true, message: '请选择地点' }]}
        onClick={() => {
          // 点击表单项时，显示地址选择弹窗
          setAddressPickerVisible(true);
          // 设置地址选择器组件，并配置回调函数
          setPickerTemplete(
            <ElevatorThreeLevel
              onchange={(data) => {
                console.log('data', data);
                // 将选择的地址数据设置到表单中
                formRef.current?.setFieldsValue({
                  address: data,
                });
              }}
              initvalue={[null, null, null]} // 初始化为三级全空的数组
            />,
          );
        }}
      >
        <View style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
          <Text>
            {/* 如果地址已选择完整，则显示级联地址名称，否则显示提示文本 */}
            {formRef.current?.getFieldValue(name)?.every((item) => item !== null)
              ? formRef.current
                  ?.getFieldValue(name)
                  .map((item: any) => item?.name)
                  .join('  ')
              : '请选择地点'}
          </Text>
          <ArrowRight size={12} />
        </View>
      </Form.Item>

      {/* 自定义选择器弹窗 */}
      <CustomPicker
        title="请选择地址"
        visible={addressPickerVisible}
        position="bottom"
        onClose={() => {
          setAddressPickerVisible(false);
        }}
        customNode={pickerTemplete} // 传入自定义的地址选择器组件
      />
    </>
  );
};

export default AddressFormItem;
