import { getSvgPath, type SvgName } from '@/utils/svgLoader';
import { useMemo } from '@taro-hooks/core';
import { Text, View } from '@tarojs/components';
import SvgIcon from '../SvgIcon';
import "./index.scss";

interface SvgIconProps {
  // name: SvgName; // SVG 文件名（不带扩展名）
  className?: string; // 自定义类名
  type: 'common' | 'advanced'; // 图标类型
}

const data = {
  common: [
    {
      icon: 'ad-icon1',
      text: '普通合伙人',
      desc: '本人消费抵扣权益'
    },
    {
      icon: 'ad-icon2',
      text: '厂家直供商品',
      desc: '现金交易额的0.8%'
    },
    {
      icon: 'ad-icon3',
      text: '会员引荐',
      desc: '会员年费提成15%'
    }
  ],
  advanced: [
    {
      icon: 'ad-icon1',
      text: '高级合伙人',
      desc: '本人消费抵扣权益'
    },
    {
      icon: 'ad-icon2',
      text: '厂家直供商品',
      desc: '现金交易额的1%'
    },
    {
      icon: 'ad-icon3',
      text: '会员引荐',
      desc: '会员年费提成15%'
    }
  ]
}

export default function AdItem({ className = '', type = 'common' }: SvgIconProps) {
  const bgSvg = `bg_ad_icon_${type}` as SvgName
  const dataList = useMemo(() => {
    return data[type]
  }, [type])

  return (
    <View className={`basic ${type} ${className}`}>
      {
        dataList.map((item, index) => {
          return <View key={index} className='split' >
            {/* top */}
            <View className='top'>
              <View className='bg-svg' style={{
                backgroundImage: `url(${getSvgPath(bgSvg)})`,
              }}>
                <SvgIcon
                  name={item.icon as SvgName}
                  size={12}
                />
              </View>
              <Text className='title'>{item.text}</Text>
            </View>
            {/* bottom */}
            {/* <View> */}
            <Text className='desc'>
              {item.desc} {'>'}
            </Text>
            {/* </View> */}
          </View>
        })
      }
    </View >
  );
}