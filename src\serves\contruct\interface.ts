// Parameter interface
export interface GetContractSignPageListParams {
  /*页码，从 1 开始 */
  pageNo: number;

  /*每页条数，最大值为 100 */
  pageSize: number;

  /*丙方签订状态 0:未签订 1:已签订 */
  partyCSignStatus?: number;
}

// Response interface
export interface ContractInfo {
  /*合同ID */
  id: number;

  /*合同类型 */
  contractType: string;

  /*合同编号 */
  contractNumber: string;

  /*案件委托ID */
  caseEntrustId: number;

  /*委案金额 */
  caseEntrusAmount: number;

  /*委案企业 */
  caseEntrusCorporation: string;

  /*签署方 */
  signatory: string;

  /*签署方式 */
  signType: string;

  /*签署模板URL */
  contractUrl: string;

  /*丙方签订状态 0:未签订 1:已签订 */
  partyCSignStatus: number;
}
export interface GetContractSignPageListRes {
  /* */
  code: number;

  /* */
  data: {
    /*数据 */
    list: ContractInfo[];

    /*总量 */
    total: number;
  };

  /* */
  msg: string;
}

// Parameter interface
export interface ContractSignParams {
  /*合同ID */
  id: number;

  /*签署图片 */
  singImage: string;
}

// Response interface
export interface ContractSignRes {
  /* */
  code: number;

  /* */
  data: boolean;

  /* */
  msg: string;
}
