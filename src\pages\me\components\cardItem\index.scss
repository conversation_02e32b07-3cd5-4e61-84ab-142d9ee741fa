.icon-item {
  padding: 6px 12px;
  border-radius: 16px;
  background: linear-gradient(90deg, #9b98c9 22.39%, #584f9d 100%);
  font-size: 20px;
  color: #fff;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: left;
  gap: 4px * 2;

  &.card-item {
    flex-shrink: 0;
    padding-right: 0px;
    .icon {
      transform: translateX(-20px);
    }
    text {
      transform: translateX(-20px);
    }
  }

  &.no-sm {
    flex-shrink: 0;
    background: rgba(102, 102, 102, 0.1);
    color: #666;
    font-size: 10px * 2;
    font-weight: 400;
  }
}
