import { View } from '@tarojs/components';
import './index.scss';

interface StepItemProps {
  status: 'undone' | ''; // 1 | 2 | 3 -> 未结清 | 已结清 | 逾期
  left?: React.ReactNode;
  right?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

export default function StepItem({
  status = 'undone',
  left,
  right,
  className,
  style,
}: StepItemProps) {
  return (
    <View className={`step-item ${className}`} style={style}>
      <View className="step-period-left">{left}</View>

      <View className="step-period-line-container">
        <View className={`step-period-dot ${status}`}></View>
        <View className="step-period-line"></View>
      </View>

      <View className="step-period-right">{right}</View>
    </View>
  );
}
