import AddressFormItem from '@/components/AddressFormItem';
import { useTodoStore } from '@/store/newTodoStore';
import { formatDate } from '@/utils/date';
import { Add, ArrowRight, Close } from '@nutui/icons-react-taro';
import {
  Button,
  Cell,
  DatePicker,
  Form,
  Input,
  Picker,
  PickerOptions,
} from '@nutui/nutui-react-taro';
import { Switch, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useRef, useState } from 'react';
import './index.scss';

const NewTodo = () => {
  const { reminderList, reminderTypes } = useTodoStore();

  // 添加 form 引用
  const formRef = useRef<any>(null);

  const onSave = () => {
    // 通过 formRef 手动触发表单提交
    formRef.current?.submit();
  };

  const onFormFinish = (values: IForm) => {
    // 在这里可以获取所有数据
    const todoData = {
      formData: values,
      reminders: reminderList,
      reminderTypes,
    };
    console.log('保存的数据：', todoData);
    Taro.redirectTo({
      url: '/package/pages/newTodo/TodoDetail/index',
    });
  };

  return (
    <View className="new-todo-container">
      <FormContainer formRef={formRef} onFinish={onFormFinish} />
      <Reminder />
      <ReminderTypeSelect />
      <Button className="confirm-btn" shape="round" type="primary" onClick={onSave}>
        保存
      </Button>
    </View>
  );
};

// form
interface IForm {
  title: string;
  address: string;
  backup: string;
  startTime: Date;
  endTime: Date;
}
const FormContainer = ({
  formRef,
  onFinish,
}: {
  formRef: any;
  onFinish: (values: IForm) => void;
}) => {
  return (
    <Form divider ref={formRef} onFinish={onFinish}>
      <Form.Item label="标题" name="title" rules={[{ required: true, message: '请输入标题' }]}>
        <Input placeholder="请输入标题" type="text" />
      </Form.Item>
      <AddressFormItem formRef={formRef} label="地点" name="address" />
      <Form.Item label="备注" name="backup" rules={[{ required: true, message: '请输入日程备注' }]}>
        <Input placeholder="请输入日程备注" type="text" />
      </Form.Item>

      <Form.Item
        label="开始时间"
        name="startTime"
        trigger="onConfirm"
        onClick={(event, ref: any) => {
          ref.open();
        }}
        getValueFromEvent={(...args) => {
          const time = args[1];
          return new Date(`${time[0]}-${time[1]}-${time[2]} ${time[3]}:${time[4]}`);
        }}
        initialValue={new Date()}
      >
        <DatePicker type="datetime">
          {(value: any) => {
            return (
              <View className="time-picker">
                <Text>{value ? formatDate(value, { showTime: true }) : '请选择开始时间'}</Text>
                <ArrowRight size={12} />
              </View>
            );
          }}
        </DatePicker>
      </Form.Item>
      <Form.Item
        label="结束时间"
        name="endTime"
        trigger="onConfirm"
        getValueFromEvent={(...args) => {
          const time = args[1];
          return new Date(`${time[0]}-${time[1]}-${time[2]} ${time[3]}:${time[4]}`);
        }}
        onClick={(event, ref: any) => {
          ref.open();
        }}
        initialValue={new Date()}
      >
        <DatePicker type="datetime">
          {(value: any) => {
            return (
              <View className="time-picker">
                <Text>{value ? formatDate(value, { showTime: true }) : '请选择结束时间'}</Text>
                <ArrowRight size={12} />
              </View>
            );
          }}
        </DatePicker>
      </Form.Item>
    </Form>
  );
};

// 提醒列表
const options = [
  [
    '日程开始时',
    '5分钟前',
    '10分钟前',
    '30分钟前',
    '1小时前',
    '2小时前',
    '1天前',
    '2天前',
    '1周前',
  ].map((item, index) => ({
    label: item,
    value: index + 1,
  })),
];
const Reminder = () => {
  const { reminderList, setReminderList } = useTodoStore();
  const [visiblePicker, setVisiblePicker] = useState(false);

  const confirmPicker = (selectedOptions: PickerOptions) => {
    const newList = [...(reminderList || []), selectedOptions[0]];
    setReminderList(newList);
    setVisiblePicker(false);
  };

  const removeReminderItem = (removeIndex: number) => {
    const newList = (reminderList || []).filter((_, index) => index !== removeIndex);
    setReminderList(newList);
  };

  return (
    <View className="reminder">
      <Cell.Group className="reminder-list">
        {reminderList.map((item, index) => (
          <Cell
            key={index}
            title={`提醒 ${index + 1}`}
            extra={
              <View className="reminder-item-extra">
                <Text>{item.label}</Text>
                <Close size={12} onClick={() => removeReminderItem(index)} />
              </View>
            }
          ></Cell>
        ))}

        <Cell clickable onClick={() => setVisiblePicker(true)}>
          <View className="reminder-btn">
            <Add size={16} color="#f00" />
            <Text>添加提醒</Text>
          </View>
        </Cell>

        <Picker
          key={visiblePicker + ''}
          defaultValue={[1]}
          options={options}
          visible={visiblePicker}
          onConfirm={confirmPicker}
          onClose={() => setVisiblePicker(false)}
        />
      </Cell.Group>
    </View>
  );
};

// 提醒方式开关
const ReminderTypeSelect = () => {
  const { reminderTypes, setReminderTypes } = useTodoStore();

  return (
    <Cell.Group className="reminder-type-select">
      <Cell
        align="center"
        title="微信消息提醒"
        extra={
          <Switch
            color="#f00"
            checked={reminderTypes.wechat}
            onChange={(val) =>
              setReminderTypes({
                ...reminderTypes,
                wechat: val.detail.value,
              })
            }
          />
        }
      />
      <Cell
        align="center"
        title="短信提醒"
        extra={
          <Switch
            color="#f00"
            checked={reminderTypes.sms}
            onChange={(val) =>
              setReminderTypes({
                ...reminderTypes,
                sms: val.detail.value,
              })
            }
          />
        }
      />
    </Cell.Group>
  );
};

export default NewTodo;
