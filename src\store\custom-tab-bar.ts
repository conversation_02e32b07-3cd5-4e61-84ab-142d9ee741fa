import {
  autoCreateSetters,
  autoUseShallow,
  create,
  createJSONStorage,
  customStorage,
  devtools,
  persist,
  Setter,
} from './index';
interface State {
  selected: number;
}
interface SetState {
  setSelected: Setter;
}
const useCustomTabbar = autoUseShallow<State, SetState>(
  create(
    persist(
      devtools(
        autoCreateSetters<State>({
          selected: 0,
        }),
      ),
      {
        name: 'custom-tab-bar', // 存储的 key
        storage: createJSONStorage(() => customStorage),
      },
    ),
  ),
);

export default useCustomTabbar;
