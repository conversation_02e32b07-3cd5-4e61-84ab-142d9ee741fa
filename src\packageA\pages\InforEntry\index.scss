.InforEntryWrapper {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background: linear-gradient(181deg, #f8dadd 0.48%, #f8ecef 18.77%, #f5f4f9 27.04%);
  .InforEntry {
    flex: 1;
    overflow-y: scroll;
    box-sizing: border-box;
    padding: 0 24px;
  }
  .InforEntry_header {
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 20px;
    .InforEntry_title {
      font-size: 44px;
      color: #131313;
      font-weight: 600;
    }
    .InforEntry_desc {
      font-size: 24px;
      color: #888;
      font-weight: 400;
    }
  }
  .form_header {
    color: #131313;
    font-size: 32px;
    font-weight: 600;
    padding: 24px 32px;
  }
  .customCell {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    flex-direction: row;
    background-color: #ffffff;
  }
  .replenish {
    padding-left: 32px;
    font-size: 28px;
    color: #666;
  }
}
.nut-cell-group {
  width: 100%;
}
.chooseRadio {
  width: 100%;
  .nut-radio,
  .nut-checkbox {
    height: 96px;
    font-size: 32px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    --nutui-radio-label-font-size: 32px;
    --nutui-radio-icon-font-size: 32px;
    --nutui-checkbox-label-font-size: 32px;
    --nutui-checkbox-icon-font-size: 32px;
    border-bottom: 1px solid #f5f6fa;
  }
}
.bankList {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;
  padding: 0 20px 0 32px;
  .bankItem {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 14px 0 14px 16px;
    color: #131313;
    border-radius: 8px;
    background: #f5f6fa;
    font-family: 'PingFang SC';
    font-size: 28px;
    margin-bottom: 16px;
    margin-right: 16px;
    .closeBut {
      padding: 0 20px;
    }
  }
}
.form_item_no_border {
  & ~ .nut-cell-divider {
    display: none;
  }
}
.rotate90 {
  transform: rotate(90deg);
}
.nut-cell-group-wrap,
.nut-form-footer {
  background-color: transparent;
}
.userBaseInfor,
.deptInfor {
  background-color: #fff;
  border-radius: 12px;
}
.userBaseInfor {
  margin-bottom: 20px;
}
.customInputBox {
  width: 100%;
  padding: 0 32px;
  box-sizing: border-box;
  min-height: 66vh;
  --nutui-input-background-color: #f5f6fa;
  .customInputBtn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
  }
}
.nut-input-readonly .nut-input-native {
  color: #131313;
}
.InforEntryNextPage {
  width: 100%;
  height: calc(100% - 75px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .InforEntryNextPageHeader {
    flex: 1;
    box-sizing: border-box;
    overflow-y: scroll;
    .debtsInfo {
      width: 100%;
      padding-bottom: 28px;
    }
    .debtsInfoItem {
      width: 100%;
      padding: 26px 32px;
      background-color: #fff;
      border-radius: 12px;
      margin-bottom: 28px;
      font-size: 28px;
      .identityLabel {
        color: #131313;
        font-size: 28px;
        margin-bottom: 26px;
        font-weight: 600;
      }
      .normaltext {
        margin-bottom: 26px;
        & > Text {
          color: #666;
          padding-right: 10px;
        }
      }
      .debtsTypeamount {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .normaltext {
          flex: 1;
        }
      }
      .detBut {
        text-align: right;
      }
    }
  }
  .InforEntryNextPageFooter {
    width: 100%;
    & > View:first-of-type {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      button {
        width: 40%;
        &:first-of-type {
          margin-right: 16px;
        }
      }
    }
  }
}
