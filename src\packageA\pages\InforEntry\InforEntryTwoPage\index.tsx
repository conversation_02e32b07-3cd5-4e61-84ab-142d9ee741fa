import ElevatorThreeLevel from '@/components/area';
import useDictStore from '@/store/useDictStore';
import { getKeyValue, getOptionsByDictType } from '@/utils/common';
import { ArrowRight } from '@nutui/icons-react-taro';
import { Button, Form, Input, TextArea } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import { getCurrentInstance } from '@tarojs/runtime/dist/current';
import Taro, { pxTransform, useLoad } from '@tarojs/taro';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import CustomPicker from '../customPicker';
import { RadioGroupTemplete } from '../pickerComponents';
import './index.scss';
const type = {
  TT: 44,
  WEAPP: 34,
};
const debtstypeInfo = {
  customer_info_type_card: {
    lable: '信用卡',
    value: 'cardName',
  },
  customer_info_type_lending: {
    lable: '网贷金融名称',
    value: 'lendingName',
  },
  customer_info_type_loan: {
    lable: '私人借贷人数',
    value: 'loanNumber',
  },
};
const Index = () => {
  const [data] = useDictStore((state) => [state.data]);
  const [isRet, setIsRet] = useState<boolean>(false);
  const [UserProfileform] = Form.useForm();
  const debtsTypeLabel = Form.useWatch('debtsTypeLabel', UserProfileform);
  const debtsType = Form.useWatch('debtsType', UserProfileform);
  const [pickerTitle, setPickerTitle] = useState<string>('请选择');
  const [pickerVisible, setPickerVisible] = useState<boolean>(false);
  const [pickerTemplete, setPickerTemplete] = useState<React.ReactNode>(null);
  const eventChannel: any = useRef();
  const submitFailed = (error: any) => {};
  const submitSucceed = async (values: any) => {
    console.log('submitSucceed', values);
    eventChannel.current?.emit('acceptDataFromOpenedPage', { data: values });
    Taro.navigateBack();
  };
  useLoad((option) => {
    console.log(option);
    const pages = Taro.getCurrentPages();
    if (pages.length === 1) {
      console.log('我是第一个页面 (栈底)');
      // 在这里做只在入口页执行的初始化逻辑
    } else {
      console.log('我不是入口页');
      setIsRet(true);
    }
  });
  useEffect(() => {
    const instance = getCurrentInstance();
    if (instance && instance.page) {
      eventChannel.current = (instance.page as any).getOpenerEventChannel?.();
    }
  }, []);
  const identitypickerOptions = useMemo(() => {
    return getOptionsByDictType('customer_info_identity', data);
  }, [data]);
  const amountpickerOptions = useMemo(() => {
    return getOptionsByDictType('customer_info_money', data);
  }, [data]);
  const debtsTypeOptions = useMemo(() => {
    return getOptionsByDictType('customer_dept_type', data);
  }, [data]);
  const getCascaderData = useCallback(
    (key: string) => {
      return getOptionsByDictType(key, data);
    },
    [data],
  );
  return (
    <View className="InforEntryWrapper">
      <View className="InforEntry">
        <Form
          form={UserProfileform}
          divider
          labelPosition="left"
          onFinish={(values) => submitSucceed(values)}
          onFinishFailed={(values, errors) => submitFailed(errors)}
          footer={
            <Button block nativeType="submit" type="primary">
              提交
            </Button>
          }
        >
          <View className="deptInfor">
            <View className="form_header">债权/债务基本信息</View>
            <Form.Item
              label="身份"
              name="identityLabel"
              shouldUpdate
              rules={[{ required: true, message: '请选择身份' }]}
              onClick={() => {
                setPickerTitle('请选择身份');
                setPickerVisible(true);
                setPickerTemplete(
                  <RadioGroupTemplete
                    data={identitypickerOptions}
                    defaultValue={UserProfileform.getFieldValue('moneyType')}
                    onChange={(value: any) => {
                      console.log(value);
                      console.log(getKeyValue(identitypickerOptions, value));
                      UserProfileform.setFieldsValue({
                        moneyType: getKeyValue(identitypickerOptions, value)[0],
                        identityLabel: getKeyValue(identitypickerOptions, value)[1],
                      });
                      setPickerVisible(false);
                    }}
                  />,
                );
              }}
            >
              {() => (
                <View className="customCell">
                  <Input
                    type={'text'}
                    align="right"
                    value={UserProfileform.getFieldValue('identityLabel')}
                    placeholder="请选择"
                    readOnly
                  />
                  <ArrowRight
                    color="var(--nutui-gray-5)"
                    size={14}
                    style={{
                      marginRight: pxTransform(10),
                    }}
                  />
                </View>
              )}
            </Form.Item>
            <Form.Item
              label="金额"
              name="amountLabel"
              shouldUpdate
              rules={[{ required: true, message: '请选择金额' }]}
              onClick={() => {
                setPickerTitle('请选择金额');
                setPickerVisible(true);
                setPickerTemplete(
                  <RadioGroupTemplete
                    data={amountpickerOptions}
                    onChange={(value: any) => {
                      console.log(value);
                      console.log(getKeyValue(amountpickerOptions, value));
                      UserProfileform.setFieldsValue({
                        money: getKeyValue(amountpickerOptions, value)[0],
                        amountLabel: getKeyValue(amountpickerOptions, value)[1],
                      });
                      setPickerVisible(false);
                    }}
                    defaultValue={UserProfileform.getFieldValue('money')}
                  />,
                );
              }}
            >
              {() => (
                <View className="customCell">
                  <Input
                    type={'text'}
                    align="right"
                    value={UserProfileform.getFieldValue('amountLabel')}
                    placeholder="请选择"
                    readOnly
                  />
                  <ArrowRight
                    color="var(--nutui-gray-5)"
                    size={14}
                    style={{
                      marginRight: pxTransform(10),
                    }}
                  />
                </View>
              )}
            </Form.Item>
            <Form.Item
              label="类型"
              name="debtsTypeLabel"
              shouldUpdate
              rules={[{ required: true, message: '请选择类型' }]}
              onClick={() => {
                setPickerTitle('请选择类型');
                setPickerVisible(true);
                setPickerTemplete(
                  <RadioGroupTemplete
                    data={debtsTypeOptions}
                    onChange={(value: any) => {
                      console.log(value);
                      console.log(getKeyValue(debtsTypeOptions, value));
                      UserProfileform.setFieldsValue({
                        debtsType: getKeyValue(debtsTypeOptions, value)[0],
                        debtsTypeLabel: getKeyValue(debtsTypeOptions, value)[1],
                        debtstypeInfoLabel: '',
                      });
                      setPickerVisible(false);
                    }}
                    defaultValue={UserProfileform.getFieldValue('debtsType')}
                  />,
                );
              }}
            >
              {() => (
                <View className="customCell">
                  <Input
                    type={'text'}
                    align="right"
                    value={UserProfileform.getFieldValue('debtsTypeLabel')}
                    placeholder="请选择"
                    readOnly
                  />
                  <ArrowRight
                    color="var(--nutui-gray-5)"
                    size={14}
                    style={{
                      marginRight: pxTransform(10),
                    }}
                  />
                </View>
              )}
            </Form.Item>
            {debtsType && debtsType !== '4' && (
              <>
                <Form.Item
                  label={debtstypeInfo[debtsType]['lable']}
                  name="debtstypeInfoLabel"
                  shouldUpdate
                  rules={[
                    { required: true, message: '请选择' + debtstypeInfo[debtsType]['lable'] },
                  ]}
                  onClick={() => {
                    setPickerTitle('请选择' + debtstypeInfo[debtsType]['lable']);
                    setPickerVisible(true);
                    setPickerTemplete(
                      <RadioGroupTemplete
                        data={getCascaderData(debtsType)}
                        onChange={(value: any) => {
                          console.log(value);
                          console.log(getKeyValue(getCascaderData(debtsType), value)[0]);
                          UserProfileform.setFieldsValue({
                            [debtstypeInfo[debtsType]['value']]: getKeyValue(
                              getCascaderData(debtsType),
                              value,
                            )[0],
                            debtstypeInfoLabel: getKeyValue(getCascaderData(debtsType), value)[1],
                          });
                          setPickerVisible(false);
                        }}
                        defaultValue={UserProfileform.getFieldValue(
                          debtstypeInfo[debtsType]['value'],
                        )}
                      />,
                    );
                  }}
                >
                  {() => (
                    <View className="customCell">
                      <Input
                        type={'text'}
                        align="right"
                        value={UserProfileform.getFieldValue('debtstypeInfoLabel')}
                        placeholder="请选择"
                        readOnly
                      />
                      <ArrowRight
                        color="var(--nutui-gray-5)"
                        size={14}
                        style={{
                          marginRight: pxTransform(10),
                        }}
                      />
                    </View>
                  )}
                </Form.Item>
              </>
            )}
            {debtsType && debtsType === 'customer_info_type_card' && (
              <>
                <Form.Item
                  className="form_item_no_border"
                  label="开卡归属城市"
                  name="cardAddressCode"
                  rules={[
                    {
                      required: debtsType && debtsType === 'customer_info_type_card',
                      message: '请选择地址',
                    },
                  ]}
                  shouldUpdate
                  onClick={() => {
                    setPickerTitle('请选择地址');
                    setPickerVisible(true);
                    setPickerTemplete(
                      <ElevatorThreeLevel
                        onchange={(data) => {
                          UserProfileform.setFieldsValue({
                            cardAddressCode: data,
                          });
                        }}
                        initvalue={
                          UserProfileform.getFieldValue('cardAddressCode') || [null, null, null]
                        }
                      />,
                    );
                  }}
                >
                  {() => {
                    let data = UserProfileform.getFieldValue('cardAddressCode') || undefined;
                    if (data) {
                      data = [data[0]?.name ?? '', data[1]?.name ?? '', data[2]?.name ?? ''].join(
                        '',
                      );
                    }
                    return (
                      <View className="customCell">
                        <Input
                          type={'text'}
                          align="right"
                          value={data}
                          placeholder="请选择"
                          readOnly
                        />
                        <ArrowRight
                          color="var(--nutui-gray-5)"
                          size={14}
                          style={{
                            marginRight: pxTransform(10),
                          }}
                        />
                      </View>
                    );
                  }}
                </Form.Item>
              </>
            )}
            {debtsType === '4' && (
              <Form.Item label="其他" name="other">
                <Input placeholder="请输入" clearable align="right" type="text" />
              </Form.Item>
            )}
            <View className="replenish">补充说明</View>
            <Form.Item className="form_item_no_border" name="remark">
              <TextArea placeholder="将当下面临的情况描述清楚；如信用卡已逾期" />
            </Form.Item>
          </View>
        </Form>
      </View>
      <CustomPicker
        title={pickerTitle}
        visible={pickerVisible}
        position="bottom"
        onClose={() => {
          setPickerVisible(false);
        }}
        customNode={pickerTemplete}
      />
    </View>
  );
};

export default Index;
