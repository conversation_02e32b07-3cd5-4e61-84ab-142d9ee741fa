import { serveIp } from '../config';
import http from '../http';
import { GetCardBenefitsRes, GetMemberInfoRes, GetMmenumenuPermissionRes } from './interface';

// 导出会员卡权益API
export * from './card-benefits';
// 导出会员卡升级API
export * from './card-upgrade';

/**
 * 会员信息
 * @returns
 */
export function getMemberInfo(): Promise<GetMemberInfoRes> {
  return http.get(`${serveIp.ip}/app-api/member/service/memberInfo`);
}

/**
 * 获取会员卡权益列表
 * @param {string} cardId
 * @returns
 */
export function getCardBenefits(cardId: number): Promise<GetCardBenefitsRes> {
  return http.get(`${serveIp.ip}/app-api/member/service/${cardId}/benefits`);
}

/**
 * 获取会员菜单
 * @returns
 */
export function getMmenumenuPermission(): Promise<GetMmenumenuPermissionRes> {
  return http.get(`${serveIp.ip}/app-api/member/service/menuPermission`);
}
