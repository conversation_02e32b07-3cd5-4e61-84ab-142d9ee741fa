/**
 * 日期格式化工具类
 */

/**
 * 日期格式化选项
 */
export interface DateFormatOptions {
  /** 是否显示时间 */
  showTime?: boolean;
  /** 日期分隔符 */
  dateSeparator?: string;
  /** 时间分隔符 */
  timeSeparator?: string;
  /** 格式化模板，支持以下占位符：
   * YYYY: 年份
   * MM: 月份
   * DD: 日期
   * HH: 小时
   * mm: 分钟
   * ss: 秒
   * w: 星期
   */
  template?: string;
}

/**
 * 格式化日期为字符串
 * @param date 日期对象或时间戳
 * @param options 格式化选项
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date | number, options: DateFormatOptions = {}): string {
  const {
    showTime = false,
    dateSeparator = '-',
    timeSeparator = ':',
    template
  } = options;

  const d = date instanceof Date ? date : new Date(date);

  // 如果提供了模板，使用模板格式化
  if (template) {
    return template
      .replace(/YYYY/g, String(d.getFullYear()))
      .replace(/MM/g, String(d.getMonth() + 1).padStart(2, '0'))
      .replace(/DD/g, String(d.getDate()).padStart(2, '0'))
      .replace(/HH/g, String(d.getHours()).padStart(2, '0'))
      .replace(/mm/g, String(d.getMinutes()).padStart(2, '0'))
      .replace(/ss/g, String(d.getSeconds()).padStart(2, '0'))
      .replace(/w/g, getWeekDay(d));
  }

  // 原有的格式化逻辑
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const dateStr = [year, month, day].join(dateSeparator);

  if (!showTime) return dateStr;

  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  const timeStr = [hours, minutes, seconds].join(timeSeparator);

  return `${dateStr} ${timeStr}`;
}

/**
 * 获取相对时间描述
 * @param date 日期对象或时间戳
 * @returns 相对时间描述字符串
 */
export function getRelativeTime(date: Date | number): string {
  const d = date instanceof Date ? date : new Date(date);
  const now = new Date();
  const diff = now.getTime() - d.getTime();

  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const month = 30 * day;
  const year = 365 * day;

  if (diff < minute) {
    return '刚刚';
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`;
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`;
  } else if (diff < month) {
    return `${Math.floor(diff / day)}天前`;
  } else if (diff < year) {
    return `${Math.floor(diff / month)}个月前`;
  } else {
    return `${Math.floor(diff / year)}年前`;
  }
}

/**
 * 获取日期是星期几
 * @param date 日期对象或时间戳
 * @returns 星期几的字符串
 */
export function getWeekDay(date: Date | number): string {
  const d = date instanceof Date ? date : new Date(date);
  const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
  return `星期${weekDays[d.getDay()]}`;
}

/**
 * 判断是否为同一天
 * @param date1 第一个日期
 * @param date2 第二个日期
 * @returns 是否为同一天
 */
export function isSameDay(date1: Date | number, date2: Date | number): boolean {
  const d1 = date1 instanceof Date ? date1 : new Date(date1);
  const d2 = date2 instanceof Date ? date2 : new Date(date2);

  return d1.getFullYear() === d2.getFullYear() &&
    d1.getMonth() === d2.getMonth() &&
    d1.getDate() === d2.getDate();
}

/**
 * 将天数转换为年月日显示格式
 * @param days 天数
 * @returns 格式化后的年月日字符串
 */
export function formatDaysToYearMonthDay(days: number): string {
  if (days <= 0) return '0天';

  const year = 365;
  const month = 30;

  if (days >= year) {
    const years = Math.floor(days / year);
    const remainDays = days % year;
    const months = Math.floor(remainDays / month);
    const remainingDays = remainDays % month;

    let result = `${years}年`;
    if (months > 0) result += `${months}个月`;
    if (remainingDays > 0) result += `${remainingDays}天`;
    return result;
  } else if (days >= month) {
    const months = Math.floor(days / month);
    const remainingDays = days % month;

    let result = `${months}个月`;
    if (remainingDays > 0) result += `${remainingDays}天`;
    return result;
  } else {
    return `${days}天`;
  }
}

