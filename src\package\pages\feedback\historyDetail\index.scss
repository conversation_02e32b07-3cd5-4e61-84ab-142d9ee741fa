.history-detail {
  height: 100%;
  padding: 28px 24px;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  gap: 28px;
}

.history-detail-card {
  padding: 24px 32px;
  background-color: #fff;
  border-radius: 8px * 2;

  .title {
    color: #666;
    font-size: 16px * 2;
    font-weight: 400;
    margin-bottom: 16px * 2;
  }

  .content {
    color: #131313;
    font-size: 14px * 2;
    font-weight: 400;
  }
}

.history-detail-card-images {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}
