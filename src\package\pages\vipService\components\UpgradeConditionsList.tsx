import { Text, View } from '@tarojs/components';
import { memo } from 'react';
import './UpgradeConditionsList.scss';

interface Condition {
  desc: string;
  isOk: boolean;
}

export interface UpgradeWay {
  title: string;
  list: Condition[];
  count: number;
  total: number;
}

/**
 * 升级条件列表组件
 * 显示用户升级会员所需满足的条件
 *
 * @param {object} props - 组件属性
 * @param {UpgradeWay[]} [props.data] - 升级方式列表
 * @returns {JSX.Element} 升级条件列表组件
 */
const UpgradeConditionsList = memo(({ data }: { data: UpgradeWay[] }) => {
  return (
    <View className="upgrade-conditions">
      {data.map((way, index) => (
        <View key={index} className="condition-group">
          <View className="condition-group-title">
            <Text>
              #{way.title}({way.count}/{way.total})
            </Text>
          </View>
          <View className="condition-group-body">
            {way.list.map((condition, cIndex) => (
              <View key={cIndex} className="condition-item">
                <View className={`condition-icon ${condition.isOk ? 'ok' : 'no'}`}>
                  {condition.isOk ? <Text>✓</Text> : <Text>!</Text>}
                </View>
                <Text className="condition-desc">{condition.desc}</Text>
              </View>
            ))}
          </View>
        </View>
      ))}
    </View>
  );
});

export default UpgradeConditionsList;
