import { View } from '@tarojs/components';
import { CSSProperties, FC, memo, useMemo, useState } from 'react';
import { CardListItem } from '../../../myTeam';
import RevenueList from '../RevenueList/index';
import './index.scss';

const MyCommissionsList: FC = () => {
  const [activeTab, setActiveTab] = useState<0 | 1 | 2>(0);

  const activeTabList = useMemo(() => {
    if (activeTab === 0) {
      return <RevenueList />;
    }
    if (activeTab === 1) {
      return (
        <CardListItem
          data={{
            nickname: '提现',
            createTime: '2025/03/21 10:09:09',
            debtTypeName: '提现',
            memberUserId: 0,
            extra: -100,
          }}
          style={
            {
              '--avatar-size': '32px',
            } as CSSProperties
          }
        />
      );
    }
    if (activeTab === 2) {
      return <View>待结算</View>;
    }
  }, [activeTab]);

  return (
    <View className="my-list">
      <View className="my-list-title">金额明细</View>
      <View className="my-list-tabs">
        <View
          className={`my-list-tabs-item ${activeTab === 0 ? 'active' : ''}`}
          onClick={() => setActiveTab(0)}
        >
          收入
        </View>
        <View
          className={`my-list-tabs-item ${activeTab === 1 ? 'active' : ''}`}
          onClick={() => setActiveTab(1)}
        >
          支出
        </View>
        <View
          className={`my-list-tabs-item ${activeTab === 2 ? 'active' : ''}`}
          onClick={() => setActiveTab(2)}
        >
          待结算
        </View>
      </View>
      <View className="my-list-content">{activeTabList}</View>
    </View>
  );
};

export default memo(MyCommissionsList);
