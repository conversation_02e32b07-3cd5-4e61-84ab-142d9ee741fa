import useDictStore from '@/store/useDictStore';
import useUserStore from '@/store/useUserStore';
import { useEffect } from 'react';
import { getdictdata } from '..';
import { getdictdataResponse } from '../interface';

const useDict = () => {
  const [data, setData] = useDictStore((state) => [state.data, state.setData]);
  const [accessToken] = useUserStore((state) => [state.accessToken]);
  useEffect(() => {
    if (data.length === 0 && accessToken) {
      console.log('字典数据不存在，开始获取字典数据');
      getdictdataFn();
    } else {
      console.log(data);
      console.log('字典数据已经存在了');
    }
  }, [accessToken]);

  const getdictdataFn = async () => {
    const res = await getdictdata();
    console.log('res', res);
    if (res.code === 0) {
      setData(res.data);
      console.log(res.data);
    } else {
      console.error('获取字典数据失败');
    }
  };
  const refetch = async () => {
    getdictdataFn();
  };

  return {
    dictData: data as getdictdataResponse,
    refetch,
  };
};
export default useDict;
