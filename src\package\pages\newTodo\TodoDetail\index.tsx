import { appPlanById } from '@/serves/planByMonth';
import { Cell, Dialog } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import { useRouter } from '@tarojs/taro';
import { FC } from 'react';
import { useRequest } from 'taro-hooks';
import './index.scss';

interface TodoDetailProps {}

const TodoDetail: FC<TodoDetailProps> = () => {
  const { params } = useRouter();

  const { data } = useRequest(() => appPlanById(params.id as string), {
    refreshDeps: [params.id],
    cacheKey: 'appPlanById',
  });

  // TODO: 删除日程
  const handleDelete = () => {
    console.log('删除');
    Dialog.open('delete-dialog', {
      title: '确定删除日程？',
      onConfirm: () => {
        Dialog.close('delete-dialog');
      },
      onCancel: () => {
        Dialog.close('delete-dialog');
      },
    });
  };

  return (
    <View className="container">
      <View className="base-info">
        <Cell.Group>
          <Cell title="标题" align="center" extra={data?.data?.workName} />
          {/* <Cell title="地点" align="center" extra={data?.data?.workAddress} /> */}
          <Cell title="备注" align="center" extra={data?.data?.remark} />
          <Cell title="开始时间" align="center" extra={data?.data?.startTime} />
          <Cell title="结束时间" align="center" extra={data?.data?.endTime} />
        </Cell.Group>
      </View>
      <View className="reminder">
        <Cell.Group>
          {/* <Cell title="提醒" align="center" extra="提醒" />
          <Cell title="提醒" align="center" extra="提醒" />
          <Cell title="提醒" align="center" extra="提醒" /> */}
        </Cell.Group>
      </View>
      {/* <View className="tabs">
        <View className="tabs-item">
          <Share size={20} />
          <Text>分享</Text>
        </View>
        <View className="tabs-item">
          <Edit size={20} />
          <Text>编辑</Text>
        </View>
        <View className="tabs-item" onClick={handleDelete}>
          <Del size={20} />
          <Text>删除</Text>
        </View>
      </View> */}

      <Dialog id="delete-dialog" />
    </View>
  );
};

export default TodoDetail;
