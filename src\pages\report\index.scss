/**index.scss**/
@import 'taro-ui/dist/style/components/progress.scss';
@import 'taro-ui/dist/style/components/icon.scss';
@import 'taro-ui/dist/style/components/radio.scss';
@import 'taro-ui/dist/style/components/icon.scss';
page {
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
}
.wrapper {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.tabscroll {
  width: 100%;
  height: 80rpx;
  font-size: 28rpx;
  position: relative;
  box-sizing: border-box;
  white-space: nowrap;
  overflow-x: scroll;

  .tabView {
    flex: none;
    height: 80rpx;
    display: inline-flex;
    width: 25%;
    justify-content: center;
    align-items: center;
  }

  .activedTab {
    position: absolute;
    flex: none;
    width: 10vw;
    height: 8rpx;
    background: linear-gradient(271deg, #9048c9 2%, #3a3dc3 102%);
    top: 50rpx;
    left: 0;
    transition: all 300ms ease-in-out;
  }
}
view,
text {
  box-sizing: border-box;
}

.scrollarea {
  flex: 1;
  overflow-y: scroll;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(271deg, #9048c9 2%, #3a3dc3 102%);
  padding: 20rpx;
  box-sizing: border-box;

  .debtInfo {
    width: 100%;
  }

  .debtInfoTitle {
    text-align: center;
    color: #333;
    font-size: 28rpx;
    padding: 38px 0;

    text {
      color: #666;
      font-size: 24rpx;
    }
  }

  .debtInfoContent {
    padding: 0 24rpx;
    font-size: 28rpx;

    & > view {
      display: flex;
      align-items: center;
      height: 75rpx;
      background-color: #fff;
      justify-content: space-between;
      border-radius: 20rpx;
      padding: 0 17rpx;

      &:last-of-type {
        margin-top: 20rpx;

        & > view {
          color: #ee5253;
        }
      }

      & > text {
        font-size: 26rpx;
        color: #666;
      }

      & > view {
        font-weight: bold;
        font-size: 28rpx;
      }
    }

    .price {
      position: relative;

      text {
        position: absolute;
        bottom: -40rpx;
        left: 0;
        color: #999;
      }
    }
  }
  .chooseMolde {
    width: calc(100% - 40px);
    margin: 20px auto;
    border-radius: 20px;
    background-color: #fff;
    padding: 20px;
    overflow: hidden;
  }
  .debtInfoResult {
    font-size: 28rpx;
    text-align: center;
    padding: 0 24rpx;

    text {
      color: #f16c35;
      font-weight: bold;
    }
  }

  .connectBox {
    width: calc(100% - 48rpx);
    margin: 0 auto;
    text-align: center;
    border-radius: 20px;
    opacity: 1;
    background: #f6f7f8;
    margin-top: 43rpx;
    padding: 22rpx 0;
    border: 4rpx solid #fff;
  }

  .connectUs {
    margin-bottom: 20rpx;

    & > view {
      &:first-of-type {
        color: #222222;
        font-size: 28rpx;
        margin-bottom: 10rpx;

        image {
          width: 28rpx;
          height: 28rpx;
          margin-right: 10rpx;
        }
      }
    }

    .Qrcode {
      display: flex;
      align-items: center;
      justify-content: space-around;

      & > view {
        width: 25vw;
        height: 25vw;
        padding: 10rpx;
        background-color: #fff;
        border-radius: 20px;

        image {
          width: 100%;
          height: 100%;
        }
      }
    }

    .QrcodeInfo {
      display: flex;
      align-items: center;
      margin-bottom: 10rpx;
      justify-content: space-around;
      font-size: 24rpx;
      margin-top: 33rpx;
      color: #707070;
    }
  }

  .connectPhone {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;
    justify-content: center;

    text {
      color: #ee5253;
      font-size: 28rpx;
      margin-left: 20rpx;
      font-weight: bold;
      text-align: center;
    }
  }

  .TestResult {
    width: 100%;
    height: 420rpx;
    margin: 0 auto;
    border-radius: 20rpx;
    position: relative;
    z-index: 1;

    .TestResultBg {
      position: absolute;
      width: 100%;
      height: 420rpx;
      left: 0;
      top: 0;

      image {
        position: absolute;
        width: 186.56rpx;
        height: 123.56rpx;
        z-index: 2;
        right: 0;
        bottom: 35rpx;
      }
    }

    .TestContent {
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      font-size: 28rpx;
      background-color: #fff;
      border-radius: 20rpx;

      & > view:first-of-type {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12rpx;

        & > view {
          width: 200rpx;
          text-align: center;
          height: 48rpx;
          line-height: 48rpx;
          font-size: 26rpx;
          border-radius: 24rpx;
          opacity: 1;
          background: #ffffff;
          box-sizing: border-box;
          color: #ee5253;
          border: 1px solid #ee5253;
        }
      }

      & > view:nth-of-type(2) {
        width: 100%;
        display: flex;

        & > view:first-of-type {
          width: 184rpx;
          height: 184rpx;
          border-radius: 50%;
          margin-left: 15rpx;
          margin-right: 23rpx;

          image {
            width: 184rpx;
            height: 184rpx;
          }
        }

        text {
          font-size: 60rpx;
          font-weight: bold;
        }

        view {
          margin-top: 20rpx;
          font-size: 44rpx;
          font-weight: bold;
        }
      }

      & > view:last-of-type {
        align-self: start;
        padding-left: 24rpx;
        padding-bottom: 13rpx;
      }
    }
  }

  .outBoxViewContainer {
    width: 100%;
    background-color: rgba($color: #fff, $alpha: 0.9);
    border-radius: 20rpx;
    padding-bottom: 23rpx;
    margin-bottom: 20rpx;
    box-sizing: border-box;
  }

  .footerTitle {
    text-align: center;
    height: 60rpx;
    line-height: 60rpx;
    color: #333;
    font-size: 30rpx;
    border-radius: 10px;
    margin: 20px 0;
    font-weight: bold;
    color: #222;
  }

  .dataDetail {
    width: 100%;

    .dataAnalysis {
      width: 100%;
      font-size: 24rpx;
      padding: 20rpx;

      & > view {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 30rpx;

        & > view {
          flex-basis: 49.5%;
          display: flex;
          align-items: center;

          &:nth-of-type(2) > text {
            text-align: right;
          }

          .progressBox {
            flex: 1;
            position: relative;
            .at-progress__outer-inner {
              background-color: #e6e5e5;
            }
            text {
              position: absolute;
              top: -35rpx;
              left: 50%;
              transform: translateX(-50%);
            }
          }

          &:nth-of-type(1) .at-progress {
            transform: rotateY(180deg);
          }

          progress {
            flex: 1;
          }
        }
      }
    }
  }

  .PortraitInterpretationBox {
    padding: 0 20rpx;

    .PortraitInterpretation {
      & > view:first-of-type {
        font-size: 28rpx;
        color: #222;
        margin-bottom: 15rpx;
        font-weight: bold;
      }

      & > view:nth-of-type(2) {
        background-color: #fff;
        padding: 15rpx;
        border-radius: 20rpx;
      }

      .pView {
        font-size: 28rpx;
        text-indent: 56rpx;
      }
    }
  }

  .goalBox {
    padding: 0 20rpx;

    .goal {
      font-size: 28rpx;

      .pView {
        padding-left: 0 !important;
        text-indent: 24rpx;
        color: #222;
        font-size: 28rpx;
      }

      & > view {
        & > text {
          display: block;
          text-align: center;
          font-weight: bold;
        }

        & > view {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 80rpx;
          border-radius: 20rpx;
          color: #222222;
          background-color: rgba(256, 256, 256, 0.7);
          margin-bottom: 30rpx;
          margin-top: 20rpx;
          font-weight: bold;
        }
      }
    }
  }

  .strategyBox {
    padding: 20rpx;

    .strategyTitle {
      font-size: 26rpx;
      color: #666;
      margin-bottom: 15rpx;
      text-align: center;
    }

    .strategyList {
      background-color: #fff;
      padding: 20rpx;
      border-radius: 20rpx;
    }

    .strategy {
      display: flex;
      align-items: center;
      border: none;
      border-radius: 10px;
      margin-bottom: 20rpx;
      font-size: 28rpx;
      color: #222;

      & > text:first-of-type {
        width: 12rpx;
        height: 12rpx;
        background-color: #ee5253;
        border-radius: 50%;
        margin-right: 30rpx;
      }
    }
  }
}
