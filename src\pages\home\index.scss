.wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  .searchbox {
    width: calc(100% - 48px);
    height: 64px;
    margin: 28px auto;
    display: flex;
    align-items: center;
    background-color: #fff;
    position: relative;
    z-index: 1;
    border-radius: 40px;
    padding-left: 24px;
    color: #9d9d9d;
    font-size: 24px;
  }
  .main {
    width: calc(100% - 48px);
    margin: 0 auto;
    .contentPrice {
      height: 138px;
      border-radius: 16px;
      background: linear-gradient(170deg, #fdf7f2 5.84%, #fff 44.05%, #fff 44.08%);
      margin: 28px auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 24px;
      padding: 0 24px;
      .contentPrice_title {
        display: flex;
        align-items: center;
        flex-basis: calc(100% - 162px);
        justify-content: space-around;
        .contentPrice_item {
          color: #666;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          .contentPrice_item_title {
            margin-top: 16px;
          }
        }
      }
      .nut-button {
        flex-basis: 152px;
        height: 56px;
        border-radius: 28px;
        font-size: 26px;
      }
    }
    .task {
      width: 100%;
      border-radius: 16px;
      background: linear-gradient(180deg, #fef3ee 0%, #fff 17.04%, #fff 100%);
      margin: 28px auto;
      box-sizing: border-box;
      padding: 24px 16px 26px 32px;
      .task_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .task_header_title {
          width: 150px;
          height: 52px;
          Image {
            width: 100%;
            height: 100%;
          }
        }
        .task_header_more {
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: #666;
          font-size: 24px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }
      .task_content {
        width: 100%;
      }
      .task_content_box {
        width: 100%;
        display: flex;
        align-items: center;
        margin-top: 28px;
        .task_content_item {
          flex-basis: calc(100% - 162px);
          .task_content_item_title {
            color: #131313;
            font-size: 28px;
            font-weight: 500;
          }
          .task_content_item_desc {
            color: #666;
            font-size: 24px;
            font-weight: 400;
          }
        }
        .nut-button {
          flex-basis: 152px;
          height: 56px;
          border-radius: 28px;
          font-size: 26px;
        }
      }
    }
    .activity {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .activityItem {
        flex-basis: calc(50% - 13px);
        padding: 24px 26px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-image: linear-gradient(136deg, #fcf6e4 3.79%, #fbe7d5 92.03%);
        stroke-width: 1px;
        stroke: rgba(255, 255, 255, 0);
        filter: drop-shadow(2px 2px 8px rgba(255, 186, 174, 0.2));
        box-sizing: border-box;
        &:first-of-type {
          background-image: linear-gradient(139deg, #fff0e7 7.55%, #f6e7ec 92.83%);
        }
        .activityItem_content {
          flex-basis: calc(100% - 90px);
          .activityItem_title {
            color: #131313;
            font-size: 28px;
            font-weight: 400;
          }
          .activityItem_desc {
            color: #9d9d9d;
            font-size: 22px;
            margin: 8px 0;
          }
          .nut-button {
            width: 152px;
            height: 56px;
            border-radius: 28px;
            border: none;
            font-size: 26px;
            background-color: #fff;
          }
        }
        .activityItem_img {
          flex-basis: 80px;
          height: 80px;
        }
      }
    }
    .recommend {
      width: 100%;
      .recommend_title {
        color: #131313;
        font-size: 32px;
        padding: 20px 0;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .linkBut {
          font-weight: normal;
          display: flex;
          font-size: 28px;
          align-items: center;
          justify-content: center;
          color: #9d9d9d;
        }
      }
    }
  }
}
