/**
 * @description 管理后台 - 合同录入
 */
export interface ContractEnterVO {
  /**
   * @description 主键ID
   */
  id?: number;
  /**
   * @description 委案id
   */
  caseEntrustId?: number;
  /**
   * @description 模板id
   */
  templateId?: number;
  /**
   * @description 提交状态   0暂存 1提交
   */
  commitStatus?: number;
  /**
   * @description 合同类型
   */
  contractType?: string;
  /**
   * @description 模板名称
   */
  templateName?: string;
  /**
   * @description 合同编号（唯一）
   */
  contractNumber?: string;
  /**
   * @description 合同名称
   */
  contractName?: string;
  /**
   * @description 化债总金额（元）
   */
  totalDebtReductionAmount?: number;
  /**
   * @description 实际减免金额（元）
   */
  actualReductionAmount?: number;
  /**
   * @description 实际化债总额（元）
   */
  actualDebtReductionAmount?: number;
  /**
   * @description 每月应化债金额（元）
   */
  monthlyDebtReductionAmount?: number;
  /**
   * @description 化债期数（月，必须>0）
   */
  debtReductionPeriods?: number;
  /**
   * @description 合同开始日期
   */
  startDate?: string;
  /**
   * @description 甲方名称
   */
  partyAName?: string;
  /**
   * @description 甲方统一社会信用代码
   */
  partyAUnifiedSocialCreditCode?: string;
  /**
   * @description 甲方法定代表人
   */
  partyALegalRepresentative?: string;
  /**
   * @description 法定代表人联系方式
   */
  partyAContactNumber?: string;
  /**
   * @description 甲方地址
   */
  partyAAddress?: string;
  /**
   * @description 乙方名称
   */
  partyBName?: string;
  /**
   * @description 乙方统一社会信用代码
   */
  partyBUnifiedSocialCreditCode?: string;
  /**
   * @description 乙方法定代表人
   */
  partyBLegalRepresentative?: string;
  /**
   * @description 法定代表人联系方式
   */
  partyBContactNumber?: string;
  /**
   * @description 乙方地址
   */
  partyBAddress?: string;
  /**
   * @description 丙方名称
   */
  partyCName?: string;
  /**
   * @description 丙方证件号码/统一社会信用代码
   */
  partyCIdNumberOrUnifiedSocialCreditCode?: string;
  /**
   * @description 丙方联系方式
   */
  partyCContactNumber?: string;
  /**
   * @description 合同状态
   * 0, 草稿
   * 1, 审批中
   * 2, 审批通过
   * 3, 审批驳回
   * 4, 作废
   * 5, 履约中
   * 6, 结束
   * 7, 变更
   */
  status?: number;
  /**
   * @description 丙方地址
   */
  partyCAddress?: string;
  /**
   * @description 创建时间
   */
  createTime?: string;
  /**
   * @description 更新时间
   */
  updateTime?: string;
  /**
   * @description 创建人
   */
  creator?: string;
  /**
   * @description 更新人
   */
  updater?: string;
  /**
   * @description 删除标志（0：未删除，1：已删除）
   */
  deleted?: number;
}

/**
 * @description 录入合同返回
 */
export type SaveContractRes = number;
