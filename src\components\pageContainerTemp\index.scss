.tabbarContainer.no-tabbar {
  & > view:first-of-type {
    width: 100%;
    // flex-basis: calc(100vh - env(safe-area-inset-bottom));
    overflow-y: hidden;
    overflow-x: hidden;
    z-index: 2;
  }
}

// 当不显示tabbar时
.wrapper {
  height: 100vh !important;
  flex-basis: 100% !important;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  position: relative;
  z-index: 2;
  overflow: auto;
  flex: 1;
}

.marks {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0 !important;

  &.hidden {
    display: none;
  }

  &.deep-pink {
    .markright,
    .markleft {
      width: 539px;
      height: 556px;
      filter: blur(46.210792541503906px);
      background: #f9d8d9;
    }
  }

  &.none {
    background-color: #f5f4f9;
    .markright,
    .markleft {
      display: none;
    }
  }

  &.glod {
    .markright,
    .markleft {
      width: 539px;
      height: 556px;
      filter: blur(46.210792541503906px);
    }

    .markleft {
      background: #faebe4;
    }
    .markright {
      background: #ffe0cb;
    }
  }
}
