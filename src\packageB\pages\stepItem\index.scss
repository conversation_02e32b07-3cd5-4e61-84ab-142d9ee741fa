.step-item {
  display: grid;
  grid-template-columns: 130px 20px 1fr;
  gap: 40px;

  // 最后一个元素不显示线
  &:last-child {
    .step-period-line-container {
      .step-period-line {
        display: none;
      }
    }
  }

  .step-period-line-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    // justify-content: center;
    width: 14px * 2;
    // justify-content: center;

    .step-period-dot {
      width: 14px;
      height: 14px;
      background-color: #ff4144;
      border-radius: 50%;
      align-items: center;
    }

    .step-period-dot.undone {
      background-color: #c7c2c2;
    }

    .step-period-line {
      width: 2px;
      flex: 1;
      background-color: #c7c2c2;
    }
  }
}
