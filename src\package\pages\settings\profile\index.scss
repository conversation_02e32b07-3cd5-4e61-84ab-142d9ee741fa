.profile {
  padding: 28px 24px;
  background-color: #f5f4f9;
  height: 100%;
  width: 100%;

  --nutui-cell-title-color: #666 !important;
  --nutui-cell-title-font-size: 16px * 2 !important;
  --nutui-cell-extra-color: #131313 !important;
  --nutui-cell-extra-font-size: 16px * 2 !important;
  --nutui-popup-border-radius: 32px 32px 0 0 !important;

  .nut-cell-left {
    flex: none;
  }

  .extra-content {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    justify-content: flex-end;

    .value-text {
      // color: #999;
      // font-size: 12px * 2;
      margin-right: 8px;
      color: #999;
    }
  }

  // 昵称编辑弹窗样式
  .nickname-popup {
    padding: 32px;

    :global(.nut-popup-title) {
      font-size: 36px;
      font-weight: 600;
      color: #333;
      padding: 32px 0;
      text-align: center;
    }

    :global(.nut-popup-close-icon) {
      top: 32px;
      right: 32px;
    }

    .nickname-input-container {
      padding: 20px;

      .nickname-input {
        margin-bottom: 16px;
        border-radius: 8px;
        border: 1px solid #eee;
        padding: 10px;
        --nutui-input-padding: 24px !important;
        --nutui-input-font-size: 32px !important;
        --nutui-input-border-bottom-width: 0 !important;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
        background-color: #f9f9f9;
      }

      .nickname-counter {
        text-align: right;
        color: #999;
        font-size: 12px;
        margin-top: 8px;
      }

      .nickname-button-container {
        display: flex;
        justify-content: space-between;
        gap: 24px;
        margin-top: 20px;

        .nut-button {
          flex: 1;
          height: 88px;
          border-radius: 44px;
          font-size: 32px;
          font-weight: 500;
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .cancel-button {
          background-color: #f5f5f5;
          color: #666;
          border: none;
          flex: 1;
          margin-right: 10px;

          &:active {
            background-color: #e8e8e8;
          }
        }

        .save-button {
          background: linear-gradient(90deg, #ff4d4f, #ff7875) !important;
          border: none;
          box-shadow: 0 4px 16px rgba(255, 77, 79, 0.2);
          flex: 1;

          &:active {
            opacity: 0.9;
          }
        }
      }
    }
  }
}

.avatar-button {
  background: transparent;
  padding: 0;
  margin: 0;
  border-radius: 0;
  border: none;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;

  &::after {
    border: none;
  }
}
