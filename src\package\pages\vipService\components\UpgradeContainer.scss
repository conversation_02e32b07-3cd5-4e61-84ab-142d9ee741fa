/* 升级弹窗开始 */
.upgrade-popup-content {
  display: flex;
  flex-direction: column;
  padding: 48px 40px;
  position: relative;
  overflow-y: hidden;
  border-radius: 48px 48px 0px 0px;
  background-color: #f5f4f9;
  height: 568px * 2;
  z-index: 0;

  /* 使用 ::before 和 ::after 伪元素来创建两个形状 */
  &::before,
  &::after {
    content: '';
    position: absolute;
    filter: blur(27.182817459106445px);
    z-index: -1;
  }

  /* 左侧的黄色形状 */
  &::before {
    width: 177px * 2;
    height: 177px * 2;
    border-radius: 177px * 2;
    background: #fef8e8;

    /* 定位，使其右下角 1/4 在盒子内 */
    top: -177px;
    left: calc(-177px * 0.5);
  }

  /* 右侧的粉色圆形 */
  &::after {
    width: 262px * 2;
    height: 262px * 2;
    border-radius: 262px * 2;
    background: #fae5e4;

    /* 定位，使其左下角 1/4 在盒子内 */
    top: -262px;
    right: calc(-262px * 0.5);
  }

  .upgrade-popup-content-title {
    display: flex;
    flex-direction: column;
    z-index: 2;
    gap: 10px;
    .text {
      color: #000;
      font-size: 18px * 2;
      font-weight: 600;
    }
    .desc {
      color: #666;
      font-size: 14px * 2;
    }
  }
}

.nut-popup-title-right-top-right {
  z-index: 20;
}

.upgrade-popup-content-list {
  flex: 1;
  overflow-y: auto;
  z-index: 2;
}

// 升级按钮
.vip-equity-upgrade {
  height: 76px * 2;
  flex-shrink: 0;
  border-radius: 16px 16px 0px 0px;
  background: #22252c;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 36px 40px;

  &-text {
    display: flex;
    flex-direction: column;
    gap: 10px;

    text:first-of-type {
      color: #fff;
      font-size: 16px * 2;
      font-weight: 400;
    }

    text:last-of-type {
      color: #b9bcc0;
      font-size: 12px * 2;
    }
  }

  &-btn {
    width: 112px * 2;
    height: 42px * 2;
    flex-shrink: 0;
    border-radius: 23px * 2;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 16px * 2;
    font-weight: 500;
    background: linear-gradient(270deg, #ffcfb5 0%, #d37d4e 96.45%);
  }
}

/* 支付 */
.upgrade-popup-content-pay {
  bottom: env(safe-area-inset-bottom);
  width: 100%;
  margin-top: 32px * 2;
}
