/**
 * @description 根据委案id和期数获取相关金额
 */

import { CommonResponse } from '@/serves/interface/miniProgram';
import { serveIp } from '../../config';
import http from '../../http';
import { ContractMoneyCalculateRes, ContractMoneyCalculateVO } from './interface';

/**
 * @description 根据委案id和期数获取相关金额
 * @param {ContractMoneyCalculateVO} params
 * @returns {Promise<ContractMoneyCalculateRes>}
 */
export function appCalculateMoney(
  params: ContractMoneyCalculateVO,
): Promise<CommonResponse<ContractMoneyCalculateRes>> {
  return http.post(`${serveIp.ip}/app-api/crm/contract/appCalculateMoney`, params);
}
