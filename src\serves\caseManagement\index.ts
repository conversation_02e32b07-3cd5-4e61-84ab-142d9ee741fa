import { serveIp } from '../config';
import HTTP from '../http';
import { CommonResponse } from '../interface/miniProgram';
import { CreateCustomerCollecParams, CustomerCollecPageRecord } from './interface';

/**
 * 新增客户信息收集
 * @param {object} params 管理后台 - 客户信息收集创建/修改参数
 * @param {number} params.id 主键ID
 * @param {string} params.customerName 客户姓名
 * @param {number} params.customerSex 客户性别（字典类型：system_user_sex）
 * @param {string} params.customerPhone 客户联系方式
 * @param {number} params.age 年龄
 * @param {string} params.addressProvince 地址编码省
 * @param {string} params.addressCity 地址编码市
 * @param {string} params.addressCode 地址编码
 * @param {string} params.addressName 地址
 * @param {string} params.addressDetail 地址详情
 * @param {number} params.type 身份(字典类型：customer_info_identity)
 * @param {number} params.money 债权/债务金额（字典类型：customer_info_money）
 * @param {number} params.moneyType 债务/债权类型（字典类型：customer_dept_type）
 * @param {string} params.cardProvince 信用卡省编码
 * @param {string} params.cardCity 信用卡市编码
 * @param {string} params.cardCode 信用卡相关银行（字典类型：信用卡：customer_info_type_card 网贷：customer_info_type_lending 借贷：customer_info_type_loan）
 * @param {string} params.cardCodeOther 信用卡其他
 * @param {string} params.cardLendingOther 网贷其他
 * @param {string} params.cardAddr 信用卡开卡城市
 * @param {string} params.cardAddrName 信用卡开卡城市名称
 * @param {string} params.remark 补充说明
 * @returns
 */
/**信息录入新增 */
export function importUserInfo(
  data: CreateCustomerCollecParams,
): Promise<CommonResponse<CustomerCollecPageRecord>> {
  return HTTP.post(serveIp.ip + `/admin-api/crm/customer/create`, data);
}
