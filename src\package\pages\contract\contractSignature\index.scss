.contract-detail-container {
  width: 100%;
  height: 100%;
  background-color: #f5f4f9;
  display: flex;
  overflow: hidden;
}

.nut-signature {
  width: 100%;
  height: 100%;
  z-index: 2;
  .spcanvas_WEAPP Canvas {
    width: 100%;
    height: 100%;
  }
}

.signature-preview {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  image {
    width: 100%;
    height: 100%;
  }
}

.utils-btn {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 64px * 2;
  height: 100%;
  gap: 20px * 2;
  background-color: #fff;
  justify-content: space-between;

  padding-bottom: env(safe-area-inset-bottom);
  padding-top: 87px * 2;

  .utils-btn-left {
    font-size: 32px;
    // width: 100%;
    // text-align: center;
    transform: rotate(90deg);
  }

  .utils-btn-right {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px * 2;

    view:first-child {
      color: #666;
      text-align: center;
      font-size: 18px * 2;
      font-weight: 400;
      line-height: 26px * 2;
      transform: rotate(90deg);
    }

    .confirm-btn {
      width: 40px * 2;
      height: 77px * 2;
      flex-shrink: 0;
      border-radius: 4px;
      background: #f00;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;

      text {
        transform: rotate(90deg);
      }
    }
  }
}

.signature-container {
  flex: 1;
  position: relative;

  .signature-tips {
    position: absolute;
    // width: 100%;
    // height: 100%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(90deg);
    height: fit-content;
    width: fit-content;
    color: #666;
    font-size: 100px * 2;
    font-weight: 400;

    text-align: center;
    letter-spacing: 50px * 2;
    z-index: 0;
    opacity: 0.1;
    white-space: nowrap;
    pointer-events: none;
  }
}
