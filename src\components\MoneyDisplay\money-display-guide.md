# 金额显示最佳实践指南

## 概述

本项目提供了统一的金额格式化和显示解决方案，包括工具函数和 React 组件，用于处理不同场景下的金额显示需求。

## 核心原则

### 1. 为金额字段提供默认值
- **避免显示 undefined/null**：使用 `--` 或 `0.00` 作为默认值
- **统一空值处理**：根据业务场景选择合适的空值显示方式

### 2. 统一保留两位小数格式
- **标准格式**：123.45
- **一致性**：所有金额显示保持相同的小数位数

### 3. 不同场景的显示需求
- **列表页面**：简洁格式，无千分位分隔符
- **详情页面**：完整格式，包含千分位分隔符和货币符号
- **卡片展示**：突出显示，大字体
- **表单输入**：用户友好，显示 0.00 而非空值

## 工具函数使用

### 基础格式化函数

```typescript
import { formatMoney, MoneyDisplayScene, formatMoneyByScene } from '@/utils/money';

// 基础使用
const formatted = formatMoney(123.456, { digits: 2 }); // "123.46"

// 处理空值
const empty = formatMoney(null, { emptyText: '--' }); // "--"
const zero = formatMoney(null, { showZeroForEmpty: true }); // "0.00"

// 添加千分位分隔符
const thousands = formatMoney(1234.56, { thousands: true }); // "1,234.56"

// 添加货币符号和单位
const withSymbol = formatMoney(123.45, { 
  symbol: '￥', 
  unit: '元' 
}); // "￥123.45元"
```

### 场景化格式化函数

```typescript
import { 
  formatMoneyForList,
  formatMoneyForDetail,
  formatMoneyForCard,
  formatMoneyForForm 
} from '@/utils/money';

// 列表页面使用
const listMoney = formatMoneyForList(123.45); // "123.45"

// 详情页面使用
const detailMoney = formatMoneyForDetail(123.45); // "￥1,234.56"

// 卡片显示使用
const cardMoney = formatMoneyForCard(123.45); // "123.45元"

// 表单使用
const formMoney = formatMoneyForForm(null); // "0.00"
```

## React 组件使用

### MoneyDisplay 组件

```tsx
import MoneyDisplay, { 
  MoneyDisplayList,
  MoneyDisplayDetail,
  MoneyDisplayCard,
  MoneyDisplayForm 
} from '@/components/MoneyDisplay';

// 基础使用
<MoneyDisplay 
  amount={123.45} 
  scene={MoneyDisplayScene.LIST}
/>

// 使用 NutUI Price 组件（默认）
<MoneyDisplay 
  amount={123.45}
  usePrice={true}
  priceProps={{ size: 'large', color: 'red' }}
/>

// 使用文本显示
<MoneyDisplay 
  amount={123.45}
  usePrice={false}
  options={{ symbol: '￥', unit: '元' }}
/>

// 便捷组件
<MoneyDisplayList amount={123.45} />
<MoneyDisplayDetail amount={123.45} />
<MoneyDisplayCard amount={123.45} />
<MoneyDisplayForm amount={null} />
```

### 自定义选项

```tsx
<MoneyDisplay 
  amount={123.45}
  options={{
    digits: 2,           // 小数位数
    thousands: true,     // 千分位分隔符
    symbol: '￥',        // 货币符号
    emptyText: '--',     // 空值显示文本
    showZeroForEmpty: false, // 空值时是否显示 0.00
    unit: '元'           // 单位
  }}
/>
```

## 实际应用示例

### 1. 列表页面金额显示

```tsx
// 商品列表
<View className="goods-price">
  <MoneyDisplayList 
    amount={item.price}
    priceProps={{ size: 'large', thousands: true }}
  />
</View>

// 债务列表
<View className="debt-amount">
  <MoneyDisplayList 
    amount={item.actualDebtReductionAmount}
    usePrice={false}
    options={{ emptyText: '--' }}
  />
</View>
```

### 2. 详情页面金额显示

```tsx
// 债务详情
<Cell 
  title="委案金额" 
  extra={
    <MoneyDisplayDetail 
      amount={item.entrustedAmount}
      options={{ unit: '元' }}
    />
  } 
/>
```

### 3. 卡片展示金额

```tsx
// 我的佣金页面
<View className="commission-amount">
  <MoneyDisplayCard
    amount={data?.usableEarnings}
    usePrice={false}
    options={{ unit: '' }}
  />
</View>
```

### 4. 表单中的金额处理

```tsx
// 表单默认值
<Form.Item 
  name="amount"
  initialValue={formatMoneyForForm(initialAmount)}
>
  <Input placeholder="请输入金额" />
</Form.Item>
```

## 空值处理策略

### 不同场景的空值处理

| 场景 | 空值显示 | 说明 |
|------|----------|------|
| 列表页面 | `--` | 简洁，不干扰阅读 |
| 详情页面 | `--` | 明确表示无数据 |
| 卡片展示 | `--` | 保持视觉一致性 |
| 表单输入 | `0.00` | 用户友好，便于编辑 |

### 代码示例

```tsx
// 推荐：使用统一的空值处理
<MoneyDisplayList 
  amount={data?.amount}  // 可能为 null/undefined
  options={{ emptyText: '--' }}
/>

// 不推荐：直接显示可能为空的值
<View>{data?.amount}</View>  // 可能显示 undefined
```

## 迁移指南

### 从现有代码迁移

```tsx
// 旧代码
<View>{data?.amount}</View>
<Price price={data?.amount ?? 0} digits={2} />

// 新代码
<MoneyDisplayList amount={data?.amount} />
<MoneyDisplayCard amount={data?.amount} />
```

### 批量替换建议

1. **直接显示金额的地方**：替换为 `MoneyDisplayList`
2. **使用 Price 组件的地方**：替换为对应的 `MoneyDisplay` 组件
3. **手动格式化的地方**：使用 `formatMoney` 系列函数

## 注意事项

1. **性能考虑**：组件内部已优化，避免重复计算
2. **类型安全**：支持 TypeScript，提供完整的类型定义
3. **样式定制**：可通过 className 和 style 属性自定义样式
4. **兼容性**：与现有的 NutUI Price 组件完全兼容

## 总结

通过使用统一的金额格式化工具和组件，可以：

- ✅ 确保金额显示的一致性
- ✅ 避免空值导致的显示问题
- ✅ 提高代码的可维护性
- ✅ 减少重复的格式化逻辑
- ✅ 提供更好的用户体验
