import { View } from '@tarojs/components';

import PageContainerTmp from '@/components/pageContainerTemp';
import useRequestWithLifecycle from '@/hooks/use-request-with-lifecycle';
import { useEventChannel } from '@/hooks/useEventChannel';
import { selectAppTemplate } from '@/serves/accounting/contractList';
import { saveAppContract } from '@/serves/accounting/saveContract';
import { ContractEnterVO } from '@/serves/accounting/saveContract/interface';
import useDict from '@/serves/dict/hooks';
import { Button, PickerOption, PickerValue } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import { useCallback, useMemo, useState } from 'react';
import { ContractChoose } from './components';
import './index.scss';

/**
 * 合同模板页面
 */
const ContractTemplate = () => {
  // 添加状态管理选择结果
  const [selectedValues, setSelectedValues] = useState<
    Record<'contractType' | 'templateName' | 'debtContractStartDate', PickerValue>
  >({
    contractType: '',
    templateName: '',
    debtContractStartDate: '',
  });
  const { dictData } = useDict();
  const contractTypeList = dictData.filter((item) => item.dictType === 'contract_type');
  const params = useEventChannel<{ data: any }>('appContractTemplate', { cache: true });
  const { data: templateNameList } = useRequestWithLifecycle(
    () =>
      selectAppTemplate({
        pageNo: 1,
        pageSize: 100,
        contractType: selectedValues['contractType'] as string,
        templateStatus: 1,
      }),
    {
      refreshDeps: [selectedValues['contractType']],
      ready: !!selectedValues['contractType'],
    },
  );
  // 提交合同
  const { run: submitContract, loading: submitting } = useRequestWithLifecycle(saveAppContract, {
    manual: true,
    onSuccess(data) {
      if (data.code === 0) {
        Taro.showToast({
          title: '合同提交成功',
          icon: 'success',
        });
        setTimeout(() => {
          Taro.navigateTo({
            url: '/package/pages/me/accounting/index',
          });
        }, 1000);
      } else {
        Taro.showToast({
          title: data.msg,
          icon: 'none',
        });
      }
    },
  });

  const handleChoose = useCallback((itemKey: string, selectedValue: PickerValue[]) => {
    if (selectedValue.length > 0) {
      setSelectedValues((prev) => ({
        ...prev,
        [itemKey]: selectedValue[0],
      }));
    }
  }, []);

  // 合同类型列表
  const contractOptions = useMemo(
    () => [
      ...contractTypeList.map((item) => ({
        text: item.label,
        value: item.value,
        label: item.label,
      })),
    ],
    [contractTypeList],
  );

  // 模板名称列表
  const templateNameOptions = useMemo(() => {
    return templateNameList?.data?.list.map((item) => ({
      text: item.templateName ?? '',
      value: item.id ?? '',
      label: item.templateName ?? '',
    }));
  }, [templateNameList]);

  // 定义选择项配置
  const items = useMemo(
    () => [
      {
        itemKey: 'contractType',
        title: '合同类型',
        extra: '请选择',
        options: contractOptions,
        pickerTitle: '合同类型',
        value: selectedValues['contractType'],
        onChoose: (_: PickerOption[], selectedValue: PickerValue[]) => {
          handleChoose('contractType', selectedValue);
        },
      },
      {
        itemKey: 'templateName',
        title: '模版名称',
        extra: '请选择',
        options: templateNameOptions,
        pickerTitle: '模版名称',
        value: selectedValues['templateName'],
        onChoose: (_: PickerOption[], selectedValue: PickerValue[]) =>
          handleChoose('templateName', selectedValue),
      },
    ],
    [contractOptions, templateNameOptions, selectedValues, handleChoose],
  );
  // 化债合同开始日期选择项
  const debtContractStartDateItems = useMemo(
    () => [
      {
        itemKey: 'debtContractStartDate',
        title: '化债合同开始日期',
        extra: '请选择',
        pickerTitle: '选择日期',
        pickerType: 'date' as 'date',
        value: selectedValues['debtContractStartDate'],
        onChoose: (_: PickerOption[], selectedValue: PickerValue[]) => {
          setSelectedValues((prev) => ({
            ...prev,
            debtContractStartDate: selectedValue[0],
          }));
          handleChoose('debtContractStartDate', [selectedValue.join('-')]);
        },
      },
    ],
    [selectedValues, handleChoose],
  );

  // 处理表单提交
  const handleSubmit = () => {
    if (submitting) {
      return;
    }
    // 这里可以添加表单验证和提交逻辑
    if (!selectedValues['contractType']) {
      Taro.showToast({
        title: '请选择合同类型',
        icon: 'none',
      });
      return;
    } else if (!selectedValues['debtContractStartDate']) {
      Taro.showToast({
        title: '请选择化债合同开始日期',
        icon: 'none',
      });
      return;
    }

    const data: ContractEnterVO = {
      ...params?.data,
      templateId: selectedValues['templateName'],
      commitStatus: 1,
      contractType: selectedValues['contractType'] as string,
      templateName: templateNameOptions?.find(
        (item) => item.value === selectedValues['templateName'],
      )?.label,
      contractName: contractTypeList?.find((item) => item.value === selectedValues['contractType'])
        ?.label,
      startDate: selectedValues['debtContractStartDate'],
    };
    // 提交合同
    submitContract(data);
  };

  const isDisabled = useMemo(() => {
    return (
      !selectedValues['contractType'] ||
      (templateNameOptions?.length && !selectedValues['templateName']) || // 如果存在模板则必选
      !selectedValues['debtContractStartDate']
    );
  }, [selectedValues, templateNameOptions]);

  return (
    <PageContainerTmp title="合同模板" marksType="none" showTabbar={false} isRet align="center">
      <View className="contract-t-page">
        <ContractChoose title="选择合同模板" items={items} />
        <ContractChoose title="选择化债合同开始日期" items={debtContractStartDateItems} />
        {/* 可以添加提交按钮等其他UI元素 */}
        <Button
          className="submit-btn"
          type="primary"
          onClick={handleSubmit}
          block
          loading={submitting}
          disabled={isDisabled}
        >
          提交合同
        </Button>
      </View>
    </PageContainerTmp>
  );
};

export default ContractTemplate;
