import { generateossurl, getFilePreUrl } from '@/serves/file';
import Taro from '@tarojs/taro';
const fs = Taro.getFileSystemManager();
type chooseMediaType = {
  count?: number;
  mediaType?: Array<'image' | 'video'>;
  sourceType?: Array<'album' | 'camera'>;
  maxDuration?: number;
  camera?: 'back' | 'front';
  sizeType?: Array<'original' | 'compressed'>;
}
export default function useUploadFile() {
  const uploadFile = async (chooseMediaType?: chooseMediaType, tempFilePath?: Taro.chooseMedia.ChooseMedia[]): Promise<{ url: string | null }> => {
    try {
      const tempFilePaths = tempFilePath || await chooseMedia(chooseMediaType || {})
      const data = await getFilePreUrl({ path: tempFilePaths[0].tempFilePath.substring(tempFilePaths[0].tempFilePath.lastIndexOf('/') + 1) })
      const file = await readFile(tempFilePaths[0].tempFilePath)
      await generateossurl({ url: data.data.uploadUrl, flie: file });
      return { url: data.data.url }
    } catch (error) {
      return { url: null }
    }

  }
  return { uploadFile }
}
const chooseMedia = (param?: chooseMediaType) => {
  return new Promise<Taro.chooseMedia.ChooseMedia[]>((resolve, reject) => {
    Taro.chooseMedia({
      ...param,
      success(res) {
        const tempFilePaths = res.tempFiles;
        console.log(res);
        resolve(tempFilePaths)
      },
    });
  })

}
const readFile = (filePath: string) => {
  return new Promise<ArrayBuffer>((resolve, reject) => {
    fs.readFile({
      filePath,
      success: async (res) => {
        const arrayBuffer = res.data as ArrayBuffer;
        resolve(arrayBuffer)
      }
    });
  })
}