import { serveIp } from '../config';
import HTTP from '../http';
import { CommonResponse } from '../interface/miniProgram';
import { GetFilePreUrlRes } from './interface';
/**信息录入新增 */
export function getFilePreUrl(
  data: { path: string },
): Promise<CommonResponse<GetFilePreUrlRes>> {
  return HTTP.get(serveIp.ip + `/app-api/infra/file/presigned-url`, data);
}
export function generateossurl(
  data: { url: string ,flie:ArrayBuffer,},
): Promise<CommonResponse<GetFilePreUrlRes>> {
  return HTTP.put(data.url, data.flie,'application/octet-stream');
}
