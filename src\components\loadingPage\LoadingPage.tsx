import { View } from '@tarojs/components';
import { FC } from 'react';
import './index.scss';

interface LoadingPageProps {
  /**
   * 是否显示加载页面
   */
  loading?: boolean;
  /**
   * 自定义加载提示文字
   */
  loadingText?: string;
  /**
   * 加载图标大小，单位px
   */
  size?: number;
  /**
   * 加载图标颜色
   */
  color?: string;
  /**
   * 子元素，加载完成后显示的内容
   */
  children?: React.ReactNode;
}

/**
 * 通用加载页面组件
 */
const LoadingPage: FC<LoadingPageProps> = ({
  loading = true,
  loadingText = '加载中...',
  size = 32,
  color = '#f00',
  children,
}) => {
  if (!loading) {
    return <>{children}</>;
  }

  return (
    <View className="loading-page">
      <View className="loading-wrapper">
        <View className="loading-spinner" style={{ width: size, height: size }}>
          <View className="loading-dot" style={{ backgroundColor: color }}></View>
          <View className="loading-dot" style={{ backgroundColor: color }}></View>
          <View className="loading-dot" style={{ backgroundColor: color }}></View>
        </View>
        {loadingText && <View className="loading-text">{loadingText}</View>}
      </View>
    </View>
  );
};

export default LoadingPage;
