import NavigationBar from '@/components/navigation-bar';
import CustomTabBar from '@/components/tabbar';
import useTabBar from '@/hooks/use-tab-bar';
import List from '@/pages/evaluation/components/list';
import ReporterList from '@/pages/evaluation/components/repoterList';
import { Tabs } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import { useCallback, useState } from 'react';
import { useEnv, useModal, useNavigationBar, useToast } from 'taro-hooks';
import './index.scss';

const Index = () => {
  const env = useEnv();
  useTabBar(2);
  const { setTitle } = useNavigationBar({ title: 'Taro Hooks' });
  const showModal = useModal({
    title: 'Taro Hooks Canary!',
    showCancel: false,
    confirmColor: '#8c2de9',
    confirmText: '支持一下',
  });
  const { show } = useToast({ mask: true });

  const handleModal = useCallback(() => {
    showModal({ content: '不如给一个star⭐️!' }).then(() => {
      show({ title: '点击了支持!' });
    });
  }, [show, showModal]);

  return (
    <View className="tabbarContainer">
      <View className="wrapper">
        {env !== 'TT' && <NavigationBar title="评测"></NavigationBar>}
        <View className="marks">
          <View className="markright"></View>
          <View className="markleft"></View>
        </View>
        <TabsOfList />
      </View>
      <CustomTabBar></CustomTabBar>
    </View>
  );
};

const TabsOfList = () => {
  const [tabvalue, setTabvalue] = useState<string | number>(0);
  return (
    <Tabs
      value={tabvalue}
      onChange={(value) => {
        console.log();
        setTabvalue(value);
      }}
      className="tabs-of-list"
      style={{
        width: '100%',
        '--nutui-tabs-tabpane-padding': '8px 12px',
      }}
    >
      <Tabs.TabPane title="测评器" value={0}>
        <List></List>
      </Tabs.TabPane>
      <Tabs.TabPane title="历史报告" value={1}>
        <ReporterList isShow={tabvalue === 1} />
      </Tabs.TabPane>
    </Tabs>
  );
};

export default Index;
