export default {
  pages: [
    'pages/home/<USER>', // 首页
    'pages/me/index', // 我的

    'pages/index/index', // 旧版首页
    'pages/plan/index', // 计划
    'pages/evaluation/index', // 测评
    'pages/message/index', // 消息中心
    'pages/me-work-plan/index', // 我的 - 工作任务
    'pages/report/index', // 报告详情
    'pages/qaSmart/index', // 智能问答
    'pages/userPrivacy/index', // 我的 - 设置 - 用户隐私协议
  ],
  tabBar: {
    custom: true,
    color: '#000000',
    selectedColor: '#DC143C',
    backgroundColor: '#ffffff',
    borderStyle: 'black',
    list: [
      {
        pagePath: 'pages/home/<USER>',
        text: '首页',
      },
      {
        pagePath: 'pages/plan/index',
        text: '计划',
      },
      {
        pagePath: 'pages/evaluation/index',
        text: '测评',
      },
      // {
      //   pagePath: 'pages/message/index',
      //   text: '资讯',
      // },
      {
        pagePath: 'pages/me/index',
        text: '我的',
      },
    ],
    customTabBarStyle: './custom-tab-bar/index',
  },
  subpackages: [
    {
      root: 'packageA',
      pages: [
        'pages/InforEntry/index', // 添星管家
        'pages/InforEntry/InforEntryTwoPage/index', // 添星管家 - 信息录入
      ],
    },
    {
      root: 'packageB',
      pages: [
        'pages/debtDetail/index', // 计划 - 债务详情
        'pages/repayment/index', // 计划 - 债务详情 - 还款
      ],
    },
    {
      root: 'package',
      pages: [
        'pages/notebook/index', // 首页 - 笔记本
        'pages/notebook/notebookDetail/index', // 首页 - 笔记本 - 笔记详情
        'pages/notebook/newNote/index', // 首页 - 笔记本 - 新建记事本
        'pages/newTodo/index', // 我的 - 工作任务 - 新建日程
        'pages/newTodo/TodoDetail/index', // 我的 - 工作任务 - 新建日程 - 日程
        'pages/debtDetailOfMe/index', // 我的 - 化债明细
        'pages/contract/index', // 我的 - 我的合同
        'pages/contract/contractSignature/index', // 我的 - 我的合同 - 电子签名
        'pages/messageList/index', // 我的 - 消息列表
        'pages/settings/index', // 我的 - 设置
        'pages/settings/realName/index', // 我的 - 设置 - 实名认证
        'pages/settings/profile/index', // 我的 - 设置 - 个人资料
        'pages/settings/proof/index', // 我的 - 设置 - 证明材料
        'pages/settings/logOut/index', // 我的 - 设置 - 注销账号
        'pages/settings/redemptionCode/index', // 我的 - 设置 - 兑换码
        'pages/settings/redeemDetails/index', // 我的 - 设置 - 兑换
        'pages/settings/bindMallAccount/index', // 我的 - 设置 - 绑定添星商城账户
        'pages/bankCard/index', // 我的 - 银行卡
        'pages/bindBandCard/index', // 我的 - 银行卡 - 绑定银行卡
        'pages/vipService/index', // 我的 - （点击会员卡）会员服务
        'pages/aboutTx/index', // 我的 - 设置 - 关于我们
        'pages/feedback/index', // 我的 - 建议反馈
        'pages/feedback/feedbackHistory/index', // 我的 - 建议反馈 - 反馈历史
        'pages/feedback/historyDetail/index', // 我的 - 建议反馈 - 反馈历史 - 详情
        'pages/me/myTeam/index', // 我的 - 我的团队
        'pages/me/myCommissions/index', // 我的 - 我的佣金
        'pages/me/accounting/index', // 我的 - 账管顾问
        'pages/me/accounting/contractTemplate/index', // 我的 - 账管顾问 - 合同模板
        'pages/me/commissionDetail/index', // 我的 - 账管顾问 - 委案详情
        'pages/me/commissionDetailDeep/index', // 我的 - 账管顾问 - 委案详情 - 委案明细
      ],
    },
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '',
    navigationBarTextStyle: 'black',
  },
  lazyCodeLoading: 'requiredComponents',
  rendererOptions: {
    skyline: {
      defaultDisplayBlock: true,
      defaultContentBox: true,
    },
  },
};
