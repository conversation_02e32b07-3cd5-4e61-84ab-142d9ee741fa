.work {
  &-date-select {
    display: flex;
    height: 40px * 2;
    align-items: center;
    gap: 12px;

    text {
      color: #131313;
      font-size: 14px * 2;
    }
  }

  &-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 24px;
    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15);
    border: 1px solid #f5f5f5;
    margin: 20px 0;
    background-color: #fff;
    // justify-content: space-around;

    &:first-child {
      margin-top: 0;
    }

    .detail {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .title {
        color: #131313;
        font-size: 14px * 2;
        font-style: normal;
        font-weight: 500;
      }

      .desc {
        color: #666;
        font-size: 12px * 2;
        font-weight: 400;
      }

      .info {
        color: #9d9d9d;
        font-size: 11px * 2;
      }
    }
  }
}

// .detail-btn-c {

// }
.detail-button {
  margin-left: auto;
  margin-bottom: 1px * 2;
}
