import useRequestWithLifecycle from '@/hooks/use-request-with-lifecycle';
import { getMemberCardActivationDetails } from '@/serves/member';
import { useState } from 'react';
import { useUpgradeConditions } from './use-upgrade-conditions';

/**
 * 会员升级弹窗逻辑Hook
 * 处理弹窗状态和升级逻辑
 *
 * @param {number|string} cardId - 卡片ID
 * @returns 弹窗状态和相关方法
 */
export const useUpgradePopup = (cardId: number | string) => {
  const [showPopup, setShowPopup] = useState(false);

  // 获取会员卡激活详情
  const { data } = useRequestWithLifecycle(
    () =>
      getMemberCardActivationDetails({
        memberCardId: cardId,
      }),
    {
      ready: !!cardId,
      refreshDeps: [cardId],
    },
  );

  // 使用自定义Hook处理升级条件数据
  const { upgradeWays, memberFee, canUpgrade } = useUpgradeConditions(data?.data);

  /**
   * 处理升级按钮点击事件
   */
  const handleUpgrade = () => {
    console.log('handleUpgrade');
    // TODO: 实现升级逻辑
  };

  /**
   * 打开弹窗
   */
  const openPopup = () => setShowPopup(true);

  /**
   * 关闭弹窗
   */
  const closePopup = () => setShowPopup(false);

  return {
    showPopup,
    openPopup,
    closePopup,
    upgradeWays,
    memberFee,
    canUpgrade,
    handleUpgrade,
  };
};
