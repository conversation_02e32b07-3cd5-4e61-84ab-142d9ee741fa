import EmptyContainer from '@/components/empty-container';
import { getMyEarnings } from '@/serves/member/myCommissions';
import useUserStore from '@/store/useUserStore';
import { FC, memo } from 'react';
import { useRequest } from 'taro-hooks';
import DebtContentList from '../../../../debtDetailOfMe/components/DebtContentList';

const RevenueList: FC = () => {
  const { userInfo } = useUserStore((state) => state);
  const { data } = useRequest(() => getMyEarnings({ memberUserId: userInfo?.userId, type: 0 }), {
    cacheKey: 'my-commissions-list',
    ready: !!userInfo?.userId,
    refreshDeps: [userInfo?.userId],
  });

  if (!data?.data?.earningsMoneyDetailList) {
    return <EmptyContainer title="暂无数据" />;
  }
  return <DebtContentList data={data?.data?.earningsMoneyDetailList || []} />;
};

export default memo(RevenueList);
