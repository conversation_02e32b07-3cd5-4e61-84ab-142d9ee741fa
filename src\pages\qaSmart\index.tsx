import useUserStore from '@/store/useUserStore';
import { WebView } from '@tarojs/components';
import { useLoad } from '@tarojs/taro';
import { useState } from 'react';
import './index.scss';
const Index = () => {
  const [url, setUrl] = useState('');
  const [userInfo] = useUserStore((state) => [state.userInfo]);
  useLoad((option) => {
    console.log(option);
    //https://guanjia.hunantianxing.com//http://192.168.110.88:3000
    let src = `https://guanjia.hunantianxing.com/?query=${encodeURIComponent(JSON.stringify(userInfo))}`;
    if (option.url) {
      src = option.url;
    }
    setUrl(src);
  });
  return url ? <WebView src={url} /> : null;
};

export default Index;
