import { ResultModal } from '@/components/ResultModal';
import useCountdown from '@/hooks/use-countdown';
import useProfile from '@/hooks/use-profile';
import useRequestWithLifecycle from '@/hooks/use-request-with-lifecycle';
import { sendCode, verify } from '@/serves/realName';
import { Button, Cell, Form, FormInstance, Input } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { memo, useRef, useState } from 'react';
import './index.scss';

// 提取验证码按钮为独立组件
const CodeButton = memo(
  ({ onGetCode, loading }: { onGetCode: () => Promise<boolean>; loading?: boolean }) => {
    const { count, start, isRunning } = useCountdown(60);

    const handleClick = async () => {
      if (isRunning || loading) return;

      try {
        const success = await onGetCode();
        if (success) {
          start();
        }
      } catch (error) {
        console.error('获取验证码失败', error);
      }
    };

    return (
      <Button
        type="primary"
        size="small"
        fill="none"
        onClick={handleClick}
        disabled={isRunning || loading}
        loading={loading}
      >
        {!isRunning ? '获取验证码' : `${count}秒后重新获取`}
      </Button>
    );
  },
);

export default function RealName() {
  const [visible, setVisible] = useState(false);
  const [type, setType] = useState<'success' | 'fail' | 'loading'>('success');
  const formRef = useRef<FormInstance>(null);
  const { runAsync: runSendCode, loading: sendCodeLoading } = useRequestWithLifecycle(sendCode, {
    manual: true,
    timeout: 8000,
    onTimeout: () => {
      Taro.showToast({
        title: '请求超时，请稍后再试',
        icon: 'none',
      });
    },
  });
  // 获取用户信息
  const { userInfo, refresh } = useProfile();

  if (userInfo?.data?.realAuthentication === '1') {
    return (
      <Cell.Group>
        <Cell title="真实姓名" extra={userInfo?.data?.name} />
        <Cell title="身份证号" extra={userInfo?.data?.idCard} />
        <Cell title="手机号" extra={userInfo?.data?.mobile || '暂无'} />
      </Cell.Group>
    );
  }

  const onFinish = async (values: any) => {
    setVisible(true);
    setType('loading');

    const data = values;
    console.log('verify data', data);
    const res = await verify(data);
    if (res.data) {
      setType('success');
      setTimeout(() => {
        refresh();
      }, 1000);
    } else {
      setType('fail');
    }
  };

  const onFinishFailed = (_: any, errors: any) => {
    console.log(errors);
  };

  const handleGetCode = async () => {
    const validateRes = await formRef.current?.validateFields(['mobile']);
    if (validateRes?.length) {
      return false;
    }

    const mobile = formRef.current?.getFieldValue('mobile');
    const res = await runSendCode({
      mobile,
      scene: 5,
    });
    if (res.code === 0) {
      if (res.data) {
        Taro.showToast({
          title: '发送成功',
          icon: 'success',
        });
        return true;
      }
    } else {
      Taro.showToast({
        title: res.msg,
        icon: 'none',
      });
    }

    return false;
  };

  return (
    <View className="real-name">
      <Form
        ref={formRef}
        divider
        labelPosition="left"
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
      >
        <Form.Item
          label="真实姓名"
          name="name"
          rules={[{ required: true, message: '请输入真实姓名' }]}
        >
          <Input placeholder="请输入真实姓名" type="text" align="right" clearable />
        </Form.Item>
        <Form.Item
          label="身份证号"
          name="idNo"
          rules={[
            { required: true, message: '请输入身份证号' },
            {
              pattern:
                /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
              message: '请输入正确的身份证号',
            },
          ]}
        >
          <Input placeholder="请输入身份证号" type="text" align="right" clearable />
        </Form.Item>
        <Form.Item
          label="手机号"
          name="mobile"
          rules={[
            { required: true, message: '请输入手机号' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
          ]}
        >
          <Input placeholder="请输入手机号" type="number" align="right" clearable />
        </Form.Item>
        <View className="code-container">
          <Form.Item
            label="验证码"
            name="code"
            rules={[{ required: true, message: '请输入验证码' }]}
          >
            <Input
              placeholder="请输入4位数字验证码"
              type="text"
              maxLength={4}
              align="right"
              clearable
            />
          </Form.Item>
          <CodeButton onGetCode={handleGetCode} loading={sendCodeLoading} />
        </View>

        <View className="tips">
          实名认证要求
          <View className="tips-list">
            <View>1. 身份证号上的姓名和输入的姓名一致，且身份证在有效期内。</View>
            <View>2. 手机号绑定的身份证号与输入的身份证</View>
            <View>3. 手机号能正常接收短信。</View>
            <View>4. 输入的短信验证码在有效期内（短信验证码5分钟有效）。</View>
          </View>
        </View>
        <Button type="primary" className="submit-btn" formType="submit">
          开始认证
        </Button>

        <ResultModal
          visible={visible}
          onClose={() => setVisible(false)}
          type={type}
          successTitle="实名认证成功"
          failTitle="实名认证失败"
          failDesc="姓名、身份证号或手机号不匹配"
          loadingTitle="认真信息审核中"
        />
      </Form>
    </View>
  );
}
