import SvgIcon from '@/pages/me/components/SvgIcon';
import {
  Cell,
  DatePicker,
  Picker,
  PickerOption,
  PickerOptions,
  PickerValue,
} from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import { useCallback, useMemo, useState } from 'react';
import './ContractChoose.scss';

/**
 * 选择合同模版项的属性接口
 */
export interface ContractChooseItemProps {
  itemKey: string; // 唯一标识
  title: string; // 标题
  extra: string; // 默认值
  onChoose?: (selectedOptions: PickerOption[], selectedValue: PickerValue[]) => void; // 选择回调
  options?: PickerOption[]; // 选项
  pickerTitle: string; // 选择器标题
  value?: PickerValue; // 当前选中的值
  pickerType?: 'date' | ''; // 选择器类型
}

/**
 * 选择合同模版组的属性接口
 */
export interface ContractChooseProps {
  title: string;
  items: ContractChooseItemProps[];
}

/**
 * 选择合同模版组件
 */
export const ContractChoose = ({ title, items }: ContractChooseProps) => {
  return (
    <Cell.Group className="contract-choose-group">
      <Cell>
        <View className="contract-choose-item-title">{title}</View>
      </Cell>
      {items.map((item) => (
        <ContractChooseItem key={item.itemKey} {...item} />
      ))}
    </Cell.Group>
  );
};

/**
 * 选择合同模版项组件
 */
export const ContractChooseItem = ({
  title,
  extra,
  onChoose,
  options,
  pickerTitle,
  value,
  pickerType,
}: ContractChooseItemProps) => {
  const [showPicker, setShowPicker] = useState(false);

  // 获取今天的日期作为默认值
  const today = useMemo(() => {
    return new Date();
  }, []);

  // 处理确认选择
  const handleConfirm = useCallback(
    (selectedOptions: PickerOption[], selectedValue: PickerValue[]) => {
      onChoose?.(selectedOptions, selectedValue);
      setShowPicker(false);
    },
    [onChoose],
  );

  // 处理取消选择
  const handleClose = useCallback(() => {
    setShowPicker(false);
  }, []);

  const handleDateConfirm = useCallback(
    (selectedOptions: PickerOptions, selectedValue: PickerValue[]) => {
      onChoose?.(selectedOptions, selectedValue);
    },
    [onChoose],
  );

  const extraValue = useMemo(() => {
    if (pickerType === 'date') {
      return value ? value : extra;
    }
    return options?.find((item) => item.value === value)?.label || extra;
  }, [pickerType, value, options, extra]);

  return (
    <Cell onClick={() => setShowPicker(true)}>
      <View className="contract-choose-item">
        <View className="title">{title}</View>
        <View className="extra">
          <View className={`extra-text ${value ? 'active' : ''}`}>{extraValue}</View>
          <SvgIcon name="arrow-right-small" size={12} />
        </View>
      </View>
      {pickerType !== 'date' ? (
        <Picker
          title={pickerTitle}
          visible={showPicker}
          defaultValue={value ? [value] : undefined}
          options={[options ?? []]}
          onConfirm={handleConfirm}
          onClose={handleClose}
        />
      ) : (
        <DatePicker
          title={pickerTitle}
          visible={showPicker}
          defaultValue={value ? new Date(value.toString()) : today}
          showChinese
          onClose={handleClose}
          onConfirm={handleDateConfirm}
        />
      )}
    </Cell>
  );
};
