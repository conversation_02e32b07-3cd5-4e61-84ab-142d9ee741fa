# LoadingPage 通用加载页面组件

一个简单易用的加载页面组件，适用于数据加载、请求等场景。符合小程序风格设计。

## 特性

- 简洁的加载动画效果
- 可自定义加载文字
- 可自定义加载图标大小和颜色
- 支持包裹内容，在加载完成后自动显示

## 使用方法

```jsx
import LoadingPage from '@/components/loadingPage';

// 基本用法
<LoadingPage loading={true} />

// 自定义文字
<LoadingPage loading={true} loadingText="正在加载数据..." />

// 自定义样式
<LoadingPage loading={true} size={40} color="#f00" />

// 包裹内容（内容只在loading=false时显示）
<LoadingPage loading={loading}>
  <View>加载完成后显示的内容</View>
</LoadingPage>
```

## API

### Props

| 参数        | 说明                         | 类型      | 默认值      |
| ----------- | ---------------------------- | --------- | ----------- |
| loading     | 是否显示加载中               | boolean   | true        |
| loadingText | 加载文案                     | string    | '加载中...' |
| size        | 加载图标大小，单位px         | number    | 32          |
| color       | 加载图标颜色                 | string    | '#f00'      |
| children    | 子元素，加载完成后显示的内容 | ReactNode | -           |

## 示例

参考 `example.tsx` 文件，展示了组件的基本使用方法。
