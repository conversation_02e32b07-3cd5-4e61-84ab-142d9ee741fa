/**
 * 会员卡升级相关接口类型定义
 */

/**
 * 会员卡开通方式完成状态 VO
 */
export interface AppMemberCardUserActivationFinishVo {
  /**
   * 状态：false-未完成 true-完成
   */
  finishStatus: boolean;

  /**
   * 字典键值
   */
  openCondition: string;

  /**
   * 字典标签
   */
  description: string;
}

/**
 * 会员卡开通方式 VO
 */
export interface AppMemberCardUserActivationModeVo {
  /**
   * 分组
   */
  activationGroup: number;

  /**
   * 会员卡开通方式
   */
  activationModeDetailsList: AppMemberCardUserActivationFinishVo[];
}

/**
 * 用户 App - 会员卡开通详情 VO
 */
export interface AppMemberCardUserActivationDetailsVo {
  /**
   * 会员卡开通方式详情
   */
  activationModeList: AppMemberCardUserActivationModeVo[];

  /**
   * 会员卡开通费用
   */
  memberFee: number;
}

/**
 * 获取会员卡开通详情响应
 */
export interface MemberCardActivationDetailsResponse {
  code: number;
  data: AppMemberCardUserActivationDetailsVo;
  msg: string;
}

/**
 * 获取会员卡开通详情请求参数
 */
export interface MemberCardActivationDetailsParams {
  /**
   * 会员卡编号
   */
  memberCardId: number | string;
}
