// Parameter interface
export interface GetPageListParams {
  /*状态 “00”：待沟通；“01”：已完成 */
  status?: string;

  /*债务人id */
  debtorId?: number;
}

// Response interface
export interface GetPageListRes {
  /* */
  code: number;

  /* */
  data: {
    /*委案数量 */
    caseCount: number;

    /*债务人ID */
    debtorId: number;

    /*债务人类型 */
    debtorType: string;

    /*债务人证件类型 */
    debtorIdType: string;

    /*债务人名称 */
    debtorName: string;

    /*统一社会信用代码/个人身份证号 */
    debtorCreditCode: string;
  }[];

  /* */
  msg: string;
}

// 获得债务人所有委案信息 参数
export interface GetDebtorIdCollecParams {
  /*状态 “00”：待沟通；“01”：已完成 */
  status?: string;

  /*债务人id */
  debtorId?: number;
}

// 获得债务人所有委案信息 响应
export interface GetDebtorIdCollecRes {
  /* */
  code: number;

  /* */
  data: {
    /*债务人ID */
    debtorId: number;

    /*债务人类型 */
    debtorType: string;

    /*债务人证件类型 */
    debtorIdType: string;

    /*债务人名称 */
    debtorName: string;

    /*统一社会信用代码/个人身份证号 */
    debtorCreditCode: string;

    /*债务人联系电话 */
    debtorContactPhone: string;

    /*债务人办公地址 */
    debtorOfficeAddress: string;

    /*债务人办公详细地址 */
    debtorOfficeDetailAddress: string;

    /*委案信息详情 */
    debtorCaseEntrustDetailVOList: {
      /* 签署状态 */
      //	合同状态: 草稿 draft, 审批中 in_approval, 审批通过 end_approval, 审批驳回 back_approval, 作废 cancel, 履约中 in_performance, 合同结束 end, 变更 change
      status:
        | 'draft'
        | 'in_approval'
        | 'end_approval'
        | 'back_approval'
        | 'cancel'
        | 'in_performance'
        | 'end'
        | 'change';
      debtReductionPeriods?: number;

      /*主键 */
      id: number;

      /*逾期账户 */
      overdueAccount: string;

      /*委案金额 */
      entrustedAmount: number;

      /*利息 */
      interest: number;

      /*滞纳金 */
      lateFee: number;

      /*合同状态：0 未关联  1已关联 */
      contractStatus: number;

      /*逾期阶段 */
      caseStage: string;

      /*业务员id */
      salesman: number;

      /*客户信息ID */
      customerCollectId: number;

      /*年化率 */
      annualizedRate: number;

      /*贷款类型（来自字典【case_loan_type】） */
      loanType: string;

      /*债权人ID */
      creditorId: number;

      /*债权人类型（来自字典【case creditor_type】） */
      creditorType: string;

      /*债权人会员ID */
      creditorMemberId: number;

      /*债权人证件类型 */
      creditorIdType: string;

      /*债权人名称 */
      creditorName: string;

      /*债权人统一社会信用代码 */
      creditorCreditCode: string;

      /*债务人ID */
      debtorId: number;

      /*债务人类型 */
      debtorType: string;

      /*债务人证件类型 */
      debtorIdType: string;

      /*债务人名称 */
      debtorName: string;

      /*债务人统一社会信用代码 */
      debtorCreditCode: string;
    }[];
  };

  /* */
  msg: string;
}
