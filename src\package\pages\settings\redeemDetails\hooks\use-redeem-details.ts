import { useCallback, useState } from 'react';

interface RedeemItem {
  id: number;
  name: string;
  image: string;
  stock: number;
}

// 兑换商品数据
const mockData: RedeemItem[] = [
  {
    id: 1,
    name: '凤凰卡',
    image:
      'https://tianxing-bucket.oss-cn-hangzhou.aliyuncs.com/5%20%E4%BD%93%E9%AA%8C%E5%8D%A1.png',
    // 库存
    stock: 100,
  },
  {
    id: 2,
    name: '体验卡',
    image:
      'https://tianxing-bucket.oss-cn-hangzhou.aliyuncs.com/5%20%E4%BD%93%E9%AA%8C%E5%8D%A1.png',
    stock: 0,
  },
  {
    id: 3,
    name: '体验卡',
    image:
      'https://tianxing-bucket.oss-cn-hangzhou.aliyuncs.com/5%20%E4%BD%93%E9%AA%8C%E5%8D%A1.png',
    stock: 100,
  },
];

/**
 * 兑换详情页状态逻辑Hook
 *
 * @returns 兑换详情页所需的状态和处理函数
 */
export const useRedeemDetails = () => {
  // 弹窗状态
  const [showPopup, setShowPopup] = useState(false);
  const [showResultModal, setShowResultModal] = useState(false);

  // 商品数据状态
  const [redeemItems] = useState<RedeemItem[]>(mockData);

  // 处理兑换操作
  const handleRedeem = useCallback(() => {
    // TODO: 实现兑换逻辑，可能需要调用API
    setShowResultModal(true);
  }, []);

  // 处理关闭结果弹窗
  const handleCloseResultModal = useCallback(() => {
    setShowResultModal(false);
  }, []);

  // 处理打开兑换弹窗
  const handleOpenPopup = useCallback(() => {
    setShowPopup(true);
  }, []);

  // 处理关闭兑换弹窗
  const handleClosePopup = useCallback(() => {
    setShowPopup(false);
  }, []);

  return {
    // 状态
    showPopup,
    showResultModal,
    redeemItems,

    // 处理函数
    handleRedeem,
    handleCloseResultModal,
    handleOpenPopup,
    handleClosePopup,
  };
};
