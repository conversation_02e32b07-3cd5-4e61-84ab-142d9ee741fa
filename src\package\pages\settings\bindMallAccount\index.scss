.bind-mall-account {
  width: 100%;
  height: 100%;
  padding: 28px 24px;
}

.context {
  border-radius: 8 * 2px;
  background: #fff;
  padding: 0px 32px 48px 32px;
}

.marks-bind-mall-account {
  background: linear-gradient(180deg, #f8dadd 0%, #f5f4f9 17.48%);
}

.input-container {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  // padding: 0 24rpx;
}

.input-wrapper {
  position: relative;
  height: 92rpx; // 46px * 2
  display: flex;
  align-items: center;
}

.label {
  font-size: 28rpx; // 14px * 2
  color: #666666;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  line-height: normal;
  flex-shrink: 0;
}

.input {
  flex: 1;
  font-size: 28rpx; // 14px * 2
  color: #333333;
  font-family: 'PingFang SC', sans-serif;
  padding: 0 20rpx;
  height: 100%;
  text-align: right;
}

.placeholder {
  color: #9d9d9d;
  font-size: 28rpx; // 14px * 2
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  line-height: normal;
}

.code-input {
  padding-right: 200rpx; // 增加右侧间距，为获取验证码按钮留出更多空间
}

.get-code {
  position: absolute;
  right: 20rpx; // 增加右边距，避免贴边
  font-size: 26rpx; // 稍微减小字体大小，避免文本过长
  color: #ff0000;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  line-height: 1.2; // 设置行高，确保文本垂直居中
  height: 100%;
  display: flex;
  align-items: center;
  padding-left: 20rpx;
  padding-right: 10rpx; // 增加右内边距
  cursor: pointer;
  white-space: nowrap; // 防止文本换行
  min-width: 160rpx; // 设置最小宽度，确保按钮区域足够
  justify-content: center; // 文本居中对齐

  &.disabled {
    color: #cccccc;
    cursor: not-allowed;
  }
}

.divider {
  height: 2rpx; // 1px * 2
  background-color: #e5e5e5;
  // margin-left: 56rpx; // 28px * 2
}

.bind-button {
  background-color: #ff0000;
  border-radius: 24rpx; // 12px * 2
  height: 88rpx; // 44px * 2
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40rpx; // 20px * 2
  cursor: pointer;

  &.disabled {
    background-color: #cccccc;
    cursor: not-allowed;

    .bind-button-text {
      color: #999999;
    }
  }
}

.bind-button-text {
  font-size: 36rpx; // 18px * 2
  color: #fcfcfc;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  line-height: normal;
}

.footer {
  color: #9d9d9d;
  text-align: left;
  font-family: 'PingFang SC';
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-top: 48rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

// 已绑定状态样式
.bound-mall-account {
  width: 100%;
  height: 100%;
  padding: 28px 24px;
}

.bound-context {
  border-radius: 16rpx; // 8px * 2
  background: #ffffff;
  padding: 0;
  height: 276rpx; // 138px * 2
  width: 702rpx; // 351px * 2
}

.bound-info-item {
  background-color: #ffffff;
  height: 92rpx; // 46px * 2
  overflow: hidden;
}

.bound-info-wrapper {
  position: relative;
  height: 92rpx; // 46px * 2
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx; // 16px * 2
}

.bound-label {
  font-size: 28rpx; // 14px * 2
  color: #666666;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  line-height: normal;
  text-align: left;
}

.bound-value {
  font-size: 28rpx; // 14px * 2
  color: #9d9d9d;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  line-height: normal;
  text-align: right;
}

.bound-divider {
  height: 2rpx; // 1px * 2
  background-color: #f5f6fa;
  margin-left: 28rpx; // 14px * 2
  width: 638rpx; // 319px * 2
}

.switch-bind-mall-account {
  margin-top: 28rpx; // 20px * 2
}

// 响应式设计 - 针对不同屏幕尺寸优化
@media screen and (max-width: 320px) {
  // 小屏幕设备优化
  .code-input {
    padding-right: 180rpx; // 小屏幕减少右侧间距
  }

  .get-code {
    font-size: 24rpx; // 进一步减小字体
    min-width: 140rpx; // 减小最小宽度
    right: 15rpx; // 减小右边距
  }
}

@media screen and (min-width: 414px) {
  // 大屏幕设备优化
  .code-input {
    padding-right: 220rpx; // 大屏幕增加右侧间距
  }

  .get-code {
    font-size: 28rpx; // 恢复正常字体大小
    min-width: 180rpx; // 增加最小宽度
    right: 25rpx; // 增加右边距
  }
}
