import { Button, Image } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';

interface RedeemDetailCardProps {
  data: {
    id: number;
    name: string;
    image: string;
    stock: number;
  };
  onRedeem: () => void;
}

/**
 * 兑换详情卡片组件
 * 展示可兑换商品的卡片，包含商品图片、名称、库存状态和兑换按钮
 *
 * @param {object} props - 组件属性对象
 * @param {object} props.data - 商品数据对象
 * @param {number} props.data.id - 商品ID
 * @param {string} props.data.name - 商品名称
 * @param {string} props.data.image - 商品图片URL
 * @param {number} props.data.stock - 商品库存数量
 * @param {Function} props.onRedeem - 点击兑换按钮时的回调函数
 * @returns {JSX.Element} 兑换详情卡片组件
 */
const RedeemDetailCard = ({ data, onRedeem }: RedeemDetailCardProps) => {
  return (
    <View className="redeem-detail-card">
      {data.stock <= 0 && <View className="sold-out-badge">售罄</View>}
      <View className="redeem-detail-card-image">
        <Image src={data.image} />
      </View>
      <View className="redeem-detail-card-name">{data.name}</View>
      {data.stock > 0 ? (
        <Button className="redeem-btn" onClick={onRedeem}>
          立即兑换
        </Button>
      ) : (
        <View className="redeem-btn disabled">库存不足</View>
      )}
    </View>
  );
};

export default RedeemDetailCard;
