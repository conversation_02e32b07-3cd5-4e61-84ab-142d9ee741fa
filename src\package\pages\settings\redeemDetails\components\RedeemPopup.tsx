import { Button, Image } from '@nutui/nutui-react-taro';
import { PageContainer, Text, View } from '@tarojs/components';

// 兑换规则数据
const redeemRulesData = [
  {
    title: '一、可兑换商品',
    rules: [
      {
        id: 1,
        content: `1. 凤凰体验卡：兑换凤凰体验卡会员权益自兑换日起生效，有效期 90天。`,
      },
      {
        id: 2,
        content: `2. 爱心卡：兑换爱心卡会员权益自兑换日起生效，有效期 30天。`,
      },
    ],
  },
  {
    title: '二、兑换码有效期及使用限制',
    rules: [
      {
        id: 1,
        content: '1. 兑换码有效期：90天，截至2025年9月30 日前完成兑换，逾期失效。',
      },
      {
        id: 2,
        content: `2. 使用次数：每个兑换码仅使用 1 次，不可重复兑换。`,
      },
      {
        id: 3,
        content: `3. 绑定规则：兑换码不绑定用户，需妥善保管，避免被盗用。`,
      },
    ],
  },
  {
    title: '三、权益生效说明',
    rules: [
      {
        id: 1,
        content:
          '无论兑换凤凰体验卡还是爱心卡，会员权益均从用户实际兑换日开始计算有效期，而非兑换码获取日期。',
      },
    ],
  },
  {
    title: '四、其他说明',
    rules: [
      {
        id: 1,
        content: '最终解释权：本兑换码相关规则的解释权归平台所有。',
      },
      {
        id: 2,
        content: '1. 请务必在2025年9月30日前完成兑换，避免因过期导致权益失效。',
      },
      {
        id: 3,
        content:
          '2. 由于兑换码不绑定用户，建议收到后尽快使用，或严格保密兑换码信息，防止他人盗用。',
      },
    ],
  },
];

interface RedeemPopupProps {
  visible: boolean;
  onClose: () => void;
  onRedeem: () => void;
}

/**
 * 兑换弹窗组件
 * 展示兑换商品的详情，包含商品图片、名称、库存状态和兑换按钮
 *
 * @param {object} props - 组件属性对象
 * @param {boolean} props.visible - 是否显示弹窗
 * @param {Function} props.onClose - 关闭弹窗时的回调函数
 * @returns {JSX.Element} 兑换弹窗组件
 */
const RedeemPopup = ({ visible, onClose, onRedeem }: RedeemPopupProps): JSX.Element => {
  return (
    <PageContainer
      show={visible}
      position="bottom"
      onBeforeLeave={onClose}
      round
      overlayStyle="background-color: rgba(0, 0, 0, 0.7);"
      className="page-container-class"
    >
      <View className="redeem-popup-content">
        <View className="redeem-popup-header">
          兑换规则详解
          <View className="close-btn" onClick={onClose}>
            ×
          </View>
        </View>
        <View className="redeem-popup-card">
          <View className="redeem-popup-card-image">
            <Image src="https://tianxing-bucket.oss-cn-hangzhou.aliyuncs.com/5%20%E4%BD%93%E9%AA%8C%E5%8D%A1.png" />
          </View>
          <View className="redeem-popup-card-title">凤凰体验卡</View>
        </View>
        <View className="redeem-popup-description">
          {redeemRulesData.map((section, sectionIndex) => (
            <View key={sectionIndex} className="section">
              <View className="section-title">{section.title}</View>
              {section.rules.map((rule) => (
                <View key={rule.id} className="rule-item">
                  <Text>{rule.content}</Text>
                </View>
              ))}
            </View>
          ))}
        </View>
        <View className="redeem-popup-footer">
          <Button type="primary" block className="redeem-popup-footer-btn" onClick={onRedeem}>
            确认兑换
          </Button>
        </View>
      </View>
    </PageContainer>
  );
};

export default RedeemPopup;
