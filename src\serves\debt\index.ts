import { serveIp } from '../config';
import http from '../http';
import {
  GetContractListRes,
  GetDebtMonthRes,
  GetMonthDebtDetailParams,
  GetMonthDebtDetailRes,
  GetRepaymentPlanRes,
} from './interface';

/**
 * 查询当月化债情况
 * @param {string} memberId
 * @returns
 */
export function getDebtMonth(memberId: number): Promise<GetDebtMonthRes> {
  return http.post(`${serveIp.ip}/app-api/crm/app/debt/getDebtMonth/${memberId}`, {});
}

/**
 * 根据会员id获取债务合同列表
 * @param {string} memberId
 * @returns
 */
export function getContractList(memberId: string): Promise<GetContractListRes> {
  return http.post(`${serveIp.ip}/app-api/crm/app/debt/getContractList/${memberId}`, {});
}

/**
 * 查询我的-化债明细
 * @param {string} memberId
 * @returns
 */

export function getMonthDebtDetail(
  params: GetMonthDebtDetailParams,
): Promise<GetMonthDebtDetailRes> {
  return http.post(`${serveIp.ip}/app-api/crm/app/debt/getMonthDebtDetail`, params);
}

/**
 * 根据合同id查询还款计划
 * @param {string} contractId
 * @returns
 */
export function getRepaymentPlan(contractId: string): Promise<GetRepaymentPlanRes> {
  return http.post(`${serveIp.ip}/app-api/crm/app/debt/getAppRepaymentPlan`, {
    pageNo: 1,
    pageSize: 100,
    contractId: contractId,
  });
}
