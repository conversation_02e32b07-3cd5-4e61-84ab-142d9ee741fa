// 直接搬运原有样式，保持不变
.all-tab-pane {
  &-loading {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-content {
    display: flex;
    flex-direction: column;
    gap: 28px;

    &-item {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 104px * 2;
      flex-shrink: 0;
      background-color: #fff;
      padding: 16px 32px 16px 32px;
      border-radius: 8px * 2;
      gap: 12px;

      .header,
      .header-left {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;

        &-icon {
          width: 16px * 2;
          height: 16px * 2;
          background-color: #ff4144;
          color: #fff;
          font-weight: 600;
          font-size: 10px * 2;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          &.help {
            background-color: #f99b57;
          }

          &.commission {
            background-color: #415feb;
          }
        }

        text:last-of-type {
          color: #666;
          font-size: 16px * 2;
        }
      }

      .header-date {
        color: #9d9d9d !important;
        font-size: 12px * 2 !important;
      }

      .content {
        flex: 1;
        display: flex;
        justify-content: space-between;
        border-top: 1px solid #e5e5e5;
        padding-top: 12px;
        align-items: center;

        &-left {
          width: 70%;
          display: flex;
          flex-direction: column;
          gap: 12px * 2;
          justify-content: space-between;

          &-title {
            width: 100%;
            color: #131313;
            font-size: 14px * 2;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &-order-number {
            color: #666;
            font-size: 12px * 2;
          }
        }

        &-price {
          color: #131313;
          font-size: 16px * 2;
          font-weight: 600;
        }
      }
    }
  }
}
