.editor-toolbar {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  padding: 20px 0;
  border-top: 2px solid #f5f5f5;

  // position: fixed;
  // bottom: env(safe-area-inset-bottom);
  // left: 0;
  // right: 0;
  // margin-bottom: env(safe-area-inset-bottom);
  width: 100%;
  z-index: 1000; /* 确保工具栏在最上层 */

  .toolbar-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}

.toolbar-item {
  flex: 1;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;

  &:active {
    background-color: #f0f0f0;
  }
}

.iconfont {
  font-size: 24px;
  color: #333;
}
