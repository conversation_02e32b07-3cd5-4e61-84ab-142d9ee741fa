import { serveIp } from '@/serves/config';
import http from '@/serves/http';
import { AppCardBenefitDTO, CommonResultListAppCardBenefitsVO } from './interface';

/**
 * 获取可升级会员卡及权益列表
 * @param {AppCardBenefitDTO} params 查询参数
 * @returns {Promise<CommonResultListAppCardBenefitsVO>} 会员卡权益列表
 */
export function getAppCardAndBenefits(
  params: AppCardBenefitDTO,
): Promise<CommonResultListAppCardBenefitsVO> {
  return http.post(`${serveIp.ip}/app-api/member/service/card-benefits`, params);
}
