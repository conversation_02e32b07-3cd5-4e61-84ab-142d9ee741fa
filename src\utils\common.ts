/**
 * 手机号码正则
 */
export const isPhoneNumber = (val: string) => {
  return /^1[3-9]\d{9}$/.test(val);
};

/**
 * 中文字符
 */
export const isChineseCharacters = (val: string) => {
  return /^[\u4e00-\u9fa5]+$/.test(val);
};

/**
 * 邮箱
 */
export const isEmailAddress = (val: string) => {
  return /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(val);
};

/**
 * 文件大小转换
 */
export const formatFileSize = (bytes: number, decimals = 2) => {
  if (bytes === 0 || !bytes) return '-';
  const k = 1024;
  const sizes = ['字节', '千', '兆', '吉', '太', '拍', '艾', '泽', '尧'];

  if (bytes < k) {
    // 对于小于1024的字节数，直接返回，不添加小数点
    return bytes.toString() + sizes[0];
  }
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const formattedNumber = (bytes / Math.pow(k, i)).toFixed(decimals);
  // 如果格式化后的数字末尾是.00，则移除小数点后的零
  const trimmedNumber = formattedNumber.replace(/\.00$/, '');
  return parseFloat(trimmedNumber).toString() + sizes[i];
};

/**
 * 获取指定位数的随机数
 */
export const getRandom = (n: number) => {
  return Math.floor((Math.random() + Math.floor(Math.random() * 9 + 1)) * Math.pow(10, n - 1));
};

/** 计算支付倒计时展示 */
export const calculateDiffTime = (startTime: number, endTime: number) => {
  if (startTime > endTime) return '';
  let runTime = (endTime - startTime) / 1000; // 秒级
  const day = Math.floor(runTime / 86400);
  runTime = runTime % 86400; // 时
  const hour =
    Math.floor(runTime / 3600).toString().length === 1
      ? '0' + Math.floor(runTime / 3600)
      : Math.floor(runTime / 3600);
  runTime = runTime % 3600;
  const minute =
    Math.floor(runTime / 60).toString().length === 1
      ? '0' + Math.floor(runTime / 60)
      : Math.floor(runTime / 60);
  runTime = runTime % 60;
  const second =
    Math.floor(runTime).toString().length === 1 ? '0' + Math.floor(runTime) : Math.floor(runTime);
  // 返回相差年数月数天数时分秒
  return day + '天' + hour + ':' + minute + ':' + second;
};
/**
 * 获取字典key value
 * @param dictType 字典类型
 */
type DictItem = {
  dictType: string;
  value: string;
  label: string;
  colorType?: string;
  cssClass?: string;
};

type Option = {
  value: string;
  label: string;
};

export function getOptionsByDictType(dictType: string, data: DictItem[]): Option[] {
  if (data.length === 0) {
    return [];
  }
  return data
    .filter((item) => item.dictType === dictType)
    .map(({ value, label }) => ({ value, label }));
}
export const getKeyValue = (
  data: { label: string; value: string | number }[] | DictItem[],
  value: string | number,
) => {
  console.log('获取字典数据', data, value);
  if (data.length === 0) {
    return [undefined, undefined];
  }
  const result = data.find((item) => item.value === value);
  return result ? [result.value, result.label] : [undefined, undefined];
};

/**
 * 格式化证件号码
 * @param code 证件号码
 * @returns (****后四位)
 */
export const formatCreditCode = (code: string) => {
  if (!code || code.length < 4) {
    return code;
  }
  return `(****${code.slice(-4)})`;
};
