.debt-plan-list {
  padding: 20px 32px;
  background-color: #fff;
  border-top-left-radius: 8px * 2;
  border-top-right-radius: 8px * 2;
  // flex: 1;
  height: 70px * 2 * 5; // item 70px * 7个
  overflow: hidden;

  &-more {
    height: fit-content;
    // overflow: auto;
  }

  &-header {
    color: #131313;
    font-size: 16px * 2;
    font-weight: 600;
    margin-bottom: 26px * 2;
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 11px * 2;
  }

  &-item {
    height: 70px * 2;
  }

  &-item-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 4px * 2;

    text:first-of-type {
      color: #131313;
      font-size: 14px * 2;
      font-weight: 600;
    }

    text:last-of-type {
      color: #666;
      font-size: 12px * 2;
      font-weight: 400;
    }
  }

  &-item-right {
    .top {
      display: flex;
      gap: 20px;
      align-items: center;

      text:first-of-type {
        color: #131313;
        font-size: 14px * 2;
        font-weight: 600;
      }

      .tag {
        // 默认已结清
        width: fit-content;
        font-size: 12px * 2 !important;
        // padding: 12px !important;
        height: 20px * 2;
        line-height: 20px * 2;
        text-align: center;
        background: rgba(75, 129, 238, 0.1);
        .nut-tag-text {
          color: #4b81ee !important;
        }

        // 待还款
        &.waiting {
          background: rgba(102, 102, 102, 0.1);
          .nut-tag-text {
            color: #666 !important;
          }
        }
        // 进行中
        &.doing {
          background: rgba(251, 160, 48, 0.1);
          .nut-tag-text {
            color: #fba030 !important;
          }
        }
        // 未结清
        &.pending {
          background: rgba(207, 48, 48, 0.1);
          .nut-tag-text {
            color: #cf3030 !important;
          }
        }
      }
    }

    .detail-container {
      width: 100%;
      margin-top: 10px;
      display: flex;
      gap: 10px * 2;

      .details {
        color: #666;
        font-size: 12px * 2;
        text-wrap: nowrap;
        width: 100%;
        overflow: hidden;
        display: block;
        text-overflow: ellipsis;
      }
    }
  }
}

.more-control {
  display: flex;
  align-items: center;
  gap: 20px;
  justify-content: center;
  background-color: #fff;
  height: 40px * 2;
  border-bottom-left-radius: 8px * 2;
  border-bottom-right-radius: 8px * 2;

  text:last-of-type {
    color: #131313;
    font-size: 12px * 2;
    font-weight: 400;
  }
}

.manual-btn {
  width: fit-content;
  height: 18px * 2;
  flex-shrink: 0;
  border-radius: 2px * 2;
  border: 1px solid #f00;

  color: #f00;
  font-size: 10px * 2;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 10px;
}
