import { serveIp } from '@/serves/config';
import http from '@/serves/http';
import {
  MemberCardActivationDetailsParams,
  MemberCardActivationDetailsResponse,
} from './interface';

/**
 * 获取会员卡开通详情
 * @param {MemberCardActivationDetailsParams} params 请求参数，包含会员卡编号
 * @returns {Promise<MemberCardActivationDetailsResponse>} 会员卡开通详情
 */
export function getMemberCardActivationDetails(
  params: MemberCardActivationDetailsParams,
): Promise<MemberCardActivationDetailsResponse> {
  return http.get(`${serveIp.ip}/app-api/member/card/userActivation/getActivationDetails`, params);
}
