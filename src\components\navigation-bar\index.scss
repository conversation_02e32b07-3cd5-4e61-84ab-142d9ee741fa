.custom-nav {
  width: calc(100vw - 24px);
  height: 48px;
  box-sizing: content-box;
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 36px;
  padding-left: 24px;
  position: relative;
  z-index: 1;
  text-align: center;
  justify-content: start;
  text-align: center;
  .navLogo {
    width: 70px;
    height: 70px;
    margin: 0 32px;
  }
  .Ret {
    position: absolute;
    left: 24px;
  }

  // 透明导航栏样式
  &.transparent {
    background-color: transparent;

    // 返回按钮和文字颜色设置为黑色，以便在深色背景上更易辨识
    .Ret {
      color: #000;
    }

    Text {
      color: #000;
    }
  }
}
