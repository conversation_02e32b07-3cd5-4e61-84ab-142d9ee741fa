import { Image, ScrollView, Swiper, SwiperItem, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect, useRef, useState } from 'react';
import { useEnv, useNavigationBar, useToast } from 'taro-hooks';
import NavigationBar from '../../components/navigation-bar';
import { start } from '../../serves';

import useLogin from '@/hooks/use-login';
import './index.scss';
const type = {
  TT: 44,
  WEAPP: 34,
};
const Index = () => {
  const env = useEnv();
  const { accessToken } = useLogin();
  const { setTitle } = useNavigationBar({ title: 'Taro Hooks' });
  const { show } = useToast();
  const flag = useRef(false);
  const [list] = useState([
    {
      key: 'SCET',
      value: '圈层运营官',
      color: '#FF9B44',
    },
    {
      key: 'SCEF',
      value: '迅捷捕猎手',
      color: '#B2E7ED',
    },
    {
      key: 'SCVT',
      value: '品传布道者',
      color: '#FFA970',
    },
    {
      key: 'SCVF',
      value: '内容造势官',
      color: '#F9B5C7',
    },
    {
      key: 'SPET',
      value: '生活筑构师',
      color: '#F9B4CC',
    },
    {
      key: 'SPEF',
      value: '危机洞察者',
      color: '#9BAFFA',
    },
    {
      key: 'SPVT',
      value: '人脉织网者',
      color: '#C2EFA3',
    },
    {
      key: 'SPVF',
      value: '刚需指挥官',
      color: '#95CBFE',
    },
    {
      key: 'NCET',
      value: '生态架构师',
      color: '#B2E7EB',
    },
    {
      key: 'NCEF',
      value: '商务操盘手',
      color: '#FDE573',
    },
    {
      key: 'NCVT',
      value: '心智造势者',
      color: '#FDA687',
    },
    {
      key: 'NCVF',
      value: '流量赋能师',
      color: '#9BAFFA',
    },
    {
      key: 'NPET',
      value: '洼地勘探者',
      color: '#F9B5C7',
    },
    {
      key: 'NPEF',
      value: '时空套利客',
      color: '#95CBFE',
    },
    {
      key: 'NPVT',
      value: '地推开拓者',
      color: '#FDE573',
    },
    {
      key: 'NPVF',
      value: '弱网联结者',
      color: '#7BBFFF',
    },
  ]);
  const [showMore, setShowMore] = useState<boolean>(false);
  const makephone = () => {
    Taro.makePhoneCall({
      phoneNumber: '400-0731-575',
      success(res) {
        // 调用成功 makePhoneCall:ok
        console.log('调用成功', res.errMsg);
      },
      fail(res) {
        // 调用失败 makePhoneCall:fail
        console.log('调用失败', res.errMsg);
      },
    });
  };
  useEffect(() => {
    if (flag.current && accessToken) {
      startQ();
    }
  }, [accessToken]);
  const startQ = async () => {
    if (accessToken) {
      const result: any = await start();
      console.log(result);
      if (result.data) {
        if (result.data.env) {
          Taro.navigateTo({
            url: '../qaSmart/index',
          });
        } else if (env !== 'TT') {
          Taro.navigateTo({
            url: '../qaSmart/index',
          });
        } else {
          show({ title: '请联系客服测评' });
        }
      }
    } else if (!flag.current) {
      flag.current = true;
      return false;
    }
  };
  const linkUserPrivacy = () => {
    Taro.navigateTo({
      url: '../userPrivacy/index',
    });
  };
  return (
    <View className="wrapper">
      {env !== 'TT' && <NavigationBar isRet align="center"></NavigationBar>}
      <ScrollView className="scrollarea" scroll-y type="list">
        <View className="container">
          <Swiper className="swiper" interval={2000} duration={500}>
            {[0].map(() => {
              return (
                <SwiperItem>
                  <View className="swiperItem">
                    <Image
                      src="https://guanjia.hunantianxing.com/static/miniprogramImage/u8_div.png"
                      mode="aspectFill"
                    />
                  </View>
                </SwiperItem>
              );
            })}
          </Swiper>
          <View className="main" onClick={startQ}>
            <View className="title">创收能力评测</View>
            <View className="sortList">
              <View className="sortItem">
                <View>精选专业问题</View>
              </View>
              <View className="sortItem">
                <Text></Text>
                <View>专业报告输出</View>
              </View>
            </View>
            <View className="testBut">
              <Text> {env === 'TT' ? '联系客服测评' : '立即测评'} </Text>
              <Image
                src="https://guanjia.hunantianxing.com/static/miniprogramImage/u26.png"
                mode="aspectFill"
              ></Image>
            </View>
          </View>
          <View className="footer">
            <View className="reportTitle">测评报告</View>
            <View className="footerContainer">
              <View className="introduceOne">
                <View className="moduleBox mb20">
                  <View className="introduceTitle">
                    <Image src="https://guanjia.hunantianxing.com/static/miniprogramImage/u68.png"></Image>
                    测试结论指导
                  </View>
                  <View className="testResult">
                    <View>
                      <Text>1</Text>
                      <Text>
                        <Text>了解自己，为未来创造更多机会！</Text>
                      </Text>
                    </View>
                    <View>
                      <Text>2</Text>
                      <Text>全面解析，共享优惠，实现共赢！</Text>
                    </View>
                    <View>
                      <Text>3</Text>
                      <Text>精打细算、积果资源两不误，轻松规划 生活更从容。</Text>
                    </View>
                  </View>
                </View>
                <View className="moduleBox mb20">
                  <View className="introduceTitle">
                    <Image src="https://guanjia.hunantianxing.com/static/miniprogramImage/u68.png"></Image>
                    湖南添星科技介绍
                  </View>
                  <View className="introduceContent mb">
                    湖南添星科技有限公司是一家专注于创新商业模到
                    内企业，通过打造“消费带动收益”模式，助力参与
                    当共享美好生活。我们致力于通过日常消费活动，为个
                    人与亲友创造更多价值，实现多方共赢!
                  </View>
                  <View className="connectBox">
                    <View className="connectUs">
                      <View onClick={makephone}>
                        <Image src="https://guanjia.hunantianxing.com/static/miniprogramImage/u385.png"></Image>
                        联系我们
                      </View>
                      <View className="connectPhone" onClick={makephone}>
                        <Text>客服电话 400-0731-575</Text>
                      </View>
                      <View className="Qrcode">
                        <View>
                          <Image
                            src="https://guanjia.hunantianxing.com/static/images/u88.png"
                            show-menu-by-longpress="true"
                          ></Image>
                        </View>
                        {env !== 'TT' && (
                          <View>
                            <Image
                              show-menu-by-longpress="true"
                              src="https://guanjia.hunantianxing.com/static/images/u89.png"
                            ></Image>
                          </View>
                        )}
                      </View>
                      <View className="QrcodeInfo">
                        <View>
                          <View>@添星科技</View>
                          <View>抖音号:44144005274</View>
                          <View>湖南添星科技官方账号</View>
                        </View>
                        {env !== 'TT' && (
                          <View>
                            <View>@添星商城</View>
                            <View>微信公众号:添星商城</View>
                            <View>湖南添星科技官方公众号</View>
                          </View>
                        )}
                      </View>
                    </View>
                  </View>
                </View>
                <View className="moduleBox mb20">
                  <View className="introduceTitle">
                    <Image src="https://guanjia.hunantianxing.com/static/miniprogramImage/tborg.png"></Image>
                    湖南添星科技SCVT社交价值策略分析体系
                  </View>
                  <View className="introduceContent">
                    SCVT智能评估模型，以社交行为（Social）、消费
                    性格（Character）、价值转化（Value）、策略弹性
                    （Tactics）​为核心维度，构建多维动态社交消费带动
                    收益策略体系，输出消费带动收益的策略方案，个人
                    通过拉动消费获得利润，逐步拆解未来规划方案。
                  </View>
                  <View className="moreBut" onClick={() => setShowMore(!showMore)}>
                    点击{showMore ? '收起' : '了解详情'}
                  </View>
                </View>
                <View className={`moreContent ${showMore ? 'showMoreContent' : ''}`}>
                  <View className="moduleBox mb20">
                    <View className="introduceTitle">
                      <Image src="https://guanjia.hunantianxing.com/static/miniprogramImage/tborg.png"></Image>
                      SCVT的逻辑体系
                    </View>
                    <View className="introduceContentBox">
                      <View className="introduceContentItem">
                        <View>
                          <Text>S深度社交</Text>
                          <View>
                            注重维护强关系，社交稳 定性高。长期维系核心好 友、偏好私域社群
                          </View>
                        </View>
                        <View>
                          <Text>N广度社交</Text>
                          <View>适应开放性平台，快速建 立新人脉、活跃于公开社 交场景</View>
                        </View>
                        <View>
                          <View>
                            <Text>社交行为</Text>
                            <View>(Social)</View>
                          </View>
                        </View>
                      </View>
                      <View className="introduceContentItem">
                        <View>
                          <Text>C品质导向</Text>
                          <View>
                            长期体验产品营销，小众 品牌、会员服务销售决策 周期长但忠诚度高
                          </View>
                        </View>
                        <View>
                          <Text>P实用导向</Text>
                          <View>
                            倾向理性对比, 折扣信息、 销售高复购率的基码商品， 易推广促销活动
                          </View>
                        </View>
                        <View>
                          <View>
                            <Text>消费性格</Text>
                            <View>(Character)</View>
                          </View>
                        </View>
                      </View>
                      <View className="introduceContentItem">
                        <View>
                          <Text>E深度服务</Text>
                          <View>倾向于定制服务、1v1咨 询建立信任，适应应对高 投入决策场晕。</View>
                        </View>
                        <View>
                          <Text>V联动共享</Text>
                          <View>依托用户分享与社交网络 自然延伸，实现精准触达写消费热情增长</View>
                        </View>
                        <View>
                          <View>
                            <Text>价值转化</Text>
                            <View>(Value)</View>
                          </View>
                        </View>
                      </View>
                      <View className="introduceContentItem">
                        <View>
                          <Text>T策略规划</Text>
                          <View>制定详细计划、依赖数据 分析、重视目标拆解、风 险规避倾向</View>
                        </View>
                        <View>
                          <Text>F灵活响应</Text>
                          <View>利用社交链扩散，趋向于 在社交成交、内容传播变 现。</View>
                        </View>
                        <View>
                          <View>
                            <Text>策略弹性</Text>
                            <View>(Tactics)</View>
                          </View>
                        </View>
                      </View>
                    </View>
                    <View className="introduceContent mb">
                      <Text className="color888888">
                        这4个维度，评测倾向哪个指标，就代表具备对应的能力、性格。根据这4个维度测试的倾向值，
                      </Text>
                      SCVT模型将用户划分成16种结果，不同的结果有着对应的能力
                    </View>
                    <View className="SCVTList">
                      {list.map((item, index) => {
                        return (
                          <View
                            className="SCVTItem"
                            style={{ backgroundColor: item.color }}
                            key={item.key + 'SCVTItem'}
                          >
                            <Image
                              src={`https://guanjia.hunantianxing.com/static/images/${index + 1}.png`}
                            ></Image>
                            <Text>{item.key}</Text>
                            <View>{item.value}</View>
                          </View>
                        );
                      })}
                    </View>
                  </View>
                  <View className="moduleBox mb20">
                    <View className="introduceTitle"></View>
                    <View className="tagBox">
                      数据穿透性<View></View>
                    </View>
                    <View className="pBox">
                      ​<View>社交资本（S）​：</View>
                      <Text>
                        量化强关系信任密度（如私域社群活跃度）与弱关系传播效能（如粉丝互动率）。
                      </Text>
                    </View>
                    <View className="pBox mb">
                      <View>​消费性格（C）​：</View>
                      <Text>
                        解析用户消费决策模式，区分「品质驱动型」（奢侈品复购率）与「理性防御型」（折扣敏感度）。
                      </Text>
                    </View>
                    <View className="tagBox">
                      策略预判力<View></View>
                    </View>
                    <View className="pBox">
                      ​<View>​价值转化（V）​：</View>
                      <Text>
                        测算社交裂变效率（如分销任务完成度）与资源调配能力（如跨城调货响应速度）。
                      </Text>
                    </View>
                    <View className="pBox">
                      <View>​弹性策略（T）​：</View>
                      <Text>
                        评估危机响应时效（如48小时闪购参与度）及平台兜底系数（资产与风险对冲值）。
                      </Text>
                    </View>
                  </View>
                </View>
                <View className="moduleBox mb20">
                  <View className="introduceTitle">
                    <Image src="https://guanjia.hunantianxing.com/static/miniprogramImage/u69.png"></Image>
                    服务协议、隐私政策
                  </View>
                  <View className="Privacy" onClick={linkUserPrivacy}>
                    《<Text>用户服务协议与隐私</Text>》
                  </View>
                </View>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default Index;
