import { AppPlanByMonthRes } from '@/serves/planByMonth/interface';
import { formatDate } from '@/utils/date';
import { CalendarCard, CalendarCardRef } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import { memo, useCallback, useEffect, useRef, useState } from 'react';
import './index.scss';

type CalendarCardDay = {
  type: 'prev' | 'current' | 'next';
  year: number;
  month: number;
  date: number;
};

interface WorkName extends AppPlanByMonthRes {
  name: string;
  type: string;
  day: number;
}

interface CalendarCardItemProps {
  date: Date;
  onDayClick: (day: string) => void;
  workNames: WorkName[];
}

const CalendarCardItem = ({ date, onDayClick, workNames }: CalendarCardItemProps) => {
  const CalendarCardRef = useRef<CalendarCardRef>(null);
  const [activeDay, setActiveDay] = useState<number>(new Date().getDate());

  useEffect(() => {
    CalendarCardRef.current?.jumpTo(date.getFullYear(), date.getMonth() + 1);
  }, [date]);

  const renderDay = (day: CalendarCardDay) => {
    if (day.type === 'current') {
      const dayItem = new Date(day.year, day.month - 1, day.date);
      const today = new Date().getDate();
      const now_day = dayItem.getDay();
      const isWeekend = now_day === 0 || now_day === 6 ? 'day-weekend' : '';
      const isActive = day.date === activeDay ? 'active' : '';

      // const isPreDay = day.date
      let name = 'day';
      if (day.date === today) {
        name = 'today';
      }
      return (
        <Text className={`day day-${name} ${isWeekend} ${isActive}`}>
          {day.date <= 9 ? `0${day.date}` : day.date}
        </Text>
      );
    } else {
      return null;
    }
  };

  const renderDayBottom = useCallback(
    (day: CalendarCardDay) => {
      const date = day.date;
      const todoItems = workNames.filter((item) => item.day === date);

      return (
        <View className={`day-todo-list `}>
          {todoItems.map((item, index) => {
            // 开始时间小于今天的零点
            const isExpired =
              new Date(item.startTime).getTime() <
              new Date(new Date().setHours(0, 0, 0, 0)).getTime();
            // console.log('isExpired', item.startTime, item.name, isExpired);
            return (
              <View key={index} className={`todo-item ${isExpired ? 'not-current-day' : ''}`}>
                {item.name}
              </View>
            );
          })}
        </View>
      );
    },
    [workNames],
  );

  const handleClick = (day: CalendarCardDay) => {
    const activeDay = formatDate(new Date(day.year, day.month - 1, day.date));
    setActiveDay(day.date);
    onDayClick(activeDay);
  };

  return (
    <CalendarCard
      ref={CalendarCardRef}
      className="calendar"
      renderDay={renderDay}
      onDayClick={handleClick}
      renderDayBottom={renderDayBottom}
    />
  );
};

export default memo(CalendarCardItem);
