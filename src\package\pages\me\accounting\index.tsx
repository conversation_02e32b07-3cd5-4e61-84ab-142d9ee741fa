import { View } from '@tarojs/components';

import PageContainerTmp from '@/components/pageContainerTemp';
import useRequestWithLifecycle from '@/hooks/use-request-with-lifecycle';
import { getPageListOfAccounting } from '@/serves/accounting';
import { useCallback, useState } from 'react';
import './index.scss';
import InfoCard from './InfoCard';
import TabsList from './TabsList';

// 定义 tab 值和 status 的映射关系
const statusMap = {
  '0': '00', // 待沟通
  '1': '02', // 跟进
  '2': '01', // 已完成
  '3': '', // 全部
};

const Accounting = () => {
  const [tabValue, setTabValue] = useState<string | number>('0');

  const { data } = useRequestWithLifecycle(
    () => getPageListOfAccounting({ status: statusMap[tabValue] }),
    {
      // refreshOnWindowFocus: true,
      refreshOnShow: true,
      refreshDeps: [tabValue],
      // ready: statusMap[tabValue] !== undefined,
    },
  );

  const handleTabChange = useCallback((value: string | number) => {
    setTabValue(value);
  }, []);

  return (
    <PageContainerTmp
      marksType="deep-pink"
      title="账管顾问"
      showTabbar={false}
      isRet
      align="center"
    >
      <View className="accounting-page">
        <InfoCard customerCount={data?.data?.length || 0} serviceArea={''} />
        <TabsList list={data?.data || []} value={tabValue} onChange={handleTabChange} />
      </View>
    </PageContainerTmp>
  );
};

export default Accounting;
