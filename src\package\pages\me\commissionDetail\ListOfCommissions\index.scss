.my-debt-container {
  width: 100%;
  height: 100%;

  .debt-info-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    // height: 146px * 2;
    gap: 28px;

    .debt-info-top {
      display: flex;

      .debt-info-top-item1,
      .debt-info-top-item2 {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;

        text:first-of-type {
          color: #333;
          font-size: 28px;
        }

        text:last-of-type {
          color: #f00;
          font-weight: 600;
          font-size: 44px;
        }
      }

      .debt-info-top-item2 {
        text:last-of-type {
          color: #131313;
          font-weight: 600;
          font-size: 44px;
        }
      }
    }

    .debt-info-bottom {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      border-radius: 8px;
      border: 1px solid #fff;
      background: rgba(255, 246, 249, 0.55);
      align-items: center;
      padding: 32px 20px;
      height: 70px * 2;
      // justify-content: start;

      .debt-info-bottom-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;

        text:first-of-type {
          color: #000;
          font-size: 28px;
          font-weight: 550;
        }

        text:last-of-type {
          color: #666;
          font-size: 24px;
          font-weight: 400;
        }
      }
    }
  }

  .debt-list-container {
    display: flex;
    flex-direction: column;
    overflow: auto;
    gap: 28px;
    // height: 500px * 2;
    // min-height: 400px * 2;
    // max-height: 500px * 2;

    .debt-list-item {
      // margin: 20px 0;
      display: flex;
      flex-direction: column;
      padding: 20px 32px;
      border-radius: 8px * 2;
      background: #fff;
      // height: 103px * 2;
      box-sizing: border-box;

      // gap: 24px;

      &-header {
        // display: flex;
        display: flex;
        // grid-template-columns: 1fr 100px 24px;
        align-items: center;
        // gap: 12px;
        height: 40px * 2;
        border-bottom: 2px solid #f5f6fa;

        &-item {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .header-item-icon {
          padding: 2px 12px;
          border-radius: 4px;
          background: rgba(251, 160, 48, 0.1);
          color: #fba030;
          font-size: 12px * 2;
          width: fit-content;
          margin-left: 38px * 2;
        }

        .title {
          color: #131313;
          font-size: 16px * 2;
          font-weight: 550;
        }

        .arrow-right {
          margin-left: auto;
        }
      }

      &-content {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        // gap: 12px;
        flex: 1;

        view {
          display: flex;
          flex-direction: column;
          padding: 20px 0px;

          text:first-of-type {
            color: #131313;
            font-size: 14px * 2;
            font-weight: 550;
          }

          text:last-of-type {
            color: #666;
            font-size: 14px * 2;
            font-weight: 400;
          }
        }
      }

      &:last-of-type,
      &:first-of-type {
        margin-bottom: 0;
      }
    }
  }

  #debt-tag {
    color: #fff;
    font-size: 12px * 2;
    background-color: #f56366;
    // width: fit-content;
    width: 44px * 2;
    // padding: 4px 8px;
    height: 20px * 2;
    text-align: center;
    line-height: 20px * 2;
    // border-radius: 4px;
  }
}
