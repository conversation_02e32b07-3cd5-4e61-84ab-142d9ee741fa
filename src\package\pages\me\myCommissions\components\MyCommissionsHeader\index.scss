.my-commissions-header {
  display: flex;
  flex-direction: column;
  padding: 18px 32px;
  gap: 16px;
  border-radius: 8px * 2;
  border: 2px solid #fff;
  background: rgba(255, 255, 255, 0.91);

  .my-commissions-header-item {
    display: flex;
    flex-direction: column;
    gap: 4px;

    &-title {
      display: flex;
      gap: 16px;
      align-items: center;
      height: 22px * 2;
    }

    .icon-container {
      display: flex;
      align-items: center;
      color: #f00;
      font-size: 12px * 2;
      font-weight: 400;
      gap: 4px;
      padding: 4px 12px;
      border-radius: 9px * 2;
      border: 2px solid rgba(255, 0, 0, 0.33);
      flex-shrink: 0;
    }

    &-value {
      color: #131313;
      font-size: 22px * 2;
      font-weight: 600;
    }
  }

  .my-commissions-header-tx {
    display: flex;
    gap: 4px;
    padding: 12px 20px;
    border-radius: 15px * 2;
    align-items: center;
    background: #f7f7f7;
    width: fit-content;

    &-title {
      color: #9d9d9d;
      font-size: 13px * 2;
      font-weight: 400;
    }

    &-value {
      color: #131313;
      font-size: 12px * 2;
      font-weight: 500;
      margin-right: 16px;
    }

    &-btn {
      display: flex;
      gap: 4px;
      color: #131313;
      font-size: 12px * 2;
      font-weight: 400;
      align-items: center;
    }
  }

  .my-commissions-header-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);

    &-item {
      display: flex;
      flex-direction: column;
      padding: 8px 42px 18px 0px;
      gap: 4px;

      &-title {
        color: #666;
        font-size: 12px * 2;
        font-weight: 400;
      }

      &-value {
        color: #131313;
        font-size: 16px * 2;
        font-weight: 600;
      }
    }
  }
}
