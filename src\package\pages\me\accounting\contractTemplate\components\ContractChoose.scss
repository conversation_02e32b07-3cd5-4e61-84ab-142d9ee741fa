.nut-cell-group-wrap {
  .nut-cell-divider:nth-child(2) {
    display: none !important;
  }

  .nut-cell-divider {
    opacity: 0.4;
  }
}

.contract-choose-item-title {
  color: #131313;
  font-size: 16px * 2;
  font-weight: 600;
}

.contract-choose-item {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    color: #666;
    font-size: 14px * 2;
    font-weight: 400;
  }
  .extra {
    display: flex;
    align-items: center;
    gap: 4px * 2;
    width: auto !important;

    .extra-text {
      color: #9d9d9d;
      text-align: right;
      font-size: 14px * 2;
      font-weight: 400;
      transition: all 0.3s ease;
      &.active {
        color: #131313;
        font-size: 14px * 2;
        font-weight: 400;
      }
    }
  }
}
