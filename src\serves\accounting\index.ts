/**
 * 账管顾问
 */

import { serveIp } from '../config';
import http from '../http';
import {
  GetDebtorIdCollecParams,
  GetDebtorIdCollecRes,
  GetPageListParams,
  GetPageListRes,
} from './interface';

/**
 * 获取客户列表
 * @param {object} params 用户APP - 账管顾问 - 查询DTO
 * @param {string} params.status 状态 “00”：待沟通；“01”：已完成
 * @param {number} params.debtorId 债务人id
 * @returns
 */
export function getPageListOfAccounting(params: GetPageListParams): Promise<GetPageListRes> {
  return http.post(`${serveIp.ip}/app-api/crm/business/list`, params);
}

/**
 * 获得债务人所有委案信息
 * @param {object} params 用户APP - 账管顾问 - 查询DTO
 * @param {string} params.status 状态 “00”：待沟通；“01”：已完成
 * @param {number} params.debtorId 债务人id
 * @returns
 */
export function getDebtorIdCollec(params: GetDebtorIdCollecParams): Promise<GetDebtorIdCollecRes> {
  return http.post(`${serveIp.ip}/app-api/crm/business/debtor`, params);
}
