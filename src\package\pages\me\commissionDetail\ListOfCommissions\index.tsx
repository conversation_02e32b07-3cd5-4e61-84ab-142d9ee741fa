import EmptyContainer from '@/components/empty-container';
import SvgIcon from '@/pages/me/components/SvgIcon';
import { GetDebtorIdCollecRes } from '@/serves/accounting/interface';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { memo, useCallback, useMemo } from 'react';
import './index.scss';

const ListOfCommissions = ({
  data,
}: {
  data: GetDebtorIdCollecRes['data']['debtorCaseEntrustDetailVOList'];
}) => {
  if (data.length === 0) {
    return <EmptyContainer title="暂无委案列表" />;
  }

  // 根据status字段确定跳转路径
  // detail路径: end_approval状态
  // planDetail路径: in_performance和end状态
  // installment路径: 其他所有状态
  const statusMap = useMemo(
    () => ({
      installment: [],
      detail: ['end_approval'],
      planDetail: ['in_performance', 'end'],
    }),
    [],
  );

  // 根据状态获取对应的页面路径
  const getPagePathByStatus = useCallback(
    (status: string) => {
      if (statusMap.detail.includes(status)) {
        return 'detail'; // 跳转到deep页面（只查看） 待审核
      } else if (statusMap.planDetail.includes(status)) {
        return 'planDetail'; // 跳转到plan页面的详情 已完成
      } else {
        return 'installment'; // 分期添加 待分期
      }
    },
    [statusMap],
  );

  return (
    <View className="my-debt-container">
      <View className="debt-list-container">
        {data.map((item, index) => (
          <View
            key={index}
            className="debt-list-item"
            onClick={() => {
              const signStatus = getPagePathByStatus(item.status);
              if (signStatus === 'planDetail') {
                Taro.navigateTo({
                  url: `/packageB/pages/debtDetail/index?caseEntrustId=${item.id}`,
                });
              } else {
                // 根据状态确定跳转路径
                Taro.navigateTo({
                  url: `/package/pages/me/commissionDetailDeep/index`,
                  success: function (res) {
                    console.log('in_approval', signStatus);
                    // 通过eventChannel向被打开页面传送数据
                    res.eventChannel.emit('acceptDataFromCommissionListPage', {
                      data: {
                        ...item,
                        signStatus,
                      },
                    });
                  },
                });
              }
            }}
          >
            <View className="debt-list-item-header">
              <View className="debt-list-item-header-item">
                <SvgIcon name="bankCard" size={16}></SvgIcon>
                <Text className="title">{item.creditorName}</Text>
              </View>
              {getPagePathByStatus(item.status) === 'detail' && (
                <View className="header-item-icon">待签署</View>
              )}
              <SvgIcon className="arrow-right" name="arrow-right-small" size={12}></SvgIcon>
            </View>
            {/* <View className="dividing"></View> */}
            <View className="debt-list-item-content">
              <View>
                <Text>{item.entrustedAmount || '-'}</Text>
                <Text>债务总额(元)</Text>
              </View>
              <View>
                <Text>{item.caseStage || '-'}</Text>
                <Text>逾期阶段</Text>
              </View>
              <View>
                <Text>{item.interest || '-'}</Text>
                <Text>利息/罚金</Text>
              </View>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

export default memo(ListOfCommissions);
