import {
  formatMoneyByScene,
  getPriceProps,
  MoneyDisplayScene,
  MoneyFormatOptions,
} from '@/utils/money';
import { Price } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import { FC } from 'react';
import './index.scss';
// 文档路径：src/docs/money-display-guide.md

/**
 * 金额显示组件属性
 */
export interface MoneyDisplayProps {
  /** 金额数值 */
  amount: number | string | null | undefined;
  /** 显示场景 */
  scene?: MoneyDisplayScene;
  /** 自定义格式化选项 */
  options?: Partial<MoneyFormatOptions>;
  /** 是否使用 NutUI Price 组件，默认为 true */
  usePrice?: boolean;
  /** Price 组件的额外属性 */
  priceProps?: Record<string, any>;
  /** 自定义样式类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 点击事件 */
  onClick?: () => void;
}

/**
 * 金额显示组件
 * 统一处理金额的显示格式和样式
 */
const MoneyDisplay: FC<MoneyDisplayProps> = ({
  amount,
  scene = MoneyDisplayScene.LIST,
  options = {},
  usePrice = true,
  priceProps = {},
  className = '',
  style,
  onClick,
}) => {
  // 如果使用 Price 组件
  if (usePrice) {
    const priceConfig = getPriceProps(amount, {
      ...options,
    });

    return (
      <Price
        {...priceConfig}
        {...priceProps}
        className={`money-display ${className}`}
        style={style}
      />
    );
  }

  // 使用文本显示
  const formattedMoney = formatMoneyByScene(amount, scene, options);

  return (
    <View
      className={`money-display money-display--text ${className}`}
      style={style}
      onClick={onClick}
    >
      {formattedMoney}
    </View>
  );
};

export default MoneyDisplay;

// 导出便捷组件
export const MoneyDisplayList: FC<Omit<MoneyDisplayProps, 'scene'>> = (props) => (
  <MoneyDisplay {...props} scene={MoneyDisplayScene.LIST} />
);

export const MoneyDisplayDetail: FC<Omit<MoneyDisplayProps, 'scene'>> = (props) => (
  <MoneyDisplay {...props} scene={MoneyDisplayScene.DETAIL} />
);

export const MoneyDisplayCard: FC<Omit<MoneyDisplayProps, 'scene'>> = (props) => (
  <MoneyDisplay {...props} scene={MoneyDisplayScene.CARD} />
);

export const MoneyDisplayForm: FC<Omit<MoneyDisplayProps, 'scene'>> = (props) => (
  <MoneyDisplay {...props} scene={MoneyDisplayScene.FORM} />
);
