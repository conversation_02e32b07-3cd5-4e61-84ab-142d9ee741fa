import NavigationBar from '@/components/navigation-bar';
import CustomTabBar from '@/components/tabbar';
import useTabBar from '@/hooks/use-tab-bar';

import GoodsList from '@/components/goodsList';

import { homepageBanner, svgStore } from '@/utils/svgLoader';
import { ArrowRight, Search } from '@nutui/icons-react-taro';
import { <PERSON>ge, Button, Price, Swiper } from '@nutui/nutui-react-taro';
import { Image, Text, View } from '@tarojs/components';

import Taro, { useDidShow } from '@tarojs/taro';

import usePlanByMonth from '@/hooks/use-plan-by-month';
import { getDebtMonth } from '@/serves/debt';
import useUserStore from '@/store/useUserStore';
import { formatDate } from '@/utils/date';
import { useEnv, useRequest } from 'taro-hooks';
import './index.scss';
const Index = () => {
  const env = useEnv();
  const { userInfo } = useUserStore((state) => state);
  useTabBar(0);
  const { data, refresh } = useRequest(() => getDebtMonth(userInfo?.userId), {
    cacheKey: 'debtMonth',
    refreshDeps: [userInfo?.userId],
    ready: !!userInfo?.userId,
  });

  useDidShow(() => {
    refresh();
  });

  const { currentMonthWorkNames: monthPlan } = usePlanByMonth({
    date: formatDate(new Date(), { template: 'YYYY-MM' }),
  });

  const handleGoToWorkPlan = (id: string) => {
    Taro.navigateTo({
      // 跳转到日程页面，并传递当前日期
      url: `/package/pages/newTodo/TodoDetail/index?id=${id}`,
    });
  };

  return (
    <View className="tabbarContainer">
      <View className="wrapper">
        {env !== 'TT' && <NavigationBar></NavigationBar>}
        <View className="marks">
          <View className="markright"></View>
          <View className="markleft"></View>
        </View>
        <View className="main">
          <View
            className="searchbox"
            onClick={() => {
              console.log('点击了搜索');
            }}
          >
            <Search size="12" style={{ marginRight: '20rpx' }} />
            搜索世界更精彩
          </View>
          <Swiper defaultValue={0} indicator autoplay height={100}>
            {homepageBanner.map((item) => (
              <Swiper.Item key={item}>
                <Image
                  style={{ width: '100%', height: '100%', borderRadius: 8 }}
                  mode="aspectFill"
                  src={item}
                />
              </Swiper.Item>
            ))}
          </Swiper>
          <view className="contentPrice">
            <view className="contentPrice_title">
              <view className="contentPrice_item">
                <Price price={data?.data?.remainingDebtReductionAmount ?? 0} symbol="" digits={2} />
                <view className="contentPrice_item_title">剩余化债(元)</view>
              </view>
              <view className="contentPrice_item">
                <Price
                  price={data?.data?.debtReductionAmount ?? 0}
                  symbol=""
                  digits={2}
                  color="darkgray"
                />
                <view className="contentPrice_item_title">已化债(元)</view>
              </view>
            </view>
            <Button
              type="primary"
              fill="outline"
              onClick={() => {
                Taro.switchTab({
                  url: '/pages/plan/index',
                });
              }}
            >
              查看
            </Button>
          </view>
          <view className="task">
            <view className="task_header">
              <view className="task_header_title">
                <Image src={svgStore['rwzd']}></Image>
              </view>
              <view
                className="task_header_more"
                onClick={() => {
                  Taro.navigateTo({
                    url: '/pages/me-work-plan/index',
                  });
                }}
              >
                {monthPlan?.length ?? 0}条任务
                <Badge style={{ margin: '0 20rpx' }} value={0} />
                <ArrowRight size={12} />
              </view>
            </view>
            <view className="task_content">
              {/* 最后三条数据 */}
              {monthPlan?.slice(-3)?.map((item) => (
                <view className="task_content_box" key={item.id}>
                  <view className="task_content_item">
                    <view className="task_content_item_title">{item.workName}</view>
                    <view className="task_content_item_desc">{item.workContent}</view>
                  </view>
                  <Button
                    type="primary"
                    fill="outline"
                    onClick={() => handleGoToWorkPlan(item.id.toString())}
                  >
                    去完成
                  </Button>
                </view>
              ))}
              {/* <view className="task_content_box">
                <view className="task_content_item">
                  <view className="task_content_item_title">分享一次小程序</view>
                  <view className="task_content_item_desc">分享一次添星管家小程序至朋友圈</view>
                </view>
                <Button type="primary" fill="outline">
                  去完成
                </Button>
              </view>
              <view className="task_content_box">
                <view className="task_content_item">
                  <view className="task_content_item_title">今日总结</view>
                  <view className="task_content_item_desc">完成今日总结</view>
                </view>
                <Button type="default" fill="outline" disabled>
                  已完成
                </Button>
              </view> */}
            </view>
          </view>
          <View className="activity">
            <View className="activityItem">
              <View className="activityItem_content">
                <View className="activityItem_title">活动中心</View>
                <View className="activityItem_desc">经常活动等你参加</View>
                <View className="activityItem_btn"></View>
              </View>
              <View className="activityItem_img">
                <Image
                  src={svgStore['hdzx']}
                  mode="aspectFill"
                  style={{ width: '100%', height: '100%' }}
                ></Image>
              </View>
            </View>
            <View className="activityItem">
              <View className="activityItem_content">
                <View className="activityItem_title">分享赚钱</View>
                <View className="activityItem_desc">一键签约现金返佣</View>
                <View className="activityItem_btn"></View>
              </View>
              <View className="activityItem_img">
                <Image
                  src={svgStore['fxzq']}
                  mode="aspectFill"
                  style={{ width: '100%', height: '100%' }}
                ></Image>
              </View>
            </View>
          </View>
          <View className="recommend">
            <View className="recommend_title">
              <View>精品推荐</View>
              <View
                className="linkBut"
                onClick={() => {
                  Taro.navigateToMiniProgram({
                    appId: 'wx445f7d3555ef57a7',
                    path: 'pages/guide/index',
                    success: function (res) {
                      // 打开成功
                    },
                  });
                }}
              >
                <Text>去商场</Text>
                <ArrowRight size={14} />
              </View>
            </View>
            <GoodsList />
          </View>
          {/* <Drag direction="y" style={{ bottom: '48px', right: '0px' }}>
            <HoverButton>
              <SvgIcon
                name="note"
                size={47}
                onClick={() => {
                  console.log('点击了记事本');
                  Taro.navigateTo({
                    url: '/package/pages/notebook/index',
                  });
                }}
              />
            </HoverButton>
          </Drag> */}
        </View>
      </View>
      <CustomTabBar></CustomTabBar>
    </View>
  );
};

export default Index;
