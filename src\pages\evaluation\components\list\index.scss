// .tabs-of-list {
//   --nutui-tabs-tabpane-backgroundColor: transparent;
// }

.ev-item {
  margin: 28px 0;
  width: 100%;
  height: 93px * 2;
  flex-shrink: 0;
  border-radius: 16px;
  background: #fff;
  border: 1px solid #f5f5f5;
  // margin: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px 42px 32px;
  overflow: hidden;
  // box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15);

  &-left {
    display: flex;
    flex-direction: column;
    width: 215px * 2;
    gap: 12px;

    .title {
      color: #131313;
      font-size: 32px;
      font-weight: 600;
    }

    .desc {
      color: #666;
      font-size: 28px;
    }
  }

  &:first-child {
    margin: 0px;
  }
}
