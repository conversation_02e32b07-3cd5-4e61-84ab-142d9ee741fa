import { View } from '@tarojs/components';

import PageContainerTmp from '@/components/pageContainerTemp';
import useRequestWithLifecycle from '@/hooks/use-request-with-lifecycle';
import SvgIcon from '@/pages/me/components/SvgIcon';
import { getDebtorIdCollec } from '@/serves/accounting';
import { AreaCascaderKey } from '@/utils/area';
import { formatCreditCode } from '@/utils/common';
import { useRouter } from 'taro-hooks';
import CommissionDetailInfo from './CommissionDetailInfo';
import './index.scss';
import ListOfCommissions from './ListOfCommissions';
const CommissionDetail = () => {
  const [routerInfo] = useRouter();
  const { debtorId } = routerInfo.params;
  const { data } = useRequestWithLifecycle(() => getDebtorIdCollec({ debtorId: debtorId }), {
    refreshDeps: [debtorId],
    ready: !!debtorId,
    refreshOnShow: true,
  });

  const addressArr = JSON.parse(data?.data?.debtorOfficeAddress ?? '[]');
  const address = addressArr
    .map((item: string) => {
      return AreaCascaderKey[item];
    })
    .join('');

  return (
    <PageContainerTmp title="委案详情" showTabbar={false} isRet align="center">
      <View className="commission-detail-page">
        <View className="commission-detail-info">
          <View className="commission-detail-info-name">
            {data?.data?.debtorName ? `${data?.data?.debtorName}的委案` : ''}
          </View>
          <CommissionDetailInfo
            label="身份证号"
            icon={<SvgIcon name="icon_sfzh" size={14} />}
            value={formatCreditCode(data?.data?.debtorCreditCode ?? '')}
          />
          <CommissionDetailInfo
            label="联系电话"
            icon={<SvgIcon name="icon_TEL" size={14} />}
            value={data?.data?.debtorContactPhone ?? ''}
            desc="点击手机号拨打电话"
            valueLine
          />

          <CommissionDetailInfo
            label="地址"
            icon={<SvgIcon name="icon_dz" size={14} />}
            value={address}
          />
        </View>
        <ListOfCommissions data={data?.data?.debtorCaseEntrustDetailVOList ?? []} />
      </View>
    </PageContainerTmp>
  );
};

export default CommissionDetail;
