import SvgIcon from '@/pages/me/components/SvgIcon';
import './index.scss';

/**
 * 箭头图标组件
 * 提供一个可以控制方向的箭头图标，可以向上或向下显示
 * 
 * @param {object} props - 组件属性
 * @param {number} props.size - 箭头图标的大小
 * @param {boolean} props.up - 箭头朝向，true为向上，false为向下
 * @returns {JSX.Element} 箭头图标组件
 */
const Arrow = ({ size, up }: { size: number; up: boolean }) => {
  // 使用SvgIcon组件渲染箭头，并通过className控制方向
  return <SvgIcon name={'arrow-right-small'} size={size} className={up ? 'up' : 'down'}></SvgIcon>;
};

export default Arrow;
