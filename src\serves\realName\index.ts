import { serveIp } from '../config';
import http from "../http";
import { SendCodeParams, SendCodeRes, VerifyParams, VerifyRes } from "./interface";

/** 
 * 创建短信验证码，并进行发送
 * @param {object} params 管理后台 - 短信验证码发送 Request VO
 * @param {string} params.mobile 手机号
 * @param {number} params.scene 发送场景
 * @returns
 */
export function sendCode(params: SendCodeParams): Promise<SendCodeRes> {
  return http.post(`${serveIp.ip}/app-api/system/sms/code/send`, params)
}

/** 
 * 实名认证
 * @param {object} params 三要素验证请求体
 * @param {string} params.name 用户姓名
 * @param {string} params.idNo 身份证号码
 * @param {string} params.mobile 手机号码
 * @param {string} params.code 验证码
 * @returns
 */
export function verify(params: VerifyParams): Promise<VerifyRes> {
  return http.post(`${serveIp.ip}/app-api/system/verify/identity`, params);
}