{"miniprogramRoot": "dist/", "projectname": "miniprograms", "description": "添星管家", "appid": "wx368b88ba5087bc8c", "setting": {"urlCheck": false, "bigPackageSizeSupport": true, "es6": false, "enhance": false, "compileHotReLoad": false, "postcss": false, "preloadBackgroundData": false, "minified": false, "newFeature": true, "autoAudits": false, "coverView": true, "showShadowRootInWxmlPanel": false, "scopeDataCheck": false, "useCompilerModule": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "libVersion": "3.7.10", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}}