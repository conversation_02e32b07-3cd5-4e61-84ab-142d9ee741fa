import LoadingPage from '@/components/loadingPage';
import { getMemberGroupInfo } from '@/serves/member/myTeam';
import { MyInviterInfo } from '@/serves/member/myTeam/interface';
import useUserStore from '@/store/useUserStore';
import { svgStore } from '@/utils/svgLoader';
import { Image } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import { useDidShow } from '@tarojs/taro';
import { CSSProperties, FC } from 'react';
import { useRequest } from 'taro-hooks';
import './index.scss';

interface MyTeamProps {}

const MyTeam: FC<MyTeamProps> = () => {
  const { userInfo } = useUserStore((state) => state);
  const { loading, data, refresh } = useRequest(
    () => getMemberGroupInfo(userInfo?.userId, 1, 100),
    {
      cacheKey: 'my-team-list',
      manual: true,
      ready: !!userInfo?.userId,
      refreshDeps: [userInfo?.userId],
    },
  );

  useDidShow(() => {
    refresh();
  });

  if (loading) {
    return <LoadingPage />;
  }

  // if (data?.data.list?.length === 0) {
  //   return <EmptyContainer title="暂无团队数据" />;
  // }

  return (
    <View className="my-team-container">
      <View className="my-team-content">
        <View className="my-team-header">
          <Text className="my-team-title">我的邀请人</Text>
        </View>
        <View className="my-team-list">
          <CardListItem data={data?.data.myInviterInfo} />
        </View>
      </View>

      <View className="my-team-content">
        <View className="my-team-header">
          <Text className="my-team-title">我的团队({data?.data.myGroupInfo?.total || 0}人)</Text>
        </View>
        <View className="my-team-list">
          {data?.data?.myGroupInfo?.list?.map((item) => (
            <CardListItem key={item.memberUserId} data={item} />
          ))}
        </View>
      </View>
    </View>
  );
};

export const CardListItem = ({
  data,
  style,
}: {
  data?: Partial<MyInviterInfo> & { debtTypeName?: string; extra?: React.ReactNode };
  avatarSize?: number;
  style?: CSSProperties;
}) => {
  return (
    <View className="card-list-item" style={style}>
      <View className="card-list-item-avatar">
        <Image src={data?.avatar || svgStore.avatar} error={<Image src={svgStore.avatar} />} />
        <View className="card-list-item-avatar-name">{data?.debtTypeName}</View>
      </View>

      <View className="card-list-item-content">
        <View className="card-list-item-content-name">{data?.nickname}</View>
        <View className="card-list-item-content-time">{data?.createTime}</View>
      </View>

      <View className="card-list-item-right">{data?.extra}</View>
    </View>
  );
};

export default MyTeam;
