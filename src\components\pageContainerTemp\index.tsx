/**
 * 页面通用容器组件
 * 提供导航栏、内容区域和底部标签栏的标准布局结构
 */
import NavigationBar from '@/components/navigation-bar'; // 导航栏组件
import CustomTabBar from '@/components/tabbar'; // 底部标签栏组件
import { View } from '@tarojs/components'; // Taro基础组件
import { useEnv } from 'taro-hooks'; // 获取当前环境的Hook
import './index.scss'; // 样式文件

/**
 * 页面容器组件属性接口定义
 */
interface PageContainerProps {
  /**
   * 子组件，页面的主要内容区域
   * 将被渲染在内容区域中
   */
  children: React.ReactNode;

  /**
   * 页面标题
   * 显示在导航栏中央或左侧(取决于align参数)
   */
  title: string;

  /**
   * 是否显示底部标签栏组件
   * true: 显示底部标签栏并为其预留空间
   * false: 不显示底部标签栏且不预留空间
   * @default false
   */
  showTabbar?: boolean;

  /**
   * 是否显示返回按钮
   * true: 在导航栏左侧显示返回按钮
   * false: 不显示返回按钮
   * @default false
   */
  isRet?: boolean;

  /**
   * 标题在导航栏中的对齐方式
   * 'center': 标题居中对齐
   * 'start': 标题靠左对齐
   * @default 'start'
   */
  align?: 'center' | 'start';

  /**
   * 是否显示背景装饰
   * true: 显示背景装饰
   * false: 不显示背景装饰
   * @default true
   */
  showMarks?: boolean;

  /**
   * 背景装饰类型，控制装饰的样式
   * 'deep-pink': 深粉色装饰
   * 'glod': 金色装饰
   * 'none': 无装饰
   * '': 默认装饰
   * @default ''
   */
  marksType?: 'deep-pink' | '' | 'glod' | 'none';

  /**
   * 页面类型
   * 'default': 默认类型，不显示导航栏和背景装饰
   * 'custom': 自定义类型，根据其他参数决定是否显示导航栏和背景装饰
   * @default 'custom'
   */
  type?: 'default' | 'custom';

  /**
   * 自定义背景装饰
   * 如果设置，将替代默认的背景装饰
   */
  customMarks?: React.ReactNode;

  /**
   * 背景装饰的自定义类名
   * 可以用于覆盖默认的marks样式
   */
  marksClassName?: string;

  /**
   * 是否使用透明导航栏
   * true: 导航栏背景透明，文字和按钮为白色
   * false: 使用默认导航栏样式
   * @default false
   */
  transparentNav?: boolean;
}

/**
 * 页面容器组件
 * 用于统一页面布局结构，包含导航栏、内容区和可选的底部标签栏
 *
 * @component PageContainerTmp
 * @description
 * 这是一个通用的页面容器组件，提供了标准的页面布局结构。
 * 主要功能包括：
 * 1. 根据环境条件显示或隐藏导航栏
 * 2. 提供内容区域的包装
 * 3. 可选的背景装饰效果
 * 4. 可选的底部标签栏
 * 5. 提供挂载点用于动态渲染其他组件
 * 6. 支持透明导航栏
 *
 * @example
 * // 基本用法
 * <PageContainerTmp title="页面标题">
 *   <View>页面内容</View>
 * </PageContainerTmp>
 *
 * // 带返回按钮和底部标签栏
 * <PageContainerTmp
 *   title="详情页"
 *   isRet={true}
 *   showTabbar={true}
 * >
 *   <View>详情页内容</View>
 * </PageContainerTmp>
 *
 * // 自定义背景装饰
 * <PageContainerTmp
 *   title="特殊页面"
 *   marksType="deep-pink"
 * >
 *   <View>页面内容</View>
 * </PageContainerTmp>
 *
 * // 透明导航栏
 * <PageContainerTmp
 *   title="透明导航栏页面"
 *   transparentNav={true}
 *   isRet={true}
 * >
 *   <View>页面内容</View>
 * </PageContainerTmp>
 *
 * @param {PageContainerProps} props 页面容器组件参数
 * @returns {React.ReactElement} 渲染后的页面容器组件
 */
const PageContainerTmp = ({
  children,
  title,
  showTabbar = false,
  isRet = false,
  align = 'start',
  showMarks = true,
  marksType = '',
  type = 'custom',
  customMarks,
  marksClassName,
  transparentNav = false,
}: PageContainerProps) => {
  // 获取当前运行环境
  const env = useEnv();

  return (
    <View className={`tabbarContainer ${!showTabbar ? 'no-tabbar' : ''}`}>
      {/* 页面主体包装器 */}
      <View className="wrapper">
        {/* 非头条环境且为自定义类型时显示导航栏 */}
        {env !== 'TT' && type === 'custom' && (
          <NavigationBar
            align={align}
            title={title}
            isRet={isRet}
            transparent={transparentNav}
          ></NavigationBar>
        )}
        {/* 内容区域包装器 */}
        <View
          className="content-wrapper"
          style={{
            paddingBottom: showTabbar ? '' : 'env(safe-area-inset-bottom)',
          }}
        >
          {children}
        </View>
        {/* 自定义类型时显示背景装饰 */}
        {type === 'custom' &&
          (customMarks ? (
            customMarks
          ) : (
            <View className={`marks ${showMarks ? 'active' : 'hidden'} ${marksType} ${marksClassName || ''}`}>
              <View className="markright"></View>
              <View className="markleft"></View>
            </View>
          ))}

        {/* 自定义类型且需要显示标签栏时渲染底部标签栏 */}
        {type === 'custom' && showTabbar && <CustomTabBar></CustomTabBar>}
      </View>

      {/* 挂载容器，用于动态挂载其他组件 */}
      <View id="mount-container"></View>
    </View>
  );
};

export default PageContainerTmp;
