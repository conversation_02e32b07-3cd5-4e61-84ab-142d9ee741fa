/**
 * @description 根据委案id查询合同信息相关接口类型
 */

/**
 * @description 合同信息响应对象
 */
export interface ContractVO {
  createTime?: string;
  updateTime?: string;
  creator?: string;
  updater?: string;
  deleted?: boolean;
  /**
   * @description 主键ID
   */
  id?: number;
  /**
   * @description 案件委托ID
   */
  caseEntrustId?: number;
  /**
   * @description 关联委案编号
   */
  caseNumber?: string;
  /**
   * @description 模板ID
   */
  templateId?: number;
  /**
   * @description 合同类型
   */
  contractType?: string;
  /**
   * @description 模板名称
   */
  templateName?: string;
  /**
   * @description 合同编号（唯一）
   */
  contractNumber?: string;
  /**
   * @description 合同名称
   */
  contractName?: string;
  /**
   * @description 合同地址
   */
  contractUrl?: string;
  /**
   * @description 化债总金额（元）
   */
  totalDebtReductionAmount?: number;
  /**
   * @description 实际减免金额（元）
   */
  actualReductionAmount?: number;
  /**
   * @description 实际化债总额（元）
   */
  actualDebtReductionAmount?: number;
  /**
   * @description 每月应化债金额（元）
   */
  monthlyDebtReductionAmount?: number;
  /**
   * @description 已化债金额（元）
   */
  convertedDebtAmount?: number;
  /**
   * @description 剩余化债金额
   */
  remainingDebtAmount?: number;
  /**
   * @description 化债期数（月，必须>0）
   */
  debtReductionPeriods?: number;
  /**
   * @description 合同开始日期
   */
  startDate?: string;
  /**
   * @description 甲方名称
   */
  partyAName?: string;
  /**
   * @description 甲方统一社会信用代码
   */
  partyAUnifiedSocialCreditCode?: string;
  /**
   * @description 甲方法定代表人
   */
  partyALegalRepresentative?: string;
  /**
   * @description 法定代表人联系方式
   */
  partyAContactNumber?: string;
  /**
   * @description 甲方地址
   */
  partyAAddress?: string;
  /**
   * @description 乙方名称
   */
  partyBName?: string;
  /**
   * @description 乙方统一社会信用代码
   */
  partyBUnifiedSocialCreditCode?: string;
  /**
   * @description 乙方法定代表人
   */
  partyBLegalRepresentative?: string;
  /**
   * @description 法定代表人联系方式
   */
  partyBContactNumber?: string;
  /**
   * @description 乙方地址
   */
  partyBAddress?: string;
  /**
   * @description 丙方名称
   */
  partyCName?: string;
  /**
   * @description 丙方证件号码/统一社会信用代码
   */
  partyCIdNumberOrUnifiedSocialCreditCode?: string;
  /**
   * @description 丙方联系方式
   */
  partyCContactNumber?: string;
  /**
   * @description 甲方签订状态
   */
  partyASignStatus?: number;
  /**
   * @description 乙方签订状态
   */
  partyBSignStatus?: number;
  /**
   * @description 丙方签订状态
   */
  partyCSignStatus?: number;
  /**
   * @description 甲方签订时间
   */
  partyATime?: string;
  /**
   * @description 乙方签订时间
   */
  partyBTime?: string;
  /**
   * @description 丙方签订时间
   */
  partyCTime?: string;
  /**
   * @description 合同状态: 草稿 draft
   * 审批中 in_approval
   * 审批通过 end_approval
   * 审批驳回 back_approval
   * 作废 cancel
   * 履约中 in_performance
   * 合同结束 end
   * 变更 change
   */
  status?: string;
  /**
   * @description 审批人ID
   */
  approverId?: number;
  /**
   * @description 审批人姓名
   */
  approveName?: string;
  /**
   * @description 审批时间
   */
  approvalTime?: string;
  /**
   * @description 审批描述
   */
  approvalRemark?: string;
  /**
   * @description 丙方地址
   */
  partyCAddress?: string;
  /**
   * @description 债权人
   */
  creditorName?: string;
  /**
   * @description 逾期账号
   */
  overdueAccount?: string;
  /**
   * @description 委托金额
   */
  entrustedAmount?: number;
  /**
   * @description 利息
   */
  interest?: number;
  /**
   * @description 年化率
   */
  annualizedRate?: number;
  /**
   * @description 滞纳金
   */
  lateFee?: number;
  /**
   * @description 最大减免金额
   */
  maxReduction?: number;
  /**
   * @description 当前期
   */
  currentPeriod?: number;
  /**
   * @description 开卡银行
   */
  bank?: string;
}
