import path from 'path';
import { defineConfig } from 'vite';

export default defineConfig({
  css: {
    preprocessorOptions: {
      scss: {
        // 忽略 Sass 弃用警告
        silenceDeprecations: ['legacy-js-api', 'import'],
        // 或者使用 quietDeps 来忽略来自 node_modules 的警告
        quietDeps: true,
      },
    },
  },
  build: {
    rollupOptions: {
      external: ['src/custom-tab-bar'], // 确保自定义组件不会被打包为外部依赖
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
});
