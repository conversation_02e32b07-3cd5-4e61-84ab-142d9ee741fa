/**
 * 金额格式化工具类
 * 统一处理金额显示的格式化、空值处理等
 */

/**
 * 金额格式化选项
 */
export interface MoneyFormatOptions {
  /** 小数位数，默认为 2 */
  digits?: number;
  /** 是否显示千分位分隔符，默认为 false */
  thousands?: boolean;
  /** 货币符号，默认为空字符串 */
  symbol?: string;
  /** 空值时的显示文本，默认为 '--' */
  emptyText?: string;
  /** 是否在空值时显示 0.00，默认为 false */
  showZeroForEmpty?: boolean;
  /** 单位文本，如 '元'，默认为空 */
  unit?: string;
}

/**
 * 金额显示场景枚举
 */
export enum MoneyDisplayScene {
  /** 列表页面 - 统一格式，简洁显示 */
  LIST = 'list',
  /** 详情页面 - 精确显示 */
  DETAIL = 'detail',
  /** 卡片展示 - 突出显示 */
  CARD = 'card',
  /** 表单输入 - 用户友好 */
  FORM = 'form',
}

/**
 * 预设的格式化配置
 */
const PRESET_CONFIGS: Record<MoneyDisplayScene, MoneyFormatOptions> = {
  [MoneyDisplayScene.LIST]: {
    digits: 2,
    thousands: false,
    symbol: '',
    emptyText: '--',
    showZeroForEmpty: false,
    unit: '',
  },
  [MoneyDisplayScene.DETAIL]: {
    digits: 2,
    thousands: true,
    symbol: '￥',
    emptyText: '--',
    showZeroForEmpty: false,
    unit: '',
  },
  [MoneyDisplayScene.CARD]: {
    digits: 2,
    thousands: false,
    symbol: '',
    emptyText: '--',
    showZeroForEmpty: false,
    unit: '元',
  },
  [MoneyDisplayScene.FORM]: {
    digits: 2,
    thousands: false,
    symbol: '',
    emptyText: '0.00',
    showZeroForEmpty: true,
    unit: '',
  },
};

/**
 * 检查值是否为空（null、undefined、NaN、空字符串）
 */
function isEmpty(value: any): boolean {
  return value === null || value === undefined || value === '' || Number.isNaN(Number(value));
}

/**
 * 格式化金额为字符串
 * @param amount 金额数值
 * @param options 格式化选项
 * @returns 格式化后的金额字符串
 */
export function formatMoney(
  amount: number | string | null | undefined,
  options: MoneyFormatOptions = {},
): string {
  const {
    digits = 2,
    thousands = false,
    symbol = '',
    emptyText = '--',
    showZeroForEmpty = false,
    unit = '',
  } = options;

  // 处理空值
  if (isEmpty(amount)) {
    return showZeroForEmpty ? `${symbol}0.${'0'.repeat(digits)}${unit}` : emptyText;
  }

  // 转换为数字
  const numAmount = Number(amount);
  if (Number.isNaN(numAmount)) {
    return showZeroForEmpty ? `${symbol}0.${'0'.repeat(digits)}${unit}` : emptyText;
  }

  // 格式化数字
  let formattedNumber = numAmount.toFixed(digits);

  // 添加千分位分隔符
  if (thousands) {
    const parts = formattedNumber.split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    formattedNumber = parts.join('.');
  }

  return `${symbol}${formattedNumber}${unit}`;
}

/**
 * 根据场景格式化金额
 * @param amount 金额数值
 * @param scene 显示场景
 * @param customOptions 自定义选项（会覆盖预设配置）
 * @returns 格式化后的金额字符串
 */
export function formatMoneyByScene(
  amount: number | string | null | undefined,
  scene: MoneyDisplayScene,
  customOptions: Partial<MoneyFormatOptions> = {},
): string {
  const presetConfig = PRESET_CONFIGS[scene];
  const finalOptions = { ...presetConfig, ...customOptions };
  return formatMoney(amount, finalOptions);
}

/**
 * 格式化金额用于列表显示
 * @param amount 金额数值
 * @param options 自定义选项
 * @returns 格式化后的金额字符串
 */
export function formatMoneyForList(
  amount: number | string | null | undefined,
  options: Partial<MoneyFormatOptions> = {},
): string {
  return formatMoneyByScene(amount, MoneyDisplayScene.LIST, options);
}

/**
 * 格式化金额用于详情显示
 * @param amount 金额数值
 * @param options 自定义选项
 * @returns 格式化后的金额字符串
 */
export function formatMoneyForDetail(
  amount: number | string | null | undefined,
  options: Partial<MoneyFormatOptions> = {},
): string {
  return formatMoneyByScene(amount, MoneyDisplayScene.DETAIL, options);
}

/**
 * 格式化金额用于卡片显示
 * @param amount 金额数值
 * @param options 自定义选项
 * @returns 格式化后的金额字符串
 */
export function formatMoneyForCard(
  amount: number | string | null | undefined,
  options: Partial<MoneyFormatOptions> = {},
): string {
  return formatMoneyByScene(amount, MoneyDisplayScene.CARD, options);
}

/**
 * 格式化金额用于表单显示
 * @param amount 金额数值
 * @param options 自定义选项
 * @returns 格式化后的金额字符串
 */
export function formatMoneyForForm(
  amount: number | string | null | undefined,
  options: Partial<MoneyFormatOptions> = {},
): string {
  return formatMoneyByScene(amount, MoneyDisplayScene.FORM, options);
}

/**
 * 获取 NutUI Price 组件的属性
 * @param amount 金额数值
 * @param options 格式化选项
 * @returns Price 组件属性对象
 */
export function getPriceProps(
  amount: number | string | null | undefined,
  options: MoneyFormatOptions = {},
) {
  const {
    digits = 2,
    thousands = false,
    symbol = '',
    emptyText = '--',
    showZeroForEmpty = false,
  } = options;

  // 处理空值
  if (isEmpty(amount)) {
    return {
      price: showZeroForEmpty ? 0 : emptyText,
      symbol,
      digits,
      thousands,
    };
  }

  const numAmount = Number(amount);
  if (Number.isNaN(numAmount)) {
    return {
      price: showZeroForEmpty ? 0 : emptyText,
      symbol,
      digits,
      thousands,
    };
  }

  return {
    price: numAmount,
    symbol,
    digits,
    thousands,
  };
}

/**
 * 将数值转换为指定精度的整数
 * @param num 数值
 * @param digits 小数位数
 * @returns 转换后的整数
 */
function toInteger(num: number | string, digits: number = 2): number {
  const strNum = typeof num === 'string' ? num : num.toString();
  const [intPart, decimalPart = ''] = strNum.split('.');
  const paddedDecimal = decimalPart.padEnd(digits, '0').slice(0, digits);
  return parseInt(intPart + paddedDecimal, 10);
}

/**
 * 从整数转回为原始精度的数值
 * @param intVal 整数值
 * @param digits 小数位数
 * @returns 转换后的浮点数
 */
function fromInteger(intVal: number, digits: number = 2): number {
  return intVal / Math.pow(10, digits);
}

/**
 * 精确相加两个金额
 * @param a 第一个金额
 * @param b 第二个金额
 * @param digits 小数位数，默认为2
 * @returns 相加后的金额
 */
export function addMoney(a: number | string, b: number | string, digits: number = 2): number {
  if (isEmpty(a)) a = 0;
  if (isEmpty(b)) b = 0;

  const intA = toInteger(a, digits);
  const intB = toInteger(b, digits);
  const result = intA + intB;

  return fromInteger(result, digits);
}

/**
 * 精确相减两个金额
 * @param a 被减数
 * @param b 减数
 * @param digits 小数位数，默认为2
 * @returns 相减后的金额
 */
export function subtractMoney(a: number | string, b: number | string, digits: number = 2): number {
  if (isEmpty(a)) a = 0;
  if (isEmpty(b)) b = 0;

  const intA = toInteger(a, digits);
  const intB = toInteger(b, digits);
  const result = intA - intB;

  return fromInteger(result, digits);
}
