import EmptyContainer from '@/components/empty-container';
import SvgIcon from '@/pages/me/components/SvgIcon';
import { getReportHistory } from '@/serves';
import useUserStore from '@/store/useUserStore';
import { formatDaysToYearMonthDay } from '@/utils/date';
import { SvgName } from '@/utils/svgLoader';
import { Text, View } from '@tarojs/components';
import Taro, { useDidShow } from '@tarojs/taro';
import { FC, useCallback } from 'react';
import { useRequest } from 'taro-hooks';
import './index.scss';

interface ReporterListProps {
  isShow: boolean;
}
interface Data {
  title: string;
  icon: SvgName;
  date: string;
  time: string;
  price: string;
}

const ReporterList: FC<ReporterListProps> = ({ isShow }) => {
  const { userInfo } = useUserStore.getState();
  const { data, refresh } = useRequest(
    () => {
      return getReportHistory(userInfo?.userId?.toString() || '');
    },
    {
      cacheKey: 'report-history',
      refreshDeps: [isShow],
      ready: !!userInfo?.userId && isShow,
    },
  );

  // 页面显示
  useDidShow(() => {
    refresh();
  });

  const handleItemClick = useCallback((id: number) => {
    Taro.navigateTo({
      url: `/pages/report/index?id=${id}`,
    });
  }, []);

  if (!data?.data?.length) {
    return <EmptyContainer title="暂无历史报告" />;
  }

  return (
    <View className="reporter-list-container">
      {data?.data?.map((data, index) => (
        <View className="reporter-list" key={index} onClick={() => handleItemClick(data.id)}>
          <View className="r-top">
            <View className="r-top-left">
              <SvgIcon name={'icon_cp3'} size={16} />
              <Text className="title">{data.type}</Text>
            </View>
            <Text className="time">{data?.updateTime?.toString()}</Text>
          </View>
          <View className="r-bottom">
            <View className="r-bottom-item">
              <Text>{formatDaysToYearMonthDay(data.repaymentTime)}</Text>
              <Text>还款时长</Text>
            </View>
            <View className="r-bottom-item">
              <Text>{data.debtAmount?.toString()}</Text>
              <Text>预测金额(元)</Text>
            </View>
          </View>
        </View>
      ))}
    </View>
  );
};

export default ReporterList;
