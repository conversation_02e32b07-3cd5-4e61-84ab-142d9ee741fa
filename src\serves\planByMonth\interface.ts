// Response interface
export interface AppPlanByMonthRes {
  /*主键ID */
  id: number;

  /*责任人Id */
  responsible: number;

  /*责任人姓名 */
  responsibleName: string;

  /*任务名称 */
  workName: string;

  /*任务编码 */
  workNum: string;

  /*任务类型 */
  workType: string;

  /*任务目标值 */
  workTarget: number;

  /*任务内容 */
  workContent: string;

  /*生效时间 */
  startTime: string;

  /*失效时间 */
  endTime: string;

  /*状态 */
  status: string;

  /*收益类型 */
  benefitType: string;

  /*预计收益 */
  benefit: string;

  /*备注 */
  remark: string;

  /*更新人 */
  updater: string;

  /*创建人 */
  creator: string;

  /*创建时间 */
  createTime: string;

  /*更新时间 */
  updateTime: string;
}

// Response interface
export interface AppPlanByIdRes {
  /* */
  code: number;

  /* */
  data: {
    /*主键ID */
    id: number;

    /*责任人Id */
    responsible: number;

    /*责任人姓名 */
    responsibleName: string;

    /*任务名称 */
    workName: string;

    /*任务编码 */
    workNum: string;

    /*任务类型 来自字典work_type */
    workType: string;

    /*任务目标值 */
    workTarget: number;

    /*任务内容 */
    workContent: string;

    /*生效时间 */
    startTime: string;

    /*失效时间 */
    endTime: string;

    /*状态 字典work_status */
    status: string;

    /*收益类型 字典work_benefit_type */
    benefitType: string;

    /*预计收益 */
    benefit: string;

    /*备注 */
    remark: string;

    /*更新人 */
    updater: string;

    /*创建人 */
    creator: string;

    /*创建时间 */
    createTime: string;

    /*更新时间 */
    updateTime: string;
  };

  /* */
  msg: string;
}
