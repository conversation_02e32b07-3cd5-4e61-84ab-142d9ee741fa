import useDict from '@/serves/dict/hooks';
import { appPlanByMonth } from '@/serves/planByMonth';
import useUserStore from '@/store/useUserStore';
import { useCallback, useMemo } from 'react';
import { useRequest } from 'taro-hooks';

/**
 * @interface UsePlanByMonthProps
 * @description usePlanByMonth Hook 的属性接口
 */
interface UsePlanByMonthProps {
  /**
   * @property {string} date
   * @description 日期，格式为 YYYY-MM
   */
  date: string;
}

/**
 * @function usePlanByMonth
 * @description 一个自定义 Hook，用于获取指定月份的工作计划数据。
 * @param {UsePlanByMonthProps} props - Hook 的属性，包含日期。
 * @returns {object} 包含工作类型数据、当月工作名称、原始数据、获取工作类型标签的函数和获取当天工作任务的函数。
 */
const usePlanByMonth = ({ date }: UsePlanByMonthProps) => {
  const { userInfo } = useUserStore((state) => state);
  const { dictData } = useDict();

  /**
   * @description 使用 useRequest Hook 请求指定月份的工作计划数据。
   * @param {string} date - 请求的日期，格式为 YYYY-MM。
   * @returns {Promise<any>} 工作计划数据。
   * @property {string} cacheKey - 缓存键，用于缓存请求结果。
   * @property {Array<any>} refreshDeps - 依赖项数组，当依赖项变化时重新请求。
   * @property {boolean} ready - 请求是否准备就绪，当 accessToken 和 date 都存在时为 true。
   * @property {Function} onError - 请求失败时的回调函数。
   */
  const { data } = useRequest(() => appPlanByMonth(date), {
    cacheKey: 'appPlanByMonth',
    refreshDeps: [date],
    ready: !!userInfo?.accessToken && !!date,
    onError: (error) => {
      console.log('appPlanByMonth error', error);
    },
  });

  /**
   * @property {Array<any>} workTypeData
   * @description 从字典数据中筛选出工作类型数据。
   */
  const workTypeData = useMemo(() => {
    const workType = dictData?.filter((item) => item.dictType === 'work_type');
    return workType;
  }, [dictData]);

  /**
   * @property {Array<object>} currentMonthWorkNames
   * @description 获取当月任务的名称、类型和日期等信息。
   */
  const currentMonthWorkNames = useMemo(() => {
    return (
      data?.data?.map((item) => {
        return {
          name: item.workName,
          type: item.workType,
          day: new Date(item.startTime).getDate(),
          ...item,
        };
      }) ?? []
    );
  }, [data?.data]);

  /**
   * @function getWorkTypeOfLabel
   * @description 根据工作类型的值获取对应的标签。
   * @param {string} value - 工作类型的值。
   * @returns {string | undefined} 对应工作类型的标签。
   */
  const getWorkTypeOfLabel = useCallback(
    (value: string) => {
      const workType = workTypeData?.find((item) => item.value === value);
      return workType?.label;
    },
    [workTypeData],
  );

  /**
   * @function getCurrentDayWorks
   * @description 获取选择日期当天的工作任务。
   * @param {Date} activeDate - 当前选择的日期对象。
   * @returns {Array<any>} 当天的工作任务列表。
   */
  const getCurrentDayWorks = useCallback(
    (activeDate: Date) => {
      return (
        data?.data?.filter((item) => {
          const startTime = new Date(item.startTime).getDate();
          const currentTime = activeDate.getDate();
          return startTime === currentTime;
        }) ?? []
      );
    },
    [data?.data],
  );

  return {
    workTypeData,
    currentMonthWorkNames,
    data,
    getWorkTypeOfLabel,
    getCurrentDayWorks,
  };
};

export default usePlanByMonth;
