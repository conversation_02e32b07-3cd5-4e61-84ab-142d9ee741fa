/* 反馈历史页面容器 */
.feedback-history {
  background-color: #f5f5f5;
  padding: 28px 24px;
  height: 100%;
  overflow: auto;

  /* 加载状态容器 */
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  /* 反馈列表容器 */
  .feedback-list {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding-bottom: 24px;

    /* 单个反馈项样式 */
    .feedback-item {
      padding: 24px 32px;
      background-color: #fff;
      border-radius: 16px;

      &:last-child {
        border-bottom: none;
      }

      /* 反馈项头部：标题和状态 */
      &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }

      /* 反馈标题 */
      &-title {
        font-size: 32px;
        font-weight: 500;
        color: #131313;
      }

      /* 反馈状态标签 */
      &-status {
        font-size: 24px;
        padding: 0px 16px;
        height: 20px * 2;
        line-height: 20px * 2;
        border-radius: 2px * 2;
        background: rgba(251, 160, 48, 0.1);
        color: #fba030;
        text-align: center;

        /* 已解决状态样式 */
        &.resolved {
          color: #4b81ee;
          background: rgba(75, 129, 238, 0.1);
          border: none;
        }
      }

      /* 反馈内容 */
      &-content {
        font-size: 28px;
        color: #666;
        margin-bottom: 16px;
      }

      /* 反馈项底部 */
      &-footer {
        display: flex;
        justify-content: flex-start;
      }

      /* 反馈时间 */
      &-time {
        font-size: 28px;
        color: #9d9d9d;
      }
    }
  }
}
