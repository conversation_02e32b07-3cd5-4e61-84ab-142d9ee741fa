.notebook-container {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  padding: 20px;
  --nutui-hoverbutton-item-background: #f00;
  position: relative;
  min-height: 100vh;
}

.notebook-content {
  display: flex;
  flex-direction: column;
  gap: 28px;
  .note-item {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 16px;
    padding: 24px 32px;
    gap: 12px;
    .note-title {
      color: #131313;
      font-size: 16px * 2;
      font-weight: 500;
    }
    .note-content {
      color: #666;
      font-size: 14px * 2;
      font-weight: 400;
    }
    .note-date {
      color: #9d9d9d;
      font-size: 12px * 2;
      font-weight: 400;
    }
  }
}

/* 添加悬浮按钮样式 */
:global(.nut-drag) {
  position: fixed !important;
  z-index: 9999 !important;
}
