import { MoneyDisplayCard } from '@/components/MoneyDisplay';
import SvgIcon from '@/pages/me/components/SvgIcon';
import { GetMyEarningsRes } from '@/serves/member/myCommissions/interface';
import { Text, View } from '@tarojs/components';
import { FC } from 'react';
import './index.scss';

interface MyCommissionsHeaderProps {
  data?: GetMyEarningsRes;
}

const MyCommissionsHeader: FC<MyCommissionsHeaderProps> = ({ data }) => {
  return (
    <View className="my-commissions-header">
      {/* 可用金额 */}
      <View className="my-commissions-header-item">
        <View className="my-commissions-header-item-title">
          <View>可用金额(元)</View>
          <View className="icon-container">
            <SvgIcon name="myCommissionSafe" size={12} />
            <Text>安全保障中</Text>
          </View>
        </View>
        <View className="my-commissions-header-item-value">
          <MoneyDisplayCard
            amount={data?.data?.earningsCombination?.usableEarnings}
            usePrice={false}
            options={{ unit: '' }}
          />
        </View>
      </View>
      {/* 可提现 */}
      <View className="my-commissions-header-tx">
        <View className="my-commissions-header-tx-title">可提现</View>
        <View className="my-commissions-header-tx-value">
          <MoneyDisplayCard
            amount={data?.data?.earningsCombination?.canWithdraw}
            usePrice={false}
            options={{ unit: '', digits: 2 }}
          />
        </View>
        <View className="my-commissions-header-tx-btn">
          <View>去提现</View>
          <SvgIcon name="arrow-right-small" size={12} />
        </View>
      </View>
      {/* info */}
      <View className="my-commissions-header-info">
        <View className="my-commissions-header-info-item">
          <View className="my-commissions-header-info-item-title">总收益</View>
          <View className="my-commissions-header-info-item-value">
            <MoneyDisplayCard
              amount={data?.data?.earningsCombination?.sumEarnings}
              usePrice={false}
              options={{ unit: '', digits: 2 }}
            />
          </View>
        </View>
        <View className="my-commissions-header-info-item">
          <View className="my-commissions-header-info-item-title">待结算金额(元)</View>
          <View className="my-commissions-header-info-item-value">
            <MoneyDisplayCard
              amount={data?.data?.earningsCombination?.pendingSettlementMoney}
              usePrice={false}
              options={{ unit: '', digits: 2 }}
            />
          </View>
        </View>
        <View className="my-commissions-header-info-item">
          <View className="my-commissions-header-info-item-title">冻结资金(元)</View>
          <View className="my-commissions-header-info-item-value">
            <MoneyDisplayCard
              amount={data?.data?.earningsCombination?.freezeMoney}
              usePrice={false}
              options={{ unit: '', digits: 2 }}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

export default MyCommissionsHeader;
