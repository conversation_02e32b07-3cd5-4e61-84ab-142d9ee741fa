/**
 * 消息列表组件
 * 用于展示系统消息列表，包含消息标题、内容和底部信息
 */
import EmptyContainer from '@/components/empty-container';
import { Text, View } from '@tarojs/components';
import './index.scss';

const messageList: any[] = [
  // {
  //   title: '还款提醒',
  //   content: '第28期（2025年5月）还款截止日期临近，请尽快还款，以免对后续还款造成影响',
  //   time: '2025/03/12 12:03:54',
  // },
  // {
  //   title: '还款提醒',
  //   content: '第28期（2025年5月）还款截止日期临近，请尽快还款，以免对后续还款造成影响',
  //   time: '2025/03/12 12:03:54',
  // },
];

const MessageList = () => {
  if (messageList.length === 0) {
    return <EmptyContainer title="暂无消息" />;
  }

  return (
    // 消息列表容器
    <View className="message-list">
      {/* 单个消息项 */}
      {messageList.map((item, index) => (
        <View className="message-list-item" key={index}>
          {/* 消息标题区域：包含状态指示点和标题文本 */}
          <View className="message-list-item-title">
            <View className="dot"></View>
            <Text>{item?.title}</Text>
          </View>
          {/* 消息内容区域：显示具体消息内容 */}
          <View className="message-list-item-content">
            第28期（2025年5月）还款截止日期临近，请尽快还款，以免对后续还款造成影响
          </View>
          {/* 消息底部信息：显示消息来源和时间 */}
          <View className="message-list-item-bottom">
            <Text>系统消息</Text>
            <Text>{item?.time}</Text>
          </View>
        </View>
      ))}
    </View>
  );
};

export default MessageList;
