import { ResultModal } from '@/components/ResultModal';
import SvgIcon from '@/pages/me/components/SvgIcon';
import { Button, Form, Input, Popup } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import { useState } from 'react';
import './index.scss';

export default function BindBandCard() {
  const [visible, setVisible] = useState(false);
  const [type, setType] = useState<'success' | 'fail' | 'loading'>('success');
  const [showIcon, setShowIcon] = useState(false);

  const onFinish = (values: any) => {
    setVisible(true);
    console.log(values);
    setTimeout(() => {
      setVisible(false);
      setType('success');
    }, 3000);
  };
  const onFinishFailed = (values: any, errors: any) => {
    console.log(errors, values);
  };
  return (
    <View className="bind-band-card">
      <Form divider labelPosition="left" onFinish={onFinish} onFinishFailed={onFinishFailed}>
        <Form.Item
          label="开户姓名"
          name="username"
          rules={[{ required: true, message: '请输入开户人姓名' }]}
        >
          <Input placeholder="请输入开户人姓名" type="text" align="right" />
        </Form.Item>
        <Form.Item
          label="银行卡号"
          name="bankCard"
          rules={[{ required: true, message: '请输入银行卡号' }]}
        >
          <Input placeholder="请输入银行卡号" type="text" align="right" />
        </Form.Item>
        <Form.Item
          label="开户银行名称"
          name="bankName"
          rules={[{ required: true, message: '请输入开户银行名称' }]}
        >
          <Input placeholder="请输入开户银行名称" type="text" align="right" />
        </Form.Item>
        <Form.Item
          label="预留手机号"
          name="tel"
          rules={[
            { required: true, message: '请输入预留手机号' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
          ]}
        >
          <Input placeholder="请输入银行卡预留手机号" type="number" align="right" />
        </Form.Item>
        <View className="code-container">
          <Form.Item
            label="验证码"
            name="code"
            rules={[{ required: true, message: '请输入验证码' }]}
          >
            <Input placeholder="请输入6位数字验证码" type="text" maxLength={6} align="right" />
          </Form.Item>
          <Button type="primary" size="small" fill="none" onClick={() => {}}>
            获取验证码
          </Button>
        </View>

        <View className="tips">
          绑定银行卡
          <View className="tips-list">
            <View>1. 该银行卡仅用于提现使用，不作其他用途。</View>
            <View>2. 绑定时开户姓名、银行卡号、开户行信息、预留手机号信息需全部一致。</View>
            <View>3. 本系统支持的银行详见说明。</View>
            <View>4. 输入的短信验证码在有效期内（短信验证码5分钟有效）。</View>
          </View>
        </View>
        <View className="submit-btn">
          <View className="submit-btn-desc" onClick={() => setShowIcon(true)}>
            <SvgIcon name="descSvg" size={14} />
            <Text>说明</Text>
          </View>
          <Button type="primary" formType="submit" block>
            绑定银行卡
          </Button>
        </View>
      </Form>
      <ResultModal
        visible={visible}
        onClose={() => setVisible(false)}
        type={type}
        successTitle="绑定银行卡成功"
      />
      <Popup
        closeable
        visible={showIcon}
        title="说明"
        position="bottom"
        onClose={() => {
          setShowIcon(false);
        }}
        className="popup"
      >
        <View className="popup-desc-title">系统支持银行列表和提现限额</View>
        <View className="popup-table">
          <View className="popup-table-header">
            <View className="popup-table-col">银行</View>
            <View className="popup-table-col">额度</View>
          </View>
          <View className="popup-table-row">
            <View className="popup-table-col">招商银行</View>
            <View className="popup-table-col">单卡单笔或日累计限额2万</View>
          </View>
          <View className="popup-table-row">
            <View className="popup-table-col">华夏银行</View>
            <View className="popup-table-col">单笔0.5万，日类型2万，月类型60万</View>
          </View>
          <View className="popup-table-row">
            <View className="popup-table-col">农业银行</View>
            <View className="popup-table-col not-support">不支持</View>
          </View>
          <View className="popup-table-row">
            <View className="popup-table-col">长沙银行</View>
            <View className="popup-table-col">单笔单日1万</View>
          </View>
          <View className="popup-table-row">
            <View className="popup-table-col">民生银行</View>
            <View className="popup-table-col">单笔1万，日类型1万</View>
          </View>
          <View className="popup-table-row">
            <View className="popup-table-col">建设银行</View>
            <View className="popup-table-col">日累计0.5万</View>
          </View>
          <View className="popup-table-row">
            <View className="popup-table-col">上海银行</View>
            <View className="popup-table-col not-support">不支持</View>
          </View>
          <View className="popup-table-row">
            <View className="popup-table-col">浦发银行</View>
            <View className="popup-table-col">单笔5万，日/月不限</View>
          </View>
          <View className="popup-table-row">
            <View className="popup-table-col">工商银行</View>
            <View className="popup-table-col">单笔1万，单日2万</View>
          </View>
          <View className="popup-table-row">
            <View className="popup-table-col">中国银行</View>
            <View className="popup-table-col">日累计2万，月累计60万</View>
          </View>
        </View>
      </Popup>
    </View>
  );
}
