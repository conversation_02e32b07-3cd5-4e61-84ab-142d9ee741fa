import useCustomTabbar from '@/store/custom-tab-bar';
import { svgStore } from '@/utils/svgLoader';
import { Image, Tabbar } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import React, { useRef } from 'react';
import './index.scss';
const tabBarConfig = {
  color: '#676767',
  selectedColor: '#FF0000',
  backgroundColor: '#F7F7F7',
  borderStyle: 'black',
  list: [
    {
      pagePath: '/pages/home/<USER>',
      title: '首页',
      image: svgStore['sy'],
      selectedImage: svgStore['sy2'],
    },
    {
      pagePath: '/pages/plan/index',
      title: '计划',
      image: svgStore['jh'],
      selectedImage: svgStore['jh2'],
    },
    {
      pagePath: '/pages/evaluation/index',
      title: '测评',
      image: svgStore['cx'],
      selectedImage: svgStore['cx2'],
    },
    // {
    //   pagePath: '/pages/message/index',
    //   title: '资讯',
    //   image: zx,
    //   selectedImage: zx2,
    // },
    {
      pagePath: '/pages/me/index',
      title: '我的',
      image: svgStore['wd'],
      selectedImage: svgStore['wd2'],
    },
  ],
};

const Index = () => {
  const [selected, setSelected] = useCustomTabbar((state) => [state.selected, state.setSelected]);
  const isSwitchingRef = useRef(false);
  const onChange = (index: number) => {
    if (isSwitchingRef.current) return;
    isSwitchingRef.current = true;
    setSelected(index);
    requestAnimationFrame(() => {
      setTimeout(() => {
        Taro.switchTab({
          url: tabBarConfig.list[index].pagePath,
          success: () => {
            // 页面跳转成功，解锁
            setTimeout(() => {
              isSwitchingRef.current = false;
            }, 300);
          },
          fail: () => {
            isSwitchingRef.current = false;
          },
        });
      }, 16);
    });
  };
  return (
    <Tabbar
      className="custom-tab-bar"
      value={selected}
      onSwitch={onChange}
      fixed
      safeArea={false}
      defaultValue={0}
      direction="horizontal"
      inactiveColor={tabBarConfig.color}
      activeColor={tabBarConfig.selectedColor}
    >
      {tabBarConfig.list.map((item, index) => (
        <Tabbar.Item
          key={index}
          title={item.title}
          icon={(active: boolean) => (
            <MemoIcon active={active} image={item.image} selectedImage={item.selectedImage} />
          )}
          max={0}
          top="0"
          right="0"
          direction="horizontal"
          onActiveClick={() => onChange(index)}
        />
      ))}
    </Tabbar>
  );
};

// 组件封装
const MemoIcon = React.memo(
  ({ active, image, selectedImage }: { active: boolean; image: string; selectedImage: string }) => (
    <Image width={'48rpx'} height={'48rpx'} src={active ? selectedImage : image} mode="aspectFit" />
  ),
);
export default Index;
Index.options = {
  addGlobalClass: true,
};
