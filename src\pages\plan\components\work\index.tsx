import SvgIcon from '@/pages/me/components/SvgIcon';
import { Feedback } from '@nutui/icons-react-taro';

import DateSelect from '@/components/dateSelect';
import { Button } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import { FC } from 'react';
import './index.scss';
interface WorkPlanProps {}

const testData = Array(10)
  .fill(0)
  .map((item) => item);

const WorkPlan: FC<WorkPlanProps> = () => {
  // 处理日期选择回调
  const handleDateConfirm = (date: string) => {
    console.log('选择的日期:', date);
    // 处理日期选择逻辑
  };

  return (
    <>
      <View className="work">
        <View className="work-date-select">
          <Feedback size={16} />
          <DateSelect onConfirm={handleDateConfirm} />
        </View>
        <View className="work-list">
          {testData.map((_, index) => (
            <View className="work-item" key={index}>
              <SvgIcon name="icon_cp2" size={32}></SvgIcon>
              <View className="detail">
                <Text className="title">个人消费·已化债金额2358.00元</Text>
                <Text className="desc">2025年3月计划消费金额3580.00元</Text>
                <Text className="info">消费毛利用于债务抵扣/提现</Text>
              </View>
              <Button size="small" className="detail-button" type="primary" fill="outline">
                去消费
              </Button>
            </View>
          ))}
        </View>
      </View>
    </>
  );
};

export default WorkPlan;
