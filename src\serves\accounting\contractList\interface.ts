/**
 * @description 管理后台 - 合同模板分页查询参数
 */
export interface SelectAppTemplateVO {
  /**
   * @description 页码，从 1 开始
   */
  pageNo: number;
  /**
   * @description 每页条数，最大值为 100
   */
  pageSize: number;
  /**
   * @description 合同类型，来自字典 contract_type
   */
  contractType?: string;
  /**
   * @description 模板名称，模糊匹配
   */
  templateName?: string;
  /**
   * @description 状态 0禁用 1启用
   */
  templateStatus?: number;
  /**
   * @description 修改时间
   */
  updateTime?: string[];
}

/**
 * @description 管理后台 - 合同模板返回参数
 */
export interface ContractTemplateVO {
  id?: number;
  /**
   * @description 合同类型，来自字典 contract_type
   */
  contractType?: string;
  /**
   * @description 模板名称，模糊匹配
   */
  templateName?: string;
  /**
   * @description 状态 0禁用 1启用
   */
  templateStatus?: number;
  /**
   * @description 创建时间
   */
  createTime?: string;
  /**
   * @description 更新时间
   */
  updateTime?: string;
  /**
   * @description 文件内网url
   */
  filePath?: string;
  /**
   * @description 关联合同数量
   */
  relatedContracts?: number;
  /**
   * @description 更新人
   */
  updater?: string;
}

/**
 * @description 分页结果
 */
export interface PageResultContractTemplateVO {
  /**
   * @description 数据
   */
  list: ContractTemplateVO[];
  /**
   * @description 总量
   */
  total: number;
}

/**
 * @description 获得合同模板分页列表响应
 */
export interface SelectAppTemplateRes {
  code?: number;
  data?: PageResultContractTemplateVO;
  msg?: string;
}
