.tabbarContainer {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: #f5f4f9;
  overflow: hidden;

  & > view:first-of-type {
    width: 100%;
    flex-basis: var(--height-tabbar);
    overflow-y: auto;
    overflow-x: hidden;
  }
}

.marks {
  width: 100vw;
  height: 100vh;
  position: absolute;
  right: 0;
  top: 0;
  pointer-events: none;
  overflow-x: hidden;

  .markright {
    position: absolute;
    right: -172px;
    top: -60px;
    width: 426px;
    height: 426px;
    flex-shrink: 0;
    background-color: #fbe6e8;
    filter: blur(31.26024055480957px);

    border-radius: 50%;
  }

  .markleft {
    position: absolute;
    left: -204px;
    top: 64px;
    width: 310px;
    height: 310px;
    flex-shrink: 0;
    background-color: #fbe6e8;
    filter: blur(31.26024055480957px);
    border-radius: 50%;
  }
}

.ellipsis {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

page {
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  --height-tabbar: calc(100vh - 110px - env(safe-area-inset-bottom));
  // 导航栏高度
  --height-navigation: 87px * 2;
  // 底部标签栏高度
  --height-tabs: 80px * 2;
  // 主内容区高度
  --height-main: calc(100vh - var(--height-navigation) - var(--height-tabs));
  // 主内容区高度，没有底部的情况
  --height-main-no-tabbar: calc(100vh - var(--height-navigation) - env(safe-area-inset-bottom));

  --nutui-tabs-titles-background-color: transparent;

  --nutui-button-default-height: 88px;
  --nutui-color-primary: #f00;
  --nutui-color-primary-stop1: #f00;
  --nutui-color-primary-stop2: #f00;
  --nutui-tabs-tabpane-backgroundColor: transparent;
  --nutui-form-item-body-slots-text-align: right;
}

View {
  box-sizing: border-box;
}

/* 编辑器中图片的全局样式 */
.note-image,
.editor img {
  max-width: 90% !important;
  max-height: 400px !important;
  object-fit: contain;
  margin: 10px 0;
  border-radius: 8px;
  display: block;
}

.formCellReset {
  padding: 0;
  --nutui-cell-divider-border-bottom: '0';
}

.nut-form {
  // CSS 自定义属性需要放在嵌套规则之前
  --nutui-form-item-label-font-size: 28px;
  --nutui-cell-title-color: #666;

  .nut-form-item .nut-form-item-labeltxt {
    position: relative;
    font-size: 28px;
  }
}
