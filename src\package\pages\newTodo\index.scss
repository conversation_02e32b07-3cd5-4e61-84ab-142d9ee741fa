.new-todo-container {
  --nutui-form-item-label-width: '120px';
  background-color: #f5f4f9;
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 36px 24px;

  .time-picker {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .nut-input-native {
    text-align: right !important;
  }

  .confirm-btn {
    margin-top: auto;
    margin-bottom: 20px * 2;
    font-size: 18px * 2;
  }
}


.reminder {
  .nut-cell-title {
    color: #666;
    font-size: 14px * 2;
  }

  .reminder-item-extra {

    display: flex;
    align-items: center;
    gap: 10px;

    text {
      color: #131313;
      font-size: 14px * 2;
      font-weight: 400;
    }
  }

  &-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    gap: 10px;

    // height: 46px * 2;
    flex-shrink: 0;
    border-radius: 8px * 2;
    background: #FFF;

    text {
      color: #F00;
      font-size: 16px * 2;
      font-weight: 400;
    }
  }
}

.reminder-type-select {
  --nutui-switch-active-background-color: #f00;
}