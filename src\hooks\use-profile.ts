import { getMemberInfo } from '@/serves/member';
import { GetMemberInfoRes } from '@/serves/member/interface';
import useUserInfoStore from '@/store/useUserInfoStore';
import useUserStore from '@/store/useUserStore';
import useRequestWithLifecycle, {
  UseRequestWithLifecycleOptions,
} from './use-request-with-lifecycle';

function useProfile(options?: UseRequestWithLifecycleOptions<GetMemberInfoRes, []>) {
  const accessToken = useUserStore((state) => state.accessToken);
  const {
    data: userInfo,
    refresh,
    ...rest
  } = useRequestWithLifecycle(getMemberInfo, {
    cacheKey: 'memberInfo',
    onSuccess: (res) => {
      useUserInfoStore.setState(res.data);
    },
    ready: !!accessToken,
    refreshOnShow: true,
    refreshDeps: [accessToken],
    ...options,
  });

  return {
    userInfo,
    refresh,
    ...rest,
  };
}

export default useProfile;
