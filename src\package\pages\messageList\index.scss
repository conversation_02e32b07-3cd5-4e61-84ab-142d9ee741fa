/* 
 * 消息列表页面样式
 * 整体采用flex布局，背景色为浅灰色，提供良好的视觉层次
 */
.message-list {
  padding: 28px 24px;
  display: flex;
  flex-direction: column;
  gap: 28px;
  background-color: #f5f4f9;
  width: 100%;
  height: 100%;

  /* 
   * 消息项样式
   * 使用grid布局，将内容分为三行：标题、内容、底部信息
   * 白色背景配合圆角设计，提供清晰的视觉边界
   */
  &-item {
    display: grid;
    gap: 28px;
    grid-template-rows: 1fr 1fr 60px;
    background-color: #fff;
    border-radius: 16px;
    padding: 24px 32px 14px 32px;

    /* 
     * 消息标题区域
     * 使用flex布局，实现状态点和标题的水平排列
     */
    &-title {
      display: flex;
      align-items: center;
      gap: 12px;

      /* 
       * 消息状态指示点
       * 使用圆形设计，红色表示未读状态
       */
      .dot {
        width: 10px * 2;
        height: 10px * 2;
        background-color: #f00;
        border-radius: 50%;
      }

      /* 
       * 消息标题文本
       * 使用深色字体，加粗显示，突出重要性
       */
      text {
        color: #131313;
        font-size: 16px * 2;
        font-weight: 500;
      }
    }

    /* 
     * 消息内容区域
     * 使用灰色字体，正常字重，提供良好的可读性
     */
    &-content {
      color: #666;
      font-size: 14px * 2;
      font-weight: 400;
    }

    /* 
     * 消息底部信息区域
     * 使用flex布局，两端对齐显示来源和时间
     * 顶部边框线分隔内容区域
     */
    &-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #9d9d9d;
      font-size: 12px * 2;
      font-weight: 400;
      border-top: 1px solid #f5f4f9;
    }
  }
}
