.bank-card {
  padding: 20px;
  height: 100%;
  width: 100%;
  background-color: #f5f4f9;

  display: flex;
  flex-direction: column;
  font-size: 24px;
  gap: 24px;

  // CSS 自定义属性需要放在嵌套规则之前
  --nutui-button-default-color: #f00;
  --nutui-button-default-border-color: #fff;
  --nutui-button-default-background-color: #fff;

  .bank-card-list {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  &-item {
    height: 100px * 2;
    flex-shrink: 0;
    border-radius: 8px;
    width: 100%;
    background: linear-gradient(180deg, #ff5358 0%, #f6343a 100%);

    display: flex;
    padding: 24px;
    // align-items: center;
    gap: 20px;

    &-icon {
      width: 32px * 2;
      height: 32px * 2;
      flex-shrink: 0;
      background-color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &-info {
      display: flex;
      flex-direction: column;
      gap: 20px;

      view:first-of-type {
        display: flex;
        gap: 4px;
        flex-direction: column;

        text:first-of-type {
          font-size: 16px * 2;
          font-weight: 500;
          color: #fff;
        }

        text:last-of-type {
          font-size: 12px * 2;
          color: #fff;
        }
      }

      .card-number {
        font-size: 22px * 2;
        color: #fff;
      }
    }
  }
}

.nut-swipe-right {
  padding-left: 8px * 2;
  height: 100%;
  button {
    height: 100%;
  }
}
