import { Text, View } from '@tarojs/components';
import { useLoad } from '@tarojs/taro';

import PageContainerTmp from '@/components/pageContainerTemp';
import SvgIcon from '@/pages/me/components/SvgIcon';
import { Button, Checkbox } from '@nutui/nutui-react-taro';
import { useState } from 'react';
import './index.scss';
const LogOut = () => {
  useLoad(() => {
    console.log('Page loaded.');
  });

  const [checked, setChecked] = useState(false);

  const handleChecked = () => {
    setChecked(!checked);
  };

  return (
    <PageContainerTmp title="注销账号" showMarks={false} showTabbar={false} isRet align="center">
      <View className="log-out-container">
        <View className="log-out-container-content">
          <View className="icon-box">
            <SvgIcon name="markSvg" size={50} />
          </View>
          <View className="text-box">
            注销账号是不可恢复的操作，注销后你的账号中的个人资料、案件、合同、待办任务等内容将无法被找回。操作前请确认以上信息均已妥善处理
          </View>
          <View className="log-out-card">
            <View className="desc">注销账号前，我们需要验证你的账号安全、财产清算完成等操作</View>
            <View className="list-box">
              <View className="list">账号处理安全状态</View>
              <View className="desc">
                最近两周内，该账号没有进行修改密码，修改绑定手机号等敏感操作且账号没被限制。
              </View>
            </View>
            <View className="list-box">
              <View className="list">账号财产已清算并结清</View>
              <View className="desc">该账号中的财产已经全部结清，没有冻结、退款中的订单。</View>
            </View>
            <View className="list-box">
              <View className="list">账号银行卡已全部解绑</View>
              <View className="desc">该账号上所有银行卡已全部解绑。不存在解绑中的银行卡。</View>
            </View>
            <View className="list">账号无任何未处理完成的纠纷</View>
          </View>

          <View className="log-out-footer">
            <View className="agree-box">
              <Checkbox checked={checked} onChange={handleChecked}></Checkbox>
              <View className="agree-box-text">
                我已阅读并同意
                <Text className="agree-box-text-link">《账号注销须知》</Text>
              </View>
            </View>
            <View className="log-out-footer-btn">
              <Button block type="primary" shape="round" disabled={!checked}>
                继续注销
              </Button>
            </View>
          </View>
        </View>
      </View>
    </PageContainerTmp>
  );
};

export default LogOut;
