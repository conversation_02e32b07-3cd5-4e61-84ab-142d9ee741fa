import SvgIcon from '@/pages/me/components/SvgIcon';
import { Add } from '@nutui/icons-react-taro';
import { Button, Swipe } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import './index.scss';

const bankCards: any[] = [
  // {
  //   id: 1,
  //   bankName: '中国银行',
  //   cardType: '信用卡',
  //   cardNumber: '**** **** **** **** 1234',
  //   icon: 'bank1',
  // },
  // {
  //   id: 2,
  //   bankName: '工商银行',
  //   cardType: '储蓄卡',
  //   cardNumber: '**** **** **** **** 5678',
  //   icon: 'bank2',
  // },
  // {
  //   id: 3,
  //   bankName: '建设银行',
  //   cardType: '信用卡',
  //   cardNumber: '**** **** **** **** 9012',
  //   icon: 'bank3',
  // },
];

const BankCard = () => {
  return (
    <View className="bank-card">
      <View className="bank-card-list">
        {bankCards?.map((item) => {
          return (
            <Swipe
              key={item.id}
              rightAction={
                <Button type="primary" shape="square">
                  解绑
                </Button>
              }
            >
              <View className="bank-card-item">
                <View className="bank-card-item-icon">
                  <SvgIcon name="bank1" size={20} />
                </View>
                <View className="bank-card-item-info">
                  <View>
                    <Text>{item.bankName}</Text>
                    <Text>{item.cardType}</Text>
                  </View>
                  <View>
                    <Text className="card-number">{item.cardNumber}</Text>
                  </View>
                </View>
              </View>
            </Swipe>
          );
        })}
      </View>
      <Button
        onClick={() => {
          Taro.navigateTo({ url: '/package/pages/bindBandCard/index' });
        }}
        icon={<Add color="#f00" />}
        className="bank-card-add"
      >
        添加银行卡
      </Button>
    </View>
  );
};

export default BankCard;
