@import 'taro-ui/dist/style/components/button.scss';
page {
  width: 100vw;
  min-height: 100vh;
  box-sizing: border-box;
  color: #333;
}

.wrapper {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/**index.scss**/
page {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

view,
text {
  box-sizing: border-box;
}

.scrollarea {
  flex: 1;
  overflow-y: scroll;
}

.container {
  width: 100%;
  height: 100%;
  overflow-y: scroll;

  .swiper {
    width: 100%;
    height: 50vw;
  }

  .swiperItem {
    width: 100%;
    height: 50vw;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .main {
    width: calc(100vw - 40px);
    height: 260px;
    position: relative;
    margin: 20px auto;
    z-index: 1;
    background-image: url('https://guanjia.hunantianxing.com/static/miniprogramImage/tb1.png');
    background-size: cover;
    & > image {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      z-index: -1;
    }

    .title {
      padding: 40px 0 0 26px;
      font-size: 52px;
      font-weight: bold;
      letter-spacing: normal;
      background: linear-gradient(180deg, #ffe8b8 63%, #ffe8b8 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }

    .testBut {
      width: 216px;
      height: 48px;
      line-height: 48px;
      text-align: center;
      background-color: #fff;
      border-radius: 24px;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #4141c7;
      font-size: 24px;
      margin-top: 24px;
      margin-left: 26px;
      font-weight: bold;
      image {
        width: 48px;
        height: 48px;
      }
    }

    .sortList {
      display: flex;
      align-items: center;
      color: #fff;
      font-size: 28px;
      margin-top: 24px;
      padding-left: 26px;
      .sortItem {
        display: flex;
        align-items: center;
        margin-right: 15px;
        text {
          width: 8px;
          height: 8px;
          background-color: #fff;
          border-radius: 50%;
          margin-right: 15px;
        }
      }
    }
  }

  .footer {
    width: 100vw;
    background: linear-gradient(295deg, #9048c9 26%, #3a3dc3 56%);
    padding: 0 20px;
    padding-bottom: 24px;

    .reportTitle {
      height: 80px;
      line-height: 80px;
      text-align: center;
      font-size: 28px;
      color: #fff;
      font-weight: 500;
    }

    .footerContainer {
      width: 100%;
      height: calc(100% - 80px);
      border-radius: 20px;
      overflow-y: scroll;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      font-size: 28px;
      font-weight: 400;
    }

    .introduceOne {
      width: 100%;

      .moduleBox {
        width: 100%;
        padding: 31px;
        background-color: #fff;
        border-radius: 20px;
      }

      .introduceTitle {
        display: flex;
        align-items: center;
        height: 80px;
        font-size: 32px;
        color: #222;
        font-weight: 500;

        image {
          display: inline-block;
          width: 48px;
          height: 48px;
          margin-right: 15px;
        }
      }

      .introduceContent {
        font-size: 28px;
        text-indent: 48px;
        margin-bottom: 20px;
      }

      .introduceContentImg {
        width: 100%;

        image {
          width: calc(100vw - 96px);
          height: calc((100vw - 96px) * 211 / 1449);
          margin-bottom: 20px;
        }
      }

      .introduceContentDetail {
        width: 100%;
        display: flex;
      }
    }
  }
}

.SCVTList {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 60px;

  .SCVTItem {
    flex-basis: calc((100vw - 168px) / 4);
    height: calc((100vw - 168px) / 4);
    margin-bottom: 24px;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-size: 24px;
    color: #fff;
    border-radius: 20px;
    overflow: hidden;

    text {
      padding-left: 4px;
      font-weight: bold;
    }

    view {
      text-align: center;
    }

    image {
      width: 84px;
      height: 84px;
      position: absolute;
      top: calc(50% - 42px);
      left: calc(50% - 42px);
      z-index: -1;
    }
  }
}

.introduceContentBox {
  width: 100%;

  .introduceContentItem {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    text-align: center;
    margin-bottom: 20px;

    & > view:nth-of-type(1) {
      flex: 1;
      height: 160px;
      background: linear-gradient(90deg, #ffffff 0%, #feeded 100%);
      padding-top: 10px;
      text {
        font-size: 26px;
        color: #ee5253;
      }

      view {
        padding-right: 90px;
        font-size: 22px;
        text-align: left;
        margin-top: 10px;
      }
    }

    & > view:nth-of-type(2) {
      flex: 1;
      height: 160px;
      background: linear-gradient(270deg, #ffffff 0%, #f4f0ff 95%);
      padding-top: 10px;

      text {
        font-size: 26px;
        color: #ee5253;
      }

      view {
        padding-left: 90px;
        font-size: 22px;
        text-align: left;
        margin-top: 10px;
      }
    }

    & > view:nth-of-type(3) {
      position: absolute;
      width: 160px;
      height: 100px;
      border-radius: 10px;
      opacity: 1;
      background: linear-gradient(90deg, #ee5253 0%, #453ec4 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28px;
      color: #fff;
    }
  }
}

.tagBox {
  display: flex;
  height: 60px;
  font-size: 30px;
  padding: 0 10px;
  align-items: center;
  justify-content: center;
  color: #222;
  margin-bottom: 20px;
  position: relative;

  view {
    position: absolute;
    width: 70px;
    height: 10px;
    bottom: 10px;
    background: linear-gradient(271deg, #9048c9 2%, #3a3dc3 102%);
  }
}

.pBox {
  font-size: 28px;
  display: flex;

  view {
    min-width: 220px;
    color: #222;
    font-size: 28px;
  }

  text {
    flex: 1;
    color: #666;
  }
}

.testResult {
  & > view {
    display: flex;
    align-items: center;
    font-size: 24px;
    margin-bottom: 20px;
    border-radius: 10px;
    overflow: hidden;
    background: linear-gradient(
      270deg,
      #ffffff 18%,
      rgba(195, 178, 232, 0.6314) 97%,
      rgba(109, 67, 198, 0.102) 97%
    );
    border-radius: 35px;

    & > text:first-of-type {
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      background: linear-gradient(274deg, #9048c9 4%, #3a3dc3 98%);
      box-sizing: border-box;
      border: 3px solid #ffffff;
      border-radius: 50%;
    }

    & > text:last-of-type {
      flex: 1;
      height: 86px;
      display: flex;
      align-items: center;
      padding-left: 15px;
    }
  }
}

.connectBox {
  width: 100%;
  margin: 0 auto;
  border-radius: 20px;
  padding: 30px;
  background: #f6f7f8;
  text-align: center;
}

.connectUs {
  margin-bottom: 20px;

  & > view {
    &:first-of-type {
      color: #222222;
      font-size: 28px;
      margin-bottom: 10px;

      image {
        width: 28px;
        height: 28px;
        margin-right: 10px;
      }
    }
  }

  .Qrcode {
    display: flex;
    align-items: center;
    justify-content: space-around;

    & > view {
      width: 25vw;
      height: 25vw;
      padding: 10px;
      background-color: #fff;
      border-radius: 20px;

      image {
        width: 100%;
        height: 100%;
      }
    }
  }

  .QrcodeInfo {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    justify-content: space-around;
    font-size: 24px;
    margin-top: 33px;
    color: #707070;
  }
}

.connectPhone {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  justify-content: center;

  text {
    color: #ee5253;
    font-size: 28px;
    margin-left: 20px;
    font-weight: bold;
    text-align: center;
  }
}

.Privacy {
  text-indent: 48px;
  margin-bottom: 30px;
  text-decoration: underline;
  text {
    font-size: 28px;
    color: #ee5253;
  }
}

.mb {
  margin-bottom: 60px !important;
}

.mb20 {
  margin-bottom: 20px !important;
}

.color888888 {
  color: #888888;
}

.moreBut {
  width: 500px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 35px;
  opacity: 1;
  background: #ffffff;
  box-sizing: border-box;
  border: 1px solid #ee5253;
  font-size: 28px;
  font-weight: normal;
  letter-spacing: 0em;
  color: #ee5253;
  margin: 0 auto;
}

.moreContent {
  max-height: 0;
  height: auto;
  overflow: hidden;
  transition: max-height 3ms ease-in-out;
  margin-bottom: 20px;
}

.showMoreContent {
  max-height: 1000vh;
  transition: max-height 1s ease-in-out;
}
