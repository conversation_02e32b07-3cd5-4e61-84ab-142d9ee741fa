/**
 * 会员卡权益相关接口类型定义
 */

/**
 * 会员卡权益查询请求参数
 */
export interface AppCardBenefitDTO {
  /**
   * 类型：0-可绑定卡，1-可升级卡
   */
  type: number;
  /**
   * 会员卡ID，当type=1时必传
   */
  cardId?: number | string;
}

/**
 * 会员卡信息
 */
export interface MemberCardDO {
  createTime?: string;
  updateTime?: string;
  creator?: string;
  updater?: string;
  deleted?: boolean;
  /**
   * 主键ID
   */
  id: number;
  /**
   * 卡名称
   */
  cardName: string;
  /**
   * 卡图片
   */
  cardImage?: string;
  /**
   * 卡口号图片
   */
  cardSloganImage?: string;
  /**
   * 小图标
   */
  smallIcon?: string;
  /**
   * 周期
   */
  period?: number;
  /**
   * 周期类型：year-年, month-月, quarter-季度, day-天, week-周, longTerm-长期
   */
  periodType?: string;
  /**
   * 会员身份
   */
  memberIdentity?: string;
  /**
   * 会员费
   */
  memberFee?: number;
  /**
   * 开通条件说明
   */
  activationConditionDescription?: string;
  /**
   * 结束条件说明
   */
  endConditionDescription?: string;
  /**
   * 是否可初次申请：0-是，1-否
   */
  initialApplicationStatus?: number;
  /**
   * 拥有人数
   */
  memberCount?: number;
  /**
   * 可升级别(卡编码，逗号分隔)
   */
  upgradableLevel?: string;
  /**
   * 可降级别(卡编码，逗号分隔)
   */
  downgradableLevel?: string;
  /**
   * 状态：0-正常，1-禁用
   */
  status: number;
  /**
   * 备注说明
   */
  remark?: string;
  /**
   * 编码
   */
  code?: string;
}

/**
 * 会员卡权益信息
 */
export interface CardBenefitRespVO {
  /**
   * 权益ID
   */
  id?: number;
  /**
   * 权益类型
   */
  benefitType?: string;
  /**
   * 权益名称
   */
  benefitName?: string;
  /**
   * 权益图标
   */
  benefitIcon?: string;
  /**
   * 规则
   */
  rule?: string;
  /**
   * 权益值
   */
  benefitValue?: string;
  /**
   * 收益是否可以提现
   */
  canWithdraw?: number;
  /**
   * 权益说明
   */
  benefitDescription?: string;
  /**
   * 状态
   */
  status?: number;
}

/**
 * 用户App - 会员卡权限
 */
export interface AppCardBenefitsVO {
  /**
   * 会员卡信息
   */
  memberCardDO?: MemberCardDO;
  /**
   * 会员权益列表
   */
  benefitList?: CardBenefitRespVO[];
}

/**
 * 通用响应结果
 */
export interface CommonResultListAppCardBenefitsVO {
  code: number;
  data: AppCardBenefitsVO[];
  msg: string;
}
