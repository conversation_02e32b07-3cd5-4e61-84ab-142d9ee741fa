import { serveIp } from '@/serves/config';

/**
 * 直接导入的SVG资源
 */
// 我的-我的佣金
import myCommissionSafe from '@/images/my-commissions/safe.svg';

// 记事本相关图标
import me_accounting from '@/images/me/accounting.svg';
import entry_icon from '@/images/me/entry.svg';
import notebookDel from '@/images/notebook/del.svg';
import notebookImage from '@/images/notebook/image.svg';
import notebookList from '@/images/notebook/list.svg';
import notebookSave from '@/images/notebook/save.svg';
import notebookShare from '@/images/notebook/share.svg';
import notebookUnordered from '@/images/notebook/unordered .svg';

/**
 * 通过URL引用的SVG资源
 */

// ===== 我的页面资源 =====
const AvatarImage = serveIp.ImageUrl + 'me/avatar.png';
const arrowRight = serveIp.ImageUrl + 'me/arrow-right.svg';
const arrowRightSmall = serveIp.ImageUrl + 'me/arrow-right-small.svg';
const cardFH = serveIp.ImageUrl + 'me/card_fh.svg';
const cardNoSM = serveIp.ImageUrl + 'me/card_no_sm.svg';
const cardSM = serveIp.ImageUrl + 'me/card_sm.png';
const fhCard = serveIp.ImageUrl + 'me/banner_fh.svg';
const icon_xf = serveIp.ImageUrl + 'me/icon_xf.svg';
const banner1 = serveIp.ImageUrl + 'me/ty-card.png'; // 会员服务

// 我的页面列表图标
const list1 = serveIp.ImageUrl + 'me/list_1.svg';
const list2 = serveIp.ImageUrl + 'me/list_2.svg';
const list3 = serveIp.ImageUrl + 'me/list_3.svg';
const list4 = serveIp.ImageUrl + 'me/list_4.svg';
const list5 = serveIp.ImageUrl + 'me/list_5.svg';
const list6 = serveIp.ImageUrl + 'me/list_6.svg';
const list7 = serveIp.ImageUrl + 'me/list_7.svg';
const list8 = serveIp.ImageUrl + 'me/list_8.svg';
const list_gzrw = serveIp.ImageUrl + 'me/list_gzrw.svg';
const list_hzmx = serveIp.ImageUrl + 'me/list_hzmx.svg';
const list_wdzq = serveIp.ImageUrl + 'me/list_wdzq.svg';
const list_wdzw = serveIp.ImageUrl + 'me/list_wdzw.svg';

// 升级会员icon
import upgradeIcon from '@/images/me/upgrade-member.png';

// ===== 测评页面资源 =====
const icon_cp1 = serveIp.ImageUrl + 'evaluation/icon_cp1.svg';
const icon_cp2 = serveIp.ImageUrl + 'evaluation/icon_cp2.svg';
const icon_cp3 = serveIp.ImageUrl + 'evaluation/icon_cp3.svg';
const icon_cp4 = serveIp.ImageUrl + 'evaluation/icon_cp4.svg';
const icon_cp5 = serveIp.ImageUrl + 'evaluation/icon_cp5.svg';

// ===== 计划页面资源 =====
const CoinPng = serveIp.ImageUrl + 'plan/coin.png';
const bankCard = serveIp.ImageUrl + 'plan/bank-card.svg';
// const FilterIcon = serveIp.ImageUrl + 'plan/filter.svg';

// ===== 设置页面资源 =====
const about = serveIp.ImageUrl + 'settings/about.svg';
const fp = serveIp.ImageUrl + 'settings/fp.svg';
const lx = serveIp.ImageUrl + 'settings/lx.svg';
const me = serveIp.ImageUrl + 'settings/me.svg';
const sm = serveIp.ImageUrl + 'settings/sm.svg';
const yh = serveIp.ImageUrl + 'settings/yh.svg';
const zx = serveIp.ImageUrl + 'settings/zx.svg';

import bindMallAccount from '@/images/me/settings/bind-mall-account.svg';
// const bindMallAccount = serveIp.ImageUrl + 'settings/bind-mall-account.svg';

// ===== 银行卡页面资源 =====
const bank1 = serveIp.ImageUrl + 'bankCard/<EMAIL>';
const descSvg = serveIp.ImageUrl + 'bankCard/desc.svg';

// ===== 会员服务页面资源 =====
const titleLeft = serveIp.ImageUrl + 'vipService/title-left.svg';
const titleRight = serveIp.ImageUrl + 'vipService/title-right.svg';

// ===== 关于我们页面资源 =====
const aboutTx = serveIp.ImageUrl + 'aboutTx/icon.svg';

// ===== 反馈页面资源 =====
const historyIcon = serveIp.ImageUrl + 'feedback/history-icon.png';
const feedbackBg = serveIp.ImageUrl + 'feedback/<EMAIL>';

// ===== 首页资源 =====
const note = serveIp.ImageUrl + 'home/note.svg'; // 记事本
const banner = serveIp.ImageUrl + '<EMAIL>';
const fxzq = serveIp.ImageUrl + '<EMAIL>';
const hdzx = serveIp.ImageUrl + '<EMAIL>';
const rwzd = serveIp.ImageUrl + '<EMAIL>';

// ===== 底部导航栏资源 =====
const jh = serveIp.ImageUrl + 'tabbar/icon_jh1.png';
const jh2 = serveIp.ImageUrl + 'tabbar/icon_jh2.png';
const cx = serveIp.ImageUrl + 'tabbar/icon_pc1.png';
const cx2 = serveIp.ImageUrl + 'tabbar/icon_pc2.png';
const sy = serveIp.ImageUrl + 'tabbar/icon_sy1.png';
const sy2 = serveIp.ImageUrl + 'tabbar/icon_sy2.png';
const wd = serveIp.ImageUrl + 'tabbar/icon_wd1.png';
const wd2 = serveIp.ImageUrl + 'tabbar/icon_wd2.png';

// ===== 结果模态框资源 =====
const successSvg = serveIp.ImageUrl + 'realName/success.svg';
const failSvg = serveIp.ImageUrl + 'realName/fail.svg';
const loadingSvg = serveIp.ImageUrl + 'realName/loading.svg';

// ===== 我的-账管顾问 =====
import icon_dz from '@/images/my-commissions/icon_dz.svg';
import icon_sfzh from '@/images/my-commissions/icon_sfzh.svg';
import icon_TEL from '@/images/my-commissions/icon_TEL.svg';
// 注销账号页面资源
import markSvg from '@/images/logout/mark.svg';
import codeBg from '@/images/me/settings/code-bg.png'; // 兑换码背景图片
import redemptionCode from '@/images/me/settings/redemption-code.svg';
// const markSvg = serveIp.ImageUrl + 'logout/mark.svg';

/**
 * SVG资源映射表
 * 按照功能模块分组
 */
export const svgStore = {
  // ===== 关于我们 =====
  aboutTx,

  // ===== 我的页面 =====
  avatar: AvatarImage,
  'arrow-right': arrowRight,
  'arrow-right-small': arrowRightSmall,
  card_fh: cardFH,
  card_sm: cardSM,
  card_no_sm: cardNoSM,
  fhCard,
  icon_xf,
  entry_icon,

  // 我的页面列表图标
  list_1: list1,
  list_2: list2,
  list_3: list3,
  list_4: list4,
  list_5: list5,
  list_6: list6,
  list_7: list7,
  list_8: list8,
  list_gzrw,
  list_hzmx,
  list_wdzq,
  list_wdzw,
  me_accounting,
  upgradeIcon,

  // ===== 测评页面 =====
  icon_cp1,
  icon_cp2,
  icon_cp3,
  icon_cp4,
  icon_cp5,

  // ===== 计划页面 =====
  bankCard,
  coinPng: CoinPng,
  // filterIcon: FilterIcon,

  // ===== 设置页面 =====
  about,
  fp,
  lx,
  me,
  sm,
  yh,
  zx,
  bindMallAccount,
  'redemption-code': redemptionCode,
  'code-bg': codeBg, // 兑换码背景图片

  // ===== 银行卡页面 =====
  bank1,
  descSvg,

  // ===== 会员服务页面 =====
  titleLeft,
  titleRight,
  banner1,

  // ===== 反馈页面 =====
  historyIcon,
  feedbackBg,

  // ===== 首页资源 =====
  note,
  banner,
  fxzq,
  hdzx,
  rwzd,

  // ===== 底部导航栏 =====
  jh,
  jh2,
  cx,
  cx2,
  sy,
  sy2,
  wd,
  wd2,

  // ===== 结果模态框 =====
  successSvg,
  failSvg,
  loadingSvg,

  // ===== 我的-我的佣金 =====
  myCommissionSafe,

  // ===== 记事本功能图标 =====
  notebookSave,
  notebookDel,
  notebookShare,
  notebookUnordered,
  notebookList,
  notebookImage,

  // ===== 我的-账管顾问 =====
  icon_dz,
  icon_sfzh,
  icon_TEL,

  // ===== 注销账号页面 =====
  markSvg,
};

export type SvgName = keyof typeof svgStore;

/**
 * 获取SVG文件路径
 * @param name SVG资源名称
 * @returns SVG资源路径
 */
export function getSvgPath(name: SvgName): string {
  return svgStore[name] || '';
}

/**
 * 首页轮播图资源
 */
export const homepageBanner = [
  `${serveIp.ImageUrl}homeBanner/banner合伙人体系1.png`,
  `${serveIp.ImageUrl}homeBanner/banner合伙人体系2.png`,
  `${serveIp.ImageUrl}homeBanner/banner合伙人体系3.png`,
  `${serveIp.ImageUrl}homeBanner/banner合伙人体系4.png`,
  `${serveIp.ImageUrl}homeBanner/banner合伙人体系5.png`,
  `${serveIp.ImageUrl}homeBanner/banner合伙人体系6.png`,
  `${serveIp.ImageUrl}homeBanner/banner合伙人体系7.png`,
  `${serveIp.ImageUrl}homeBanner/banner合伙人体系8.png`,
  `${serveIp.ImageUrl}homeBanner/banner合伙人体系9.png`,
  `${serveIp.ImageUrl}homeBanner/banner合伙人体系10.png`,
];
