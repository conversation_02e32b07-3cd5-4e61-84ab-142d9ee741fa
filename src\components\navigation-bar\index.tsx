import { ArrowLeft } from '@nutui/icons-react-taro';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useMemo } from 'react';
import { useMenuButtonBoundingClientRect, useSystemInfo } from 'taro-hooks';
import './index.scss';

/**
 * 导航栏组件
 * @param props 导航栏组件参数
 * @returns 导航栏组件
 */
const Index = ({
  isRet = false,
  title = '添星管家',
  align = 'start',
  transparent = false, // 新增透明背景选项
}: {
  isRet?: boolean;
  title?: string;
  align?: string;
  transparent?: boolean; // 是否使用透明背景
}) => {
  const rect = useMenuButtonBoundingClientRect();
  const { platform, statusBarHeight } = useSystemInfo();
  const NavHeight = useMemo(() => {
    return (rect.top - (statusBarHeight || 20)) * 2 + rect.height;
  }, [platform]);
  return (
    <View
      style={{
        height: NavHeight + 'px',
        paddingTop: statusBarHeight + 'px',
        justifyContent: align,
        backgroundColor: transparent ? 'transparent' : '', // 根据transparent参数决定背景色
      }}
      className={`custom-nav ${transparent ? 'transparent' : ''}`}
    >
      {isRet ? (
        <ArrowLeft
          onClick={() => Taro.navigateBack()}
          className="Ret"
          style={isRet ? { marginRight: 16 } : {}}
        />
      ) : (
        <></>
      )}
      <Text>{title}</Text>
    </View>
  );
};

export default Index;
